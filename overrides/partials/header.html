{#-
  This file was automatically generated - do not edit
-#}
{% set class = "md-header" %}
{% if "navigation.tabs.sticky" in features %}
  {% set class = class ~ " md-header--lifted" %}
{% endif %}
<header class="{{ class }}" data-md-component="header">
  <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "fkujvevd2q");
  </script>
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-1438193177826254"
  crossorigin="anonymous"></script>
  <nav class="md-header__inner md-grid" aria-label="{{ lang.t('header') }}">
    <a href="{{ config.extra.homepage | d(nav.homepage.url, true) | url }}" title="{{ config.site_name | e }}" class="md-header__button md-logo" aria-label="{{ config.site_name }}" data-md-component="logo">
      {% include "partials/logo.html" %}
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      {% include ".icons/material/menu" ~ ".svg" %}
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            {{ config.site_name }}
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            {% if page.meta and page.meta.title %}
              {{ page.meta.title }}
            {% else %}
              {{ page.title }}
            {% endif %}
          </span>
        </div>
      </div>
    </div>
    {% if not config.theme.palette is mapping %}
      <form class="md-header__option" data-md-component="palette">
        {% for option in config.theme.palette %}
          {% set scheme = option.scheme | d("default", true) %}
          <input class="md-option" data-md-color-media="{{ option.media }}" data-md-color-scheme="{{ scheme | replace(' ', '-') }}" data-md-color-primary="{{ option.primary | replace(' ', '-') }}" data-md-color-accent="{{ option.accent | replace(' ', '-') }}" {% if option.toggle %} aria-label="{{ option.toggle.name }}" {% else %} aria-hidden="true" {% endif %} type="radio" name="__palette" id="__palette_{{ loop.index0 }}">
          {% if option.toggle %}
            <label class="md-header__button md-icon" title="{{ option.toggle.name }}" for="__palette_{{ loop.index % loop.length }}" hidden>
              {% include ".icons/" ~ option.toggle.icon ~ ".svg" %}
            </label>
          {% endif %}
        {% endfor %}
      </form>
    {% endif %}
    {% if not config.theme.palette is mapping %}
      {% include "partials/javascripts/palette.html" %}
    {% endif %}
    {% if config.extra.alternate %}
      <div class="md-header__option">
        <div class="md-select">
          {% set icon = config.theme.icon.alternate or "material/translate" %}
          <button class="md-header__button md-icon" aria-label="{{ lang.t('select.language') }}">
            {% include ".icons/" ~ icon ~ ".svg" %}
          </button>
          <div class="md-select__inner">
            <ul class="md-select__list">
              {% for alt in config.extra.alternate %}
                <li class="md-select__item">
                  <a href="{{ alt.link | url }}" hreflang="{{ alt.lang }}" class="md-select__link">
                    {{ alt.name }}
                  </a>
                </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    {% endif %}
    {% if "material/search" in config.plugins %}
      <label class="md-header__button md-icon" for="__search">
        {% include ".icons/material/magnify.svg" %}
      </label>
      {% include "partials/search.html" %}
    {% endif %}
    {% if config.repo_url %}
      <div class="md-header__source">
        {% include "partials/source.html" %}
      </div>
    {% endif %}
  </nav>
  {% if "navigation.tabs.sticky" in features %}
    {% if "navigation.tabs" in features %}
      {% include "partials/tabs.html" %}
    {% endif %}
  {% endif %}
</header>
