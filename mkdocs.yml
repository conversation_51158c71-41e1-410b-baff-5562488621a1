site_name: "<PERSON>'s Blog"
site_description: "Coding, AI, and Life"
site_author: <PERSON>
site_url: https://fengchao.pro
# Repository
# repo_name: '<PERSON>'
# repo_url: 'https://github.com/jeremy-feng/blog'
edit_uri: "https://github.com/jeremy-feng/blog/edit/master/docs/"
copyright: <p>Copyright &copy; 2022 - <script>document.write(new Date().getFullYear())</script> <PERSON></p>

nav:
  - 首页: index.md
  - 博客:
      - blog/index.md
  - 代码:
      - code/index.md
      - Pandas 数据框: code/pandas.md
      - NumPy 数组: code/numpy.md
      - List 列表: code/list.md
      - Loop 循环: code/loop.md
      - Plot 绘图: code/plot.md
      - Print 打印: code/print.md
      - Machine Learning 机器学习: code/machine-learning.md
      - SQL 数据库: code/sql.md
      - R 语言: code/r.md
      - Others 其他: code/others.md
  - 效率:
      - workflow/index.md
      - 量化研究: workflow/quant.md
      - 写作: workflow/writing.md
      - 工具: workflow/tools.md
  - 标签: blog/tags.md
  - 问答: https://v.fengchao.pro
  - 关于:
      - 关于: about/index.md
      - About: about/en.md

theme:
  name: "material"
  favicon: blog/avatar.jpg
  language: "zh"
  custom_dir: overrides
  font: false
  palette:
    # Palette toggle for automatic mode
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: 跟随系统
    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/brightness-7
        name: 日间模式
    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/brightness-4
        name: 夜间模式
  icon:
    logo: fontawesome/solid/house
    admonition:
      note: fontawesome/solid/note-sticky
      abstract: fontawesome/solid/book
      info: fontawesome/solid/circle-info
      tip: fontawesome/solid/bullhorn
      success: fontawesome/solid/check
      question: fontawesome/solid/circle-question
      warning: fontawesome/solid/triangle-exclamation
      failure: fontawesome/solid/bomb
      danger: fontawesome/solid/skull
      bug: fontawesome/solid/robot
      example: fontawesome/solid/flask
      quote: fontawesome/solid/quote-left
    tag:
      cs: fontawesome/solid/computer
      database: fontawesome/solid/database
      dl: fontawesome/solid/d
      indie-dev: fontawesome/brands/dev
      latex: material/format-text
      leetcode: simple/leetcode
      life: fontawesome/solid/heart
      linux: fontawesome/brands/linux
      ml: fontawesome/solid/m
      or: material/axis-x-y-arrow-lock
      python: fontawesome/brands/python
      rust: fontawesome/brands/rust
      pytorch: simple/pytorch
      quant: material/alpha
      stats: material/calculator-variant
      visulization: material/chart-bar
  features:
    # - navigation.instant # 即时加载
    # - navigation.sections # 左侧导航栏分章节，而不是折叠
    - navigation.tabs # 将导航栏置于顶部
    - navigation.top # 快速返回顶部
    - navigation.indexes # 首页导航
    - navigation.footer # 页脚导航，即上一页和下一页
    - navigation.tracking # url 随着滚动条的滚动而改变
    - search.share # 搜索结果分享
    - search.highlight # 搜索结果高亮
    # - search.suggest # 搜索结果自动补全，建议关闭，因为搜索框中输入的字体与建议的文本字体不一致，导致很难看
    - content.action.edit # 编辑按钮
    - content.code.copy # 代码块复制
    - content.code.annotate # 代码块中添加可点击注释
    # - content.code.select # 选中几行代码
    - content.tabs.link # 代码块链接，对相同代码块的不同语言版本进行统一切换
    - toc.follow # 目录跟随当前页面滚动
    - header.autohide # 自动隐藏页眉
    - content.tooltips # 更好看的工具提示框
    - announce.dismiss # 顶部公告栏标记为已读

plugins:
  - search:
      separator: '[\s\u200b\-]'
  - meta
  - blog:
      blog_dir: ./blog
      blog_toc: true
      post_url_format: "{slug}"
      post_readtime_words_per_minute: 265
      archive_name: 归档
      archive_date_format: "yyyy 年 M 月"
      archive_url_date_format: yyyy/M
      archive_url_format: "{date}"
      categories_slugify:
        !!python/object/apply:pymdownx.slugs.slugify # 使锚点链接更可读
        kwds:
          case: lower
      categories_toc: true
      pagination_per_page: 10
      pagination_url_format: "{page}"
      pagination_template: "$link_first $link_previous ~2~ $link_next $link_last"
  - typeset # 侧边栏等位置可以加入富文本
  # - optimize
  # - i18n:
  #     default_language: zh
  #     languages:
  #       zh: 中文
  #       en: English
  #     nav_translations:
  #       en:
  #         首页: Home
  #         博客: Blog
  #         标签: Tags
  #         关于: About
  - tags:
      tags_file: ./blog/tags.md
      tags_slugify:
        !!python/object/apply:pymdownx.slugs.slugify # 使锚点链接更可读
        kwds:
          case: lower
      # tags_compare_reverse: true
  - encryptcontent:
      title_prefix: ""
      summary: "本页内容已加密。This page is encrypted."
      placeholder: "请输入密码"
      decryption_failure_message: "密码错误"
      encryption_info_message: "需要密码"
      encrypted_something:
        mkdocs-encrypted-toc: [nav, id]
  - privacy:
      external_assets: report # 不自动下载外部资源
      external_links_attr_map:
        target: _blank # 自动在新标签页打卡外部链接，并加上 rel="noopener"，保护用户隐私
  - git-revision-date-localized:
      type: custom
      custom_format: "%Y 年 %-m 月 %d 日"
      timezone: Asia/Shanghai
      locale: zh
      fallback_to_build_date: true
      exclude:
          - index.md
          - blog/tags.md

extra:
  generator: false # 不显示左下角的生成器，Made with Material for MkDocs Insiders
  social:
    - icon: simple/linkedin
      link: https://www.linkedin.com/in/chao-jeremy-feng/
      name: LinkedIn
    - icon: fontawesome/solid/location-dot
      link: https://map.qq.com/poi/?sm=4310240715305234267
      name: Location
  search:
    lang:
      - zh-CN
    tokenizer: '[\s\-\.]+'
  tags:
    Computer Science: cs
    数据库: database
    深度学习: dl
    IndieDev: indie-dev
    LaTeX: latex
    LeetCode: leetcode
    生活: life
    Linux: linux
    机器学习: ml
    运筹学: or
    Python: python
    Rust: rust
    PyTorch: pytorch
    量化研究: quant
    统计: stats
    数据可视化: visulization

  # alternate:
  #   - name: 中文
  #     link: /
  #     lang: zh
  #   - name: English
  #     link: en/
  #     lang: en

extra_css:
  - stylesheets/extra.css

extra_javascript:
  - javascripts/mathjax.js
  - "https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML" # 数学公式支持

markdown_extensions:
  # Python Markdown
  - abbr # 缩写支持
  - admonition # 注解块支持
  - attr_list # 属性列表支持，给 markdown 元素添加属性，使其具有更多的功能，例如关于页的"联系"按钮
  - pymdownx.snippets # 代码片段支持
  - def_list # 定义列表支持
  - footnotes # 脚注支持
  - md_in_html
  - toc:
      permalink: true # 为标题添加锚点
      permalink_title: 定位到这
      slugify: !!python/object/apply:pymdownx.slugs.slugify # 使锚点链接更可读
        kwds:
          case: lower

  # Python Markdown Extensions
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.critic # 删除、插入、高亮文本支持
  - pymdownx.caret # 更多文本格式
  - pymdownx.tilde # 更多文本格式
  - pymdownx.mark # 更多文本格式
  # - pymdownx.betterem:
  #     smart_enable: all # 好像没啥用，反而导致加粗和斜体的语法失效
  - pymdownx.details # 详情块支持，可以设置默认折叠或展开
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.highlight: # 代码高亮支持
      anchor_linenums: true # 代码块自动添加锚点，用于复制代码
      auto_title: true # 代码块自动添加标题，用于标明代码语言，例如 Python
      linenums: true # 代码块自动添加行号
      linenums_style: pymdownx-inline # 代码行号样式，复制时不会复制行号
  - pymdownx.inlinehilite # 行内代码高亮支持，一般行内代码是不能高亮的，但是这个插件可以
  - pymdownx.keys # 渲染键盘快捷键
  - pymdownx.smartsymbols # 智能符号支持，例如将 (c) 渲染为 ©
  - pymdownx.superfences # 代码块支持
  - pymdownx.tabbed: # 选项卡支持
      alternate_style: true # 选项卡样式，只能填 true
      slugify: !!python/object/apply:pymdownx.slugs.slugify # 使锚点链接更可读
        kwds:
          case: lower
  - pymdownx.tasklist: # 任务清单支持
      custom_checkbox: true # 自定义任务清单样式，使其更美观
