---
hide:
  - navigation
  - toc
---

## 导航

<div class="grid cards" markdown>

- [:fontawesome-solid-blog:{ .lg .middle } **Blog 博客** 学习笔记与日常生活](./blog/)
- [:fontawesome-solid-laptop-code:{ .lg .middle } **Code 代码** 常用的代码片段](./code/)
- [:octicons-workflow-16:{ .lg .middle } **Workflow 效率** 常用的工作流](./workflow/)
- [:octicons-tag-16:{ .lg .middle } **Tags 标签** 分类查看历史文章](./blog/tags/)
- [:simple-openai:{ .lg .middle } **Chat 问答** 强大的问答工具](https://v.fengchao.pro)
- [:fontawesome-solid-hands-clapping:{ .lg .middle } **About 关于** 关于我和本站](./about/)

</div>

## 精选

<div class="grid cards" markdown>

-	[![master-graduation](index-image/master-graduation.png){ loading=lazy }](/blog/master-graduation/)
			
	
	<p><a class="md-tag" href="/blog/tags/#生活">生活</a></p>
	
	---
	
	[告别学生时代](/blog/master-graduation/)
	
	感恩所有的遇见。
	
-	[![predict-james](index-image/predict-james.png){ loading=lazy }](/blog/bert-qa/)
			
	<p><a class="md-tag" href="/blog/tags/#深度学习">深度学习</a><a class="md-tag" href="/blog/tags/#pytorch">PyTorch</a></p>
	
	---
	
	[基于 Bert 的中文问答机器人](/blog/bert-qa/)
	
	基于 Bert 的中文分词和问答的预训练模型，利用 10, 000 余条中文问答数据进行微调，构建了能够理解中文段落并回答相关问题的问答机器人。用自定义的数据进行测试，模型的效果基本能达到“正确回答小学三年级以下的阅读理解题”的水平。

-	[![ols](index-image/ols.png){ loading=lazy }](/blog/ols-estimator/)
			
	
	<p><a class="md-tag" href="/blog/tags/#统计">统计</a><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a><a class="md-tag" href="/blog/tags/#量化研究">量化研究</a></p>
	
	---
	
	线性回归 $Y=X \widehat{\beta}+e$
	
	- [线性回归的普通最小二乘估计](/blog/ols-estimator/)
	
	- [普通最小二乘估计的无偏性和一致性](/blog/ols-estimator-unbiasedness-and-consistency/)
	
	- [普通最小二乘估计的方差与高斯 - 马尔可夫定理](/blog/ols-estimator-variance-and-gauss-markov-theorem/)
	
	- [普通最小二乘估计的假设](/blog/ols-estimator-assumptions/)
	
	- [极大似然估计与最小均方误差的等价性](/blog/maximum-likelihood-and-least-square/)
	
-	[![alphanet](index-image/alphanet.png){ loading=lazy }](/blog/alphanet/)
			
	<p><a class="md-tag" href="/blog/tags/#深度学习">深度学习</a><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a><a class="md-tag" href="/blog/tags/#pytorch">PyTorch</a></p>
	
	---
	
	[AlphaNet——基于深度学习的量价因子挖掘](/blog/alphanet/)
	
	借鉴卷积神经网络的思想，通过自定义运算符函数，构造类似卷积层的特征提取层。结合批标准化层、池化层、全连接层，搭建 AlphaNet-V1，实现从量价数据到收益率预测的自动挖掘。
	
	[AlphaNet-V3——调整网络结构和预测目标](/blog/alphanet-v3/)
	
	在 AlphaNet-V1 加入多步长的特征提取层，将池化层替换为门控循环单元（GRU），并尝试预测收益率和超额收益的方向。最后将随机森林模型作为 baseline 进行比较。

-	[![shap-value](index-image/shap-value.png){ loading=lazy }](/blog/xgboost-spotify-track-popularity-and-recommendation/)
			
	[![shap-summary-plot](index-image/shap-summary-plot.png){ loading=lazy }](/blog/xgboost-spotify-track-popularity-and-recommendation/)
	
	<p><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a></p>
	
	---
	
	[基于 XGBoost 的音乐流行度预测与推荐](/blog/xgboost-spotify-track-popularity-and-recommendation/)
	
	基于 Spotify 中的 30 万余条音乐数据与 10 万余条歌手数据，分析并构造了音乐流派、音乐语言等特征，与歌手热度、音乐发行时间等特征共同建模预测音乐流行度，使用 XGBoost 和 SHAP 评估各特征的重要性及其对预测结果的影响方向与大小，并构建了基于内容余弦相似度的音乐推荐系统。

-	[![logistic-regression-lasso-roc](index-image/logistic-regression-lasso-roc.svg){ loading=lazy }](/blog/assets/logistic-regression-lasso.pdf)
			
	<p><a class="md-tag" href="/blog/tags/#统计">统计</a><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a><a class="md-tag" href="/blog/tags/#python">Python</a></p>
	
	---
	
	[梯度下降和 MCMC 实现逻辑回归的 LASSO 形式](/blog/assets/logistic-regression-lasso.pdf)
	
	使用梯度下降法、随机梯度下降法、坐标下降法和基于贝叶斯后验的 MCMC 采样法实现带有 L1 惩罚项的逻辑回归，并在银行客户流失数据集上进行实证检验。

-	[![facial-emotion-recognition](index-image/facial-emotion-recognition.png){ loading=lazy }](/blog/cnn-for-facial-expression-recognition/)
			
	<p><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a><a class="md-tag" href="/blog/tags/#深度学习">深度学习</a></p>
	
	---
	
	[卷积神经网络 CNN 对 fer2013 数据集进行人脸表情识别](/blog/cnn-for-facial-expression-recognition/)
	
	使用 Keras 构建卷积神经网络，对 Block 数量、卷积核大小、Dropout rate 进行参数调优，使用数据增强方法生成模型数据缓解过拟合问题。最优模型在测试集上的分类准确率、精确率和召回率均为 63%，比基准模型的分类效果提高了约 10%。

-	[![decision-tree-id3](index-image/decision-tree-id3.png){ loading=lazy }](/blog/decision-tree-id3/)
			
	<p><a class="md-tag" href="/blog/tags/#机器学习">机器学习</a></p>
	
	---
	
	[手写基于 ID3 算法的决策树模型](/blog/decision-tree-id3/)
	
	不借助现成的机器学习框架，使用 NumPy 实现基于 ID3 算法的决策树模型。

-	[![graduation](index-image/graduation.jpg){ loading=lazy }](/blog/graduation/)
			
	
	<p><a class="md-tag" href="/blog/tags/#生活">生活</a></p>
	
	---
	
	[我们毕业了](/blog/graduation/)
	
	珍贵的照片和珍贵的人。

</div>
