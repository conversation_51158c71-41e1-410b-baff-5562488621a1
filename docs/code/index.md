---
title: 代码
comment: false
---

许多代码在学习和工作中经常使用，将它们记录并整理于此，方便查找与温习。

<div class="grid cards" markdown>

-   :simple-pandas:{ .lg .middle } **Pandas 数据框**

	---

	表格数据的增删查改、分组统计、多重索引、日期格式等。

	[:octicons-arrow-right-24: 查看](./pandas/)

-	:simple-numpy:{ .lg .middle } **NumPy 数组**

	---

	向量、矩阵的创建、切片、排序等。

	[:octicons-arrow-right-24: 查看](./numpy/)

-	:material-code-brackets:{ .lg .middle } **List 列表**

	---

	列表的筛选、添加、拼接、展平等。

	[:octicons-arrow-right-24: 查看](./list/)

-	:material-circle-multiple:{ .lg .middle } **Loop 循环**

	---

	显示循环进度条、逐对循环、遍历路径等。

	[:octicons-arrow-right-24: 查看](./loop/)

-	:material-draw:{ .lg .middle } **Plot 绘图**

	---

	常用的绘图代码。

	[:octicons-arrow-right-24: 查看](./plot/)

-	:material-printer:{ .lg .middle } **Print 打印**

	---

	整齐地打印、将内容写入文件等。

	[:octicons-arrow-right-24: 查看](./print/)

-	:fontawesome-solid-m:{ .lg .middle } **Machine Learning 机器学习**

	---

	常用的机器学习代码。

	[:octicons-arrow-right-24: 查看](./machine-learning/)

-	:material-database:{ .lg .middle } **SQL 数据库**

	---

	常用的 SQL 代码。

	[:octicons-arrow-right-24: 查看](./sql/)

-	:simple-rstudio:{ .lg .middle } **R 语言**

	---

	常用的 R 语言代码。

	[:octicons-arrow-right-24: 查看](./r/)

-	:fontawesome-solid-gear:{ .lg .middle } **Others 其他**

	---

	字典与字符串的操作、Git 命令、Conda 命令、正则表达式等。

	[:octicons-arrow-right-24: 查看](./others/)
</div>
