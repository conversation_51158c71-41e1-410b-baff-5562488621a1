---
title: List 列表
icon: material/code-brackets
tags:
  - Python
---

# List 列表

## 导出到本地 TXT 文件

可以使用 Python 内置的文件操作函数 `open()` 和 `write()` 将列表中的每一个元素逐个写入新建的 TXT 文件中，每个元素占据一行。具体实现方法如下：

```python
# 定义一个数值列表
my_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

# 新建 TXT 文件并写入数值列表
with open("my_list.txt", "w") as f:
    for i in my_list:
        f.write(str(i) + "\n")
```

其中，`open('my_list.txt', 'w')` 打开一个名为 `'my_list.txt'` 的 TXT 文件，如果该文件已经存在，则会清空文件中的内容重写，如果该文件不存在则会新建该文件。`'w'` 表示写入模式。`for i in my_list:` 循环遍历数值列表中的每一个元素，`f.write(str(i) + '\n')` 将当前元素转换为字符串类型，并写入 TXT 文件中，`\n` 表示换行符，使得每个元素占据一行。`with` 语句用于自动关闭文件，避免文件操作过程中的错误。

## 判断列表是否为空

- 可以用 `len(list_var) == 0`；

- 也可以用 `not list_var`，更简洁高效：

```pycon
>>> a = []
>>> not a
True
>>> a.append(1)
>>> a
[1]
>>> not a
False
```

## 生成重复元素列表

- 例如，生成一个长度为 10、元素全为 1 的列表：`[1]*10`

## 生成二维列表，且避免浅拷贝

直接用

```python
[[0] * 10] * 3
```

会得到一个 3*10 的二维列表，但第一维的 3 个列表是一样的，修改其中一个，就会修改其他两个，这是浅拷贝导致的。

若想避免浅拷贝，可以用

```python
[[0 for i in range(10)] for j in range(3)]
```

参考：[https://zhuanlan.zhihu.com/p/88197389](https://zhuanlan.zhihu.com/p/88197389)

## List 去除重复项

```python
list(set(my_list))
```

当我们在 Python 中需要对一个列表进行排序时，可以使用 list.sort() 方法或 sorted(list) 函数。虽然这两种方法都能满足我们的要求，但它们的用法和效果略有不同。

## 排序

### list.sort()

list.sort() 方法是在原列表的基础上进行排序的，该方法没有返回值，会直接修改原列表，用法如下：

```python
list_a = [3, 1, 4, 2, 5]
list_a.sort()
print(list_a)  # [1, 2, 3, 4, 5]
```

如上述代码所示，我们可以直接通过 list.sort() 方法对列表进行排序，并在原列表上进行更改。

### sorted(list)

sorted(list) 函数是在不改变原列表的基础上，返回一个排序后的新列表，用法如下：

```python
list_b = [3, 1, 4, 2, 5]
sorted_list_b = sorted(list_b)
print(sorted_list_b)  # [1, 2, 3, 4, 5]
print(list_b)  # [3, 1, 4, 2, 5]
```

如上述代码所示，我们可以在不改变原列表的基础上，使用 sorted(list) 函数生成一个新的排序后的列表 sorted_list_b，并保留原列表 list_b。

### 区别

可以看出，两种方式都能够对列表进行排序。不同之处在于：

- list.sort() 是对原列表进行操作，无需生成新的列表，而 sorted() 函数则需要生成一个新的排序后的列表。
- list.sort() 方法的返回值是 None，而 sorted() 函数的返回值是排序后的新列表。

因此，在我们需要修改原列表并且不需要返回值时，我们可以使用 list.sort() 方法来排序；在需要生成新的列表且不改变原列表时，我们可以使用 sorted() 函数来排序。

## 从 List 中筛选出符合条件的元素，并返回一个新的 List

- 可以使用`filter`
- 例如，我们想要在一个列表中，找到包含某个特定字符串的所有元素，并舍弃那些不包含这个字符串的元素：（搭配`in`用于判断“是否包含”）
- `list(filter(lambda item: '需要包含的字符串' in item, list))`

## 对 List 中的每一个元素进行同样的操作

- 例如，对列表中的每一个元素都加 1

- `list(map(lambda x: x+1, mylist))`

## `.append()`和`.extend()`

- `.append()`是将括号内的东西作为**整体**添加到原列表中。

![image-20230117142645199](list-image/image-20230117142645199.png)

- `.extend()`是将括号内的东西（这个东西也是列表）的**元素**添加到原列表中。

![image-20230117142648052](list-image/image-20230117142648052.png)

## 两个字符串列表，交叉拼接

```python
letters = ["abc", "def", "ghi"]
numbers = ["123", "456"]

from itertools import product

result = ["".join(p) for p in product(letters, numbers)]
```

## 判断两个列表是否完全相同

```python
import operator

a = [1, -1, 0]
b = [1, -1, 0]
c = [-1, 1, 0]
print(operator.eq(a, b))
print(operator.eq(a, c))

# True
# False
```

## 替换列表中的某个元素为新的元素

```pycon
# 将所有的 1 都替换为 4
>>> a=[1,2,3,1,3,2,1,1]
>>> [4 if x==1 else x for x in a]
[4, 2, 3, 4, 3, 2, 4, 4]
```

## 从列表 A 中删去列表 B 中的元素

```python
list_C = list(set(list_A) - set(list_B))
```

## 将 List 展平 Flatten

```python
import itertools

a_list = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
b_list = list(itertools.chain.from_iterable(a_list))
```

![image-20230117142652126](list-image/image-20230117142652126.png)

## 对 List 求均值

```python
np.mean(a_list)
```

注意不能用

```python
a_list.mean()
```

因为 List 本身没有`.mean`的方法。

## 生成 Fibonacci 数列

```python
def generate_fibonacci_sequence(min_number, max_number):
    """
    生成费拨那契数列，支持小数的生成
    :param min_number: 最小值
    :param max_number: 最大值
    :return:s
    """
    sequence = []
    base = 1
    if min_number < 1:
        base = 10 ** len(str(min_number).split(".")[1])
    last_number = 0
    new_number = 1
    while True:
        last_number, new_number = new_number, last_number + new_number
        if new_number / base > min_number:
            sequence.append(new_number / base)
        if new_number / base > max_number:
            break
    return sequence[:-1]
```

## 将两个列表转为字典

```python
keys = ["a", "b", "c"]
values = [1, 2, 3]
dictionary = dict(zip(keys, values))
print(dictionary)  # {'a': 1, 'b': 2, 'c': 3}
```

参考：[https://stackoverflow.com/a/209854/](https://stackoverflow.com/a/209854/)

## 一个列表中的所有元素都是字符，将这个列表转换为字符串

```python
"".join(list_var)
```

或者更直观但消耗更多内存的方法：

```python
string = ""
for i in list_var:
    string += i
```
