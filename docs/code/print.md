---
title: Print 打印
icon: material/printer
tags:
  - Python
---

# Print 打印

## 将列表（List）导出为文本文件（.txt）

```python
with open(r"output.txt", "w") as fp:
    fp.write("\n".join(list))
```

## 打印居中文本，两边填充字符

`.center`可以让字符居中，并在两边填充字符。

```python
print("执行开始".center(10, "-"))  # 10 代表所有字符的总长度
```

## 将数据框以带边框的表格形式打印在终端

```python
from prettytable import PrettyTable
import pandas as pd

# 生成数据框
df = pd.DataFrame({"name": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "age": [28, 34, 29, 42]})

# 生成表格
table = PrettyTable()
table.field_names = df.columns.tolist()
for i in range(len(df)):
    table.add_row(df.iloc[i].tolist())
print(table)
```

![image-20230117173813695](print-image/image-20230117173813695.png)

## 将 print 的内容导出到文本文件

参考：https://www.askpython.com/python/built-in-methods/python-print-to-file

```python
# sys 用于将输出结果保存为本地文件
import sys

# Save the current stdout so that we can revert sys.stdou after we complete
# our redirection
stdout_fileno = sys.stdout

# 将输出内容重定向至文本文件
sys.stdout = open("想要导出到的文件名.txt", "w")
print("some texts")
sys.stdout.close()
# 将输出内容恢复至默认，即在终端显示，而不是写入到文本
sys.stdout = stdout_fileno
```

## print 内容后，不换行，而是覆盖本行继续打印

这个功能可以当作进度条。

```python
print(" %s /%s" % (i, total), end="\r")
```

## 将输出呈现为绿色

```python
print("\033[92m All tests passed.")
```

要将输出文本呈现为绿色，可以在 Python 中使用 ANSI 转义序列 `\033[92m` 将其设置为绿色文本。此转义序列包含一个转义字符 (`\033`)、一个左方括号 (`[`)、数字 `9`（表示绿色），以及字母 `m`（表示结束）。因此，在要呈现为绿色的文本前添加 `\033[92m` 即可。

![image-20230330112700445](print-image/image-20230330112700445.png)
