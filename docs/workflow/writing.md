---
icon: material/file-edit
---

## Markdown

基本语法可以参考开源文档 [Cmd Markdown 简明语法手册](https://www.zybuluo.com/mdeditor?url=https://www.zybuluo.com/static/editor/md-help.markdown)。

### 图片缩放

```markdown
![Image title](https://dummyimage.com/600x200/){width="80%"}
```

![Image title](https://dummyimage.com/600x200/){width="80%"}

其中，尾部的参数可以使得图片居中，且能够指定缩放大小。

```markdown
{width="80%"}
```

也可以指定像素值：

```markdown
{width=500px}
```

### 图片显示圆形轮廓

```markdown
{width=500px style="border-radius:50%"}
```

### 单个图片居中

```markdown
![Image title](https://dummyimage.com/600x200/){style="display:block; margin:0 auto;"}
```

![Image title](https://dummyimage.com/600x200/){style="display:block; margin:0 auto;"}

### 全局图片居中

!!! tip "添加 CSS，使所有图片自动居中显示"

    ```css
    img {
      display: block;
      margin-left: auto;
      margin-right: auto;
    }
    ```

### 插入标题

```markdown
<figure markdown>
  ![插入标题](https://dummyimage.com/600x200/)
  <figcaption>标题</figcaption>
</figure>
```

<figure markdown>
  ![插入标题](https://dummyimage.com/600x200/)
  <figcaption>标题</figcaption>
</figure>
### 插入表格

下面是一个简单的示例：

```
| 水果 | 价格 | 数量 |
| :-- | ---: | :---: |
| 苹果 | ¥5.00 | 10 |
| 香蕉 | ¥3.00 | 5 |
| 葡萄 | ¥8.00 | 20 |
```

在这个表格中，第一列使用了居左对齐方式，第二列使用了居右对齐方式，第三列使用了居中对齐方式。

如果您想要调整表格的宽度，可以根据需求修改列对应的 `:` 以确定对齐方式和宽度。例如，如果您想要将第一列和第三列各占据表格宽度的 30%，第二列占据表格宽度的 40%，则可以像下面这样修改：

```
| 水果 | 价格 | 数量 |
| :--------- | :-------: | ---------: |
| 苹果 | ¥5.00 | 10 |
| 香蕉 | ¥3.00 | 5 |
| 葡萄 | ¥8.00 | 20 |
```

在这个表格中，第一列和第三列的 `:` 从 `:--` 和 `:---:` 改为了 `:---------` 和 `---------:`，表示每列占据表格宽度的 30%；第二列的 `:` 从 `---:` 改为了 `:-------:`，表示占据表格宽度的 40%。这样就可以根据需要调整表格的宽度和对齐方式了。

## 格式化

使用 [autocorrect](https://github.com/huacnlee/autocorrect) 自动格式化文档。

下载 autocorrect 的可执行文件，以在命令行中使用：

[https://github.com/huacnlee/autocorrect/releases](https://github.com/huacnlee/autocorrect/releases)

将`autocorrect.exe`可执行文件放在当前目录下，可通过下面的命令行检查和修复格式问题。

### 检查格式问题

```shell
.\autocorrect --lint .\
```

### 修复格式问题

```shell
.\autocorrect --fix .\
```

!!! warning

    `#!shell .\autocorrect --fix .\` 会将当前目录下的所有文件全部递归格式化，修复后无法撤销！请做好备份，谨慎使用该命令！

### 自动检查格式问题并选择是否修复的批处理脚本

```shell
:: This is a simple script to run autocorrect on all files in the current directory
.\autocorrect --lint .\
@echo off
set /p user_input=Do you want to format all files? (y/n):
if /i %user_input%==y (goto Yes) else (goto No)
if /i %user_input%==n (goto No) else (goto Invalid)

:Yes
.\autocorrect --fix .\
pause
exit

:No
pause
exit

:invalid
echo %user_input% is invalid, try again!
pause
exit
```

这段 Batch 脚本学习了 [AutomatewithRakesh@youtube](https://youtu.be/4TT5MhurtgM) 的教程。

!!! success "使用效果"

    ![image-20230207003819721](writing-image/image-20230207003819721.png)

## $\LaTeX$

### 模板

[这里](../../blog/latex-template/) 有自用的 $\LaTeX$ 模板，包括作业、书信等场景。

### 速查笔记

[这里](../../blog/latex-note/) 有速查笔记。

### Mac OS 配置 $\LaTeX$

参考：[Mac OS 配置 $\LaTeX$ (MacTex+VSCode+Skim)](https://zhuanlan.zhihu.com/p/163044184)

- $\TeX$ 代码跳转到 PDF：++command+option+j++
- PDF 跳转到 $\TeX$ 代码：按住 ++command+shift++ 并用鼠标点击文档。

### 数学公式

基本语法可以参考开源文档 [Cmd Markdown 公式指导手册](https://ericp.cn/cmd)。

### 设置全局行距

```
\linespread{1.5} % 设置行距为 1.5 倍
```

### 设置 section 标题格式为左对齐，加粗，大号字体

参考：[https://tex.stackexchange.com/a/247571/](https://tex.stackexchange.com/a/247571/)

```
\ctexset{section/format=\Large\bfseries}
```

### 修改中文字体

以下命令将正文设为宋体，粗体设为黑体。

```tex
\setCJKmainfont[BoldFont=STHeiti]{STSong}
```

### 自定义摘要样式

```
\usepackage{abstract}

\renewcommand{\abstractname}{\zihao{3} 摘要\\} % 设置摘要名称和摘要字体大小
\setlength{\absleftindent}{0pt}
\setlength{\absrightindent}{0pt}

\begin{abstract}
     This is abstract
\end{abstract}
```

### 更改摘要本身的字号大小

如果你要更改“摘要”这几个字的字体大小，可以使用`\renewcommand`命令来重定义摘要标题的格式。下面是一个例子：

```
\documentclass{article}

\renewcommand{\abstractname}{\large\textbf{摘要}} % 将"摘要"改为加粗、大号字体

\begin{document}

\begin{abstract}
这里是摘要的内容。
\end{abstract}

\end{document}
```

在`\renewcommand`命令中，我们定义了一个新的命令`\abstractname`，并将其设置为了加粗、大号字体的“摘要”。你可以根据需要更改这个命令的定义，以达到自己想要的摘要标题格式。

### `Jupyter Notebook` 通过 $\TeX$ 转换为 PDF

工作流程：

1. 先通过网页版 `Jupyter Notebook` 转换为 $\TeX$ 文件，得到一个压缩包。

2. 在 `\documentclass[11pt]{article}` 下面补充 `\usepackage[UTF8]{ctex}`，以支持中文：

	![image-20231109005448426](writing-image/image-20231109005448426.png)

3. 使用 $\LaTeX$ 编译成 PDF 文件。

## `marp`

### 插入双栏

```css
.columns {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1rem;
}
```

```html
<div class="columns">

<div>

</div>

<div>

</div>
```

### 高亮文本

```css
mark {
  background-color: #ffee00;
}
```

```html
<mark>需要高亮的文本</mark>
```

### 减小间距

```css
.lesshspace {
  margin-top: -1em;
}
```

```html
<div class="lesshspace">
</div>
```

### 增大间距

```css
.morehspace {
  margin-top: 1em;
}
```

```html
<div class="morehspace">
</div>
```

### 图片居中

```markdown
<style>
img[alt~="center"] {
  display: block;
  margin: 0 auto;
}
</style>

![w:640 center](image.png)
```

