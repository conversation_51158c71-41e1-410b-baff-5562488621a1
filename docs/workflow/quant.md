---
password: 'fc'
icon: material/alpha
hide:
  - toc
---

## 构造因子

### 提取数据

#### 日频数据

```python
daily_data = D.dataset(
    ["000001.SZ"],
    ["$high", "$low", "$close", "Ref($close, 1)"],
    start_time=start_date,
    end_time=end_date,
)
```

#### 分钟频数据

``` python
minute_data = D.dataset(
    ["000001.SZ"],
    ["RTH($pctchange@min)"],
    start_time=start_date,
    end_time=end_date,
    freq="min",
    verbose=True,  # 可以输出提取数据的进度条。
)
```

![image-20230203005529951](quant-image/image-20230203005529951.png){width="80%" style="display:block; margin:0 auto;"}

#### 重复表达式复用

```python
# 重复表达式存为变量，方便后续复用
daily_panic_level = "DailyPanicLevel($pctchange)"
daily_panic_level_minus_avg_of_previous_two_days = f"{daily_panic_level} - (Ref({daily_panic_level}, 1) + Ref({daily_panic_level}, 2)) / 2"

feature_config = (
    [
        "$pctchange",
        daily_panic_level,
        "RsStd($pctchange@min)",
        "Div(Add($smallbuydealamt_4, $smallselldealamt_4), Mul(2, $amount))",
        f"If({daily_panic_level_minus_avg_of_previous_two_days} > 0, {daily_panic_level_minus_avg_of_previous_two_days}, np.NaN)",
    ],
    [
        "daily_pctchange",  # 日收益率
        "daily_panic_level",  # 日惊恐度
        "daily_volitility",  # 日波动率
        "daily_individual_investor",  # 日个人投资者交易比
        "daily_decayed_panic_level",  # 日衰减惊恐度
    ],
)
```

## 绩效评价

### 将收益率数据转换为价格数据

```python
def prices_from_returns(returns):
    ret = 1 + returns
    ret.iloc[0] = 1
    return ret.cumprod()
```

### 计算年化收益率

```python
def annualized_return(returns, periods_per_year=250):
    return (1 + returns).prod() ** (periods_per_year / len(returns)) - 1
```

如果知道资金曲线对应的交易日期，那么下面的计算方式更准确：

```python
annual_return = (df["资金曲线"].iloc[-1]) ** (
    "1 days 00:00:00" / (df["交易日期"].iloc[-1] - df["交易日期"].iloc[0]) * 365
) - 1
```

### 计算每年、每月收益率

```python
# 请确保索引是日期格式
df.set_index("交易日期", inplace=True)
year_return = df[["涨跌幅"]].resample(rule="A").apply(lambda x: (1 + x).prod() - 1)
month_return = df[["涨跌幅"]].resample(rule="M").apply(lambda x: (1 + x).prod() - 1)
```

### 计算年化波动率

```python
def annualized_volatility(returns, periods_per_year=250):
    return returns.std() * np.sqrt(periods_per_year)
```

### 计算夏普比率
```python
def sharpe_ratio(returns, risk_free_rate=0, periods_per_year=250):
    return (
        annualized_return(returns, periods_per_year) - risk_free_rate
    ) / annualized_volatility(returns, periods_per_year)
```

### 计算最大回撤

```python
def max_drawdown(prices):
    return (prices / prices.cummax() - 1).min()
```

