/* 字体 */
h1,
h2,
h3 {
  font-weight: bold !important;
}
body {
  font-family: Palatino, palatino linotype, palatino lt std, "思源宋体 CN",
    source-han-serif-sc, "Source Han Serif SC", "Source Han Serif CN",
    "Source Han Serif TC", "Source Han Serif TW", "Source Han Serif",
    "Songti SC", sans-serif;
  text-align: justify;
}
code {
  font-family: consolas, monospace;
}
/* 代码块的最大高度，若太高则可以垂直滚动 */
.md-typeset pre > code {
  max-height: 20rem;
}
::selection {
  background: #b5d6fc;
}
/* 块的样式 */
.md-typeset .admonition,
.md-typeset details {
  border-width: 0;
  border-left-width: 4px;
  font-size: 0.75rem !important;
  font-family: palatino linotype, book antiqua, Palatino, stkaiti, kaiti, 楷体,
    simkai, dfkai-sb, nsimsun, serif;
}
/* 图片居中 */
img {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
/* 菜单栏字号 */
.md-tabs__link {
  font-size: 0.8rem !important;
}
/* 限制 admonition 的宽度 */
.md-typeset .admonition.narrow {
  max-width: 400px;
  margin-inline: auto;
}
/* 禁用 Mathjax 右键菜单 */
.MathJax_Menu {
  display: none !important;
}
