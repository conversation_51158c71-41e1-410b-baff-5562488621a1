---
title: 将微调后的模型发布至 Hugging Face
authors: 
  - <PERSON>
date: '2024-02-23'
slug: publish-finetuned-model-to-huggingface
categories:
  - 深度学习
  - PyTorch
  - Python
tags:
  - 深度学习
  - PyTorch
  - Python
links:
  - "Hugging Face 教程": https://huggingface.co/docs/transformers/v4.15.0/model_sharing
---

# 将微调后的模型发布至 Hugging Face

发布微调后的 BERT 模型到 Hugging Face 模型库是一个很好的方式，可以让社区成员共享和使用你的工作。本文介绍了如何准备和发布你的模型到 Hugging Face。

![image-20240223110029966](index-image/image-20240223110029966.png)

<!-- more -->

## `save_pretrained`

使用以下代码将微调后的模型保存到本地：

```python
# 假设 model 是你微调后的模型，tokenizer 是对应的 tokenizer
model.save_pretrained("your_model_directory")
tokenizer.save_pretrained("your_model_directory")
```

 `model.save_pretrained('your_model_directory')` 会生成

```
config.json
model.safetensors
```

`safetensors` 是 Hugging Face 推出的一种新的模型存储格式。如果想得到传统的 `pytorch_model.bin` 文件，可以参考[https://github.com/huggingface/transformers/issues/28863](https://github.com/huggingface/transformers/issues/28863)，使用

```python
model.save_pretrained("your_model_directory", safe_serialization=False)
```

`tokenizer.save_pretrained('your_model_directory')` 会生成

```
tokenizer_config.json
special_tokens_map.json
vocab.txt
tokenizer.json
```

最终得到：

![image-20240223105622904](index-image/image-20240223105622904.png)

!!! tip "生成适用于多个框架的模型文件"

	如果需要使 PyTorch 模型文件也能被 TensorFlow 使用，可以参考 [https://huggingface.co/docs/transformers/v4.15.0/model_sharing#make-your-model-work-on-all-frameworks](https://huggingface.co/docs/transformers/v4.15.0/model_sharing#make-your-model-work-on-all-frameworks)

## 在 Hugging Face 新建 Model

最后，在 [https://huggingface.co/new](https://huggingface.co/new) 上新建 Model，再点击 Add File 即可将文件拖拽上传。

![image-20240223110029966](index-image/image-20240223110029966.png)

这样以后，所有人都能通过下面的代码导入该模型：

```python
# Load model directly
from transformers import AutoTokenizer, AutoModelForSequenceClassification

tokenizer = AutoTokenizer.from_pretrained("JeremyFeng/machine-generated-text-detection")
model = AutoModelForSequenceClassification.from_pretrained(
    "JeremyFeng/machine-generated-text-detection"
)
```

