---
title: 调试 MapReduce
authors: 
  - <PERSON>
date: '2022-09-17'
slug: MapReduce-debug
categories:
  - 数据库
tags:
  - 数据库
toc: yes
katex: yes
description: MapReduce 在 Hadoop 上运行时可能会报错，但报错信息并不会告诉我们哪里出错了。我们可以在本地调试 Map 或 Reduce 的 Python 文件，帮助找到报错的原因。
---

# 调试 MapReduce

MapReduce 在 Hadoop 上运行时可能会报错，但报错信息并不会告诉我们哪里出错了。我们可以在本地调试 Map 或 Reduce 的 Python 文件，帮助找到报错的原因。

![image-20220917111605682](index-image/image-20220917111605682.png)

<!-- more -->

## 问题描述

本项目的目录层级结构：

![image-20220917120453740](index-image/image-20220917120453740.png)

在运行`run.sh`时，可能会运行失败。

![image-20220917111403946](index-image/image-20220917111403946.png)

可以看到，才刚开始`map`，就出现了错误。这说明`mapper.py`文件可能有 bug。

## 调试`mapper.py`

但是 Hadoop 端并没有告诉我们 bug 在哪里。我们可以在本地通过**管道命令**对`mapper.py`进行调试。

```bash
cat ./*.txt | python ./mapper.py
```

![image-20220917111605682](index-image/image-20220917111605682.png)

`|`是 Linux 命令的管道，`|`左边的东西会作为右边的输入。在这个例子中，我们将`A.txt`和`B.txt`作为`mapper.py`的输入，并在屏幕打印`mapper.py`的输出结果。

可以看到，第 7 行的代码中`field`没有被定义。

再检查`mapper.py`，发现确实是少写了一个字母，导致变量名出错。

![image-20220917111915930](index-image/image-20220917111915930.png)

修改变量名后，再次调试，可以发现运行正常：

![image-20220917112818103](index-image/image-20220917112818103.png)

## 调试`reducer.py`

在调试完`mapper.py`后，可以继续将`mapper.py`的输出作为`reducer.py`的输入，对`reducer.py`进行调试。

```bash
cat ./*.txt | python ./mapper.py | python ./reducer.py
```

![image-20220917113002445](index-image/image-20220917113002445.png)

可以发现运行结果符合预期，说明`reducer.py`应该也是正确的。
