---
title: 使用 joblib 实现并行计算
authors: 
  - <PERSON>
date: '2022-12-01'
slug: parallel-computing-using-joblib
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 使用 joblib 实现简单的并行计算。
links:
  - blog/posts/2022-08-09-python-multiprocessing/index.md
---

# 使用 joblib 实现并行计算

使用 joblib 实现简单的并行计算。

![并行运行函数耗时](index-image/并行运行函数耗时.png)

<!-- more -->

[这篇帖子](../python-multiprocessing/)介绍了使用 Python Multiprocessing 多进程并行计算。本文使用 joblib 实现简单的并行计算。

## 查看 CPU 数量

- 使用 multiprocessing：

```python
import multiprocessing

multiprocessing.cpu_count()
```

- 使用 joblib：

```python
import joblib

joblib.cpu_count()
```

## 串行运行函数

```python
def single(a):
    """定义一个简单的函数"""
    time.sleep(1)  # 休眠 1s
    print(a)  # 打印出 a
```

直接使用`for` 循环运行函数 10 次，需要花 10 秒：

```python
import time

start = time.time()  # 记录开始的时间
for i in range(10):  # 执行 10 次 single() 函数
    single(i)
Time = time.time() - start  # 计算执行的时间
print("一共耗时" + str(Time) + "s")
```

## 并行运行函数

```python
from joblib import Parallel, delayed

start = time.time()  # 记录开始的时间
Parallel(n_jobs=5)(delayed(single)(i * 3) for i in range(10))  # 并行化处理
Time = time.time() - start  # 计算执行的时间
print("一共耗时" + str(Time) + "s")
```

![并行运行函数耗时](index-image/并行运行函数耗时.png)

- 一共耗时 2.76 秒。这是由于同时运行 5 次函数，总共运行 10 次，一共花了 2 轮时间。
- 并且，函数的打印结果也是每 5 个同时打印出来的。
- 当`n_jobs`的值为 1 时，即相当于`for`循环的顺序执行，结果仍然会是 10 秒。
- 当`n_jobs`的值为 -1 时，会使用所有的 CPU。
- 当`n_jobs`的值为其它负值时，会使用所有的 CPU 数量减去`n_jobs`。例如一共有 8 个 CPU，则`n_jobs=-2`意味着使用 8-2=6 个 CPU。
- 当`n_jobs=-1`时，使用所有的 CPU 执行并行计算。
- 当`n_jobs=1`时，就不会使用并行代码，即等同于顺序执行，可以在 debug 情况下使用。
- 当`n_jobs<-1`时，将会使用`n_cpus + 1 + n_jobs`个 CPU，例如`n_jobs=-2`时，将会使用`n_cpus-1`个 CPU 核，其中`n_cpus`为 CPU 核的数量。
- 当`n_jobs=None`的情况等同于`n_jobs=1`。