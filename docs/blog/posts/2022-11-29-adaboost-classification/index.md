---
title: AdaBoost 分类原理
authors:
  - <PERSON>
date: "2022-11-29"
slug: adaboost-classification
categories:
  - 机器学习
tags:
  - 机器学习
toc: yes
katex: yes
description: 话语权（Amount of say）、权重更新，是 AdaBoost 中十分重要的两个概念。
links:
  - "AdaBoost, Clearly Explained by StatQuest": https://www.youtube.com/embed/LsK-xG1cLYA
---

# AdaBoost 分类原理

话语权（Amount of say）、权重更新，是 AdaBoost 中十分重要的两个概念。

<!-- more -->

StatQuest 频道的视频讲得非常赞，跟着过一遍就能弄懂 AdaBoost 的原理，并且可以理解幻灯片上晦涩难懂的公式。

说实话，我一直认为教学最需要用简单、直白、图像化的形式。对我来说，老师写的许多板书、念的许多公式，在我脑子里都需要先转换为图像才能理解。如果全世界的老师都能像 StatQuest 这样教学，那么学一个知识并不需要多久，也一定不会很痛苦。

<iframe width="684" height="385" src="https://www.youtube.com/embed/LsK-xG1cLYA" title="AdaBoost, Clearly Explained" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

临近期末，没有时间整理笔记，暂时先记下这个宝贵的视频。
