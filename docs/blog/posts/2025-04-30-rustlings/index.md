---
title: 学习 Rust
authors: 
  - <PERSON>
date: '2025-04-30'
slug: learn-rust
categories:
  - Rust
tags:
  - Rust
links:
  - "Rust 语言圣经": https://course.rs/about-book.html
  - "rustlings": https://github.com/rust-lang/rustlings
---

# 学习 Rust

最近学习了 Rust。一直听说 Rust 效率高，从`ruff` 和 `uv` 这两个基于 Rust 实现的工具开始对 Rust 印象深刻，自己亲身体会过它的语法细节之后，对它为什么快、为什么“安全”，总算有了一点理解。

推荐一下 [`rustlings`](https://github.com/rust-lang/rustlings) 这个学习 Rust 的项目。每个文件就是解决一个小问题，这种在小练习中修改代码的过程比从头到尾读教程要有趣和有成就感得多。结合[《Rust 语言圣经》](https://course.rs/about-book.html)这个优秀的教程食用，效果甚佳。

![rustlings-finished](index-image/rustlings-finished.png)

<!-- more -->

就编程语言来说，我觉得从入门到熟练掌握的过程就是：

- 首先能看懂代码。这是需要付出时间来扎实学习的。
- 然后能写出正确的代码。这一步在 AI 的帮助下将变得越来越便利。但“看懂代码”的能力仍然重要，这样才能引导 AI 写出正确地代码。
- 经验丰富的程序员见多识广，熟悉各种最佳实践，能评价一段代码写得好不好、是否有更好的做法。

![rustlings-list](index-image/rustlings-list.png){width=500px}

