---
title: Batch Normalization
authors:
  - <PERSON>
date: "2022-12-08"
slug: batch-normalization
categories:
  - 深度学习
tags:
  - 深度学习
toc: yes
katex: yes
description: 批标准化是神经网络中最常用的组件之一。
---

# Batch Normalization

批标准化是神经网络中最常用的组件之一。

![image-20221208231504504](index-image/image-20221208231504504.png)

<!-- more -->

## Batch Normalization 的过程

Batch Normalization 通过将每一层的原始输出进行标准化（减去均值，除以标准差），还可以乘以$\gamma$（Scale），再加上$\beta$（Offset）。$\gamma$和$\beta$都是超参数，可以用神经网络训练它们。

具体的数学公式如下。

求第$l$层的批均值：

$$
\mu=\frac{1}{m} \sum_{i=1}^m Z^{l(i)}
$$

求第$l$层的批方差：

$$
\sigma^2=\frac{1}{m} \sum_{i=1}^m\left(Z^{l(i)}-\mu\right)^2
$$

批标准化的结果：

$$
\hat{Z}^l=\gamma * \frac{Z^l-\mu}{\sqrt{\sigma^2+\varepsilon}}+\beta
$$

经过上述操作，即可将$Z^l$转换为$\hat{Z}^l$。

在批标准化中，可优化的参数是$\gamma$和$\beta$。如果没有$\gamma$和$\beta$，则批标准化的运算就为常规的 z-score 标准化。

!!! tip "为什么要加上参数$\gamma$和$\beta$？"

    $\gamma$和$\beta$是可优化的参数，至少可以让它们取值为 $\sqrt{\sigma^2+\varepsilon}$ 和 $\mu$，这样就可以将批标准化的结果转换为原始的 $Z^l$。这意味着，批标准化的结果不会比不使用批标准化的结果差。

![image-20221208231504504](index-image/image-20221208231504504.png)

## Batch Normalization 的 Python 实现

只要加上

```python
keras.layers.BatchNormalization()
```

![image-20221208232015838](index-image/image-20221208232015838.png)

## Batch Normalization 的优点

Batch Normalization 可以使神经网络具有以下优点：

### 加快训练速度

- 标准化后的值的分布更加相似，因此可以用更大的学习率进行梯度下降，这可以加快训练速度。

![image-20221208230551827](index-image/image-20221208230551827.png)

### 对梯度下降的初始值更加不敏感

- 如果没有标准化，那么梯度下降的初始点选得不一样，迭代的次数可能差别非常大。标准化后，所有的点离最优点的距离都差不多，因此对初始值不敏感。

![image-20221208230846371](index-image/image-20221208230846371.png)

### 在一定程度上缓解过拟合问题

标准化会将原始数据的极端值变换为合理的区间，因此模型的输出受训练集的极端值的影响更小，在测试集上的泛化能力更强。

## 学习视频

<iframe width="684" height="385" src="https://www.youtube.com/embed/yXOMHOpbon8" title="Batch normalization | What it is and how to implement it" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

<iframe width="684" height="385" src="https://www.youtube.com/embed/DtEq44FTPM4" title="Batch Normalization - EXPLAINED!" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
