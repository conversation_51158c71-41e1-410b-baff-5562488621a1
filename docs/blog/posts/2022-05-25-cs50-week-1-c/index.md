---
title: CS50 Week 1 C
authors: 
  - <PERSON> Feng
date: '2022-05-25'
slug: CS50-Week-1-C
categories:
  - Computer Science
tags:
  - Computer Science
toc: true
description: "C 语言基础知识。"
---

# CS50 Week 1 C

C 语言基础知识。

<!-- more -->

## `C`语言

`C`是一门古老的语言，它也是很多编程语言（如`Python`）的基础。

`C`会将源代码编译（Compile）成机器语言（Machine Code），也就是得到一个可执行文件，这个可执行文件是由 0 和 1 组成的。

## 函数和参数

`printf("hello, world");`

- `printf`中的`f`代表 format。

- `C`的语句由`;`结尾。

## 返回值和变量

`string answer = get_string("What's your name? ");`

- 可以接收用户输入的字符串，并赋值给`answer`这个字符串变量。

- `get_string`需要使用`#include <cs50.h>`，这是 CS50 课程的一个头文件。`.h`代表`head`。

## Linux 命令

- `rm`：删除

- `ls`：列出文件夹中的项目（包括文件和子文件夹）

- `mkdir`：新建文件夹

- `code hello.c`：新建`hello.c`文件	

- `cd`：进入到某个文件夹

- `cd ..`：返回上一级文件夹

- `cd ../..`：返回上上一级文件夹

## 数据类型

- `bool`：布尔值，表示`Ture`和`False`

- `char`：字符

- `string`：字符串

- `int`：整型数字

- `long`：长型数字（占用空间更大）

- `float`：单精度浮点型数字

- `double`：双精度浮点型数字（占用空间更大，精度是 float 的两倍）

## 格式化输出

- `%c`、`%s`分别表示字符和字符串。

- `%i`表示整型数字。
- `%f`表示浮点型数字。

## Syntactic Sugar

理解：用更少的代码实现同样的功能。

例如：下面的三个语句都是将`a`的值加 1。

- `a = a + 1;`
- `a += 1;`
- `a++;`

## 注释

用两个斜杠`//`来将一行代码注释掉。

## 整数溢出

储存数据的字节不足以存放一个巨大的数字时，运算可能会出错。

> 在`C`中存在整数溢出的问题，但`Python`中不用考虑这个问题。[^整数溢出]

## 判断语句（Conditions）

```c
if (x < y)
{
    printf("x is less than y\n");
}
```

- 在只有一行执行语句的时候，判断语句中的`{}`可以不加，但最好的实践方式是：不论执行语句有多少行，都加上`{}`。
- `if`这一行的最后不用加`;`。

## 逻辑“或”

- 用`||`来表示逻辑“或”。

```C
#include <cs50.h>
#include <stdio.h>
  
int main(void)
{
    // Prompt user to agree
    char c = get_char("Do you agree? ");
  
    // Check whether agreed
    if (c == 'Y' || c == 'y')
    {
        printf("Agreed.\n");
    }
    else if (c == 'N' || c == 'n')
    {
        printf("Not agreed.\n");
    }
}
```

## 循环（Loops）

- `while`实现无限次循环：

```C
while (true)
{
    printf("meow\n");
}
```

- `while`实现有限次循环：

```C
int counter = 0;
while (counter < 3)
{
    printf("meow\n");
    counter = counter + 1;
}
```

- `for`实现有限次循环：

```C
for (int i = 0; i < 3; i++)
{
    printf("meow\n");
}
```

`for`循环和`while`循环基本上都能实现同样的功能。

一个细小的区别是：`for`中的`i`是在循环内的变量，一旦跳出循环，`i`就不存在了；而`while`循环的`counter`是在循环外（也就是`while`语句所在行的前面）定义的，所以在`while`循环结束时仍然存在。

- `do while`循环：

先执行语句，再判断条件是否成立。若成立，则继续执行语句；若不成立，则跳出循环。

```C
do
{
    n = get_int("Width: ");
}
while (n < 1);
```

## 自定义函数

下面自定义了函数`meow`，可以输出一个字符串。

```C
#include <stdio.h>
  
void meow(void)
{
    printf("meow\n");
}
  
int main(void)
{
    for (int i = 0; i < 3; i++)
    {
        meow();
    }
}
```

`C`是从代码的上端到下端编译的，因此不能直接把`meow`函数放到`main`函数的下面。也就是说，下面的代码会报错：

```C
#include <stdio.h>
  
int main(void)
{
    for (int i = 0; i < 3; i++)
    {
        meow();
    }
}
//把自定义函数直接放到main函数的下面，编译时会报错。
void meow(void)
{
    printf("meow\n");
}
```

如果需要把自定义函数放到`main`函数的后面，可以用`void meow(void);`提前声明（Declare）。

```C
#include <stdio.h>

void meow(void); //声明自定义的函数。
  
int main(void)
{
    for (int i = 0; i < 3; i++)
    {
        meow();
    }
}

void meow(void)
{
    printf("meow\n");
}
```













[^整数溢出]: 关于 Python 整数溢出问题的[讨论](https://www.daniweb.com/programming/software-development/threads/71008/comparing-python-and-c-part-1-integer-variables)。
