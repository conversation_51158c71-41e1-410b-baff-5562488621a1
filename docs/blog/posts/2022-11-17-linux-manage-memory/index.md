---
title: Linux 查看与释放内存
authors:
  - <PERSON>
date: "2022-11-17"
slug: linux-manage-memory
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
toc: yes
katex: yes
description: 查看内存与释放内存的 Linux 命令。
---

# Linux 查看与释放内存

查看内存与释放内存的 Linux 命令。

<!-- more -->

## 查看内存使用情况

```bash
free -m
```

- `m`代表以 MB 为单位。也可用`-g`代表以 GB 为单位。

## 释放内存

```bash
echo 3 > /proc/sys/vm/drop_caches
```

`drop_caches`的值可以是 0-3 之间的数字，代表不同的含义：

- 0：不释放（系统默认值）

- 1：释放页缓存

- 2：释放 dentries 和 inodes

- 3：释放所有缓存
