---
title: 常用的 Conda 命令
authors: 
  - <PERSON>
date: '2022-11-22'
slug: conda
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 总结常用的 conda 命令，方便查找与使用。
---

# 常用的 Conda 命令

总结常用的 Conda 命令，方便查找与使用。

<!-- more -->

## 显示现有的环境

```shell
conda env list
```

## 新建环境

```shell
conda create -n 环境名 python=3.8
```

## 激活环境

```shell
conda activate 环境名
```

## 在虚拟环境中安装指定版本的包

```shell
conda install tensorflow==1.12.0
```

### 如果不需要指定版本，可以省略版本号

```shell
conda install tensorflow
```

## 安装包时指定来源

```shell
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/

conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/

conda config --set show_channel_urls yes
```

其中，`conda config --set show_channel_urls yes`的意思是从 channel 中安装包时显示 channel 的 URL，这样就可以知道包的安装来源了。

## 删除来源

```shell
conda config --remove channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/

conda config --remove channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
```

## 回滚到历史版本

### 查看历史记录

首先，您需要查看已安装的包的历史记录。这可以通过运行以下命令完成：

```bash
conda list --revisions
```

这将显示一个列表，其中包含所有以前的修订版本，以及每个修订版本中安装或删除的包。

### 选择要恢复的版本

一旦您知道了要恢复的版本，就可以通过运行以下命令来恢复它：

```bash
conda install --revision REVNUM
```

其中，`REVNUM`是您希望恢复到的修订版本的编号。例如，如果您要恢复到第 5 次修订，那么您应该运行：

```bash
conda install --revision 5
```

这将会把所有的库都恢复到第 5 次修订时的状态。

!!! warning "注意事项"

	虽然这个功能非常有用，但是也有一些注意事项。首先，当您回滚时，所有在该修订版本之后安装的包都将被删除。这意味着，如果您在第5次修订之后安装了新的库，那么在回滚到第5次修订版本后，这些库将不再存在。因此，在回滚之前，最好先备份您的环境。
	
	其次，这个命令只能回滚到之前的版本，不能回滚到未来的版本。这意味着，如果您已经回滚到了第5次修订，那么您不能再回滚到第6次修订。如果您想要恢复到第6次修订，那么您需要重新安装所有在第6次修订版本中添加的库。

## 查看 conda config

```shell
conda config --show
```

## 删除环境

```shell
conda remove -n 环境名 --all
```

## 重命名环境

conda 其实没有重命名指令，实现重命名是通过 clone 完成的，分两步：

1. 先 clone 一份 new name 的环境
2. 删除 old name 的环境

例如，将`old_env`重命名成`new_env`。需要先将`old_env`克隆一份到`new_env`。

```shell
conda create -n new_env --clone old_env
```

再删除原环境

```shell
conda remove -n old_env --all
```

## 迁移环境

详见[这篇帖子](../migrate-conda-environment/)。

## Conda Prompt 在开始菜单无法显示

```bash
conda install console_shortcut
```

参考：

[https://blog.csdn.net/weixin_41013322/article/details/103010748](https://blog.csdn.net/weixin_41013322/article/details/103010748)

## JupyterLab 无法检测到 Conda 环境

先在终端中 `conda activate base`，再输入：

```bash
ipython kernel install --user --name base
```

参考：[https://blog.csdn.net/dc0723/article/details/123718616](https://blog.csdn.net/dc0723/article/details/123718616)
