---
title: Python 中的赋值与深浅拷贝
authors:
  - <PERSON>
date: "2023-02-05"
slug: python-copy
categories:
  - Python
tags:
  - Python
links:
  - "本文代码": https://github.com/jeremy-feng/scripts/blob/master/python-copy.ipynb
  - "Python3 字典 copy() 方法": https://www.runoob.com/python3/python3-att-dictionary-copy.html
---

# Python 中的赋值与深浅拷贝

Python 中的赋值只是引用了对象，当原变量发生改变时，新变量也会随之发生改变。

`#!py .copy()`方法可以进行浅拷贝，它可以深拷贝父对象（一级目录），但子对象（二级目录）仍然只是引用。

`#!py .deepcopy()`方法可以进行深拷贝，它可以深拷贝父对象（一级目录）和子对象（二级目录），当原变量改变时，深拷贝得到的变量不会发生任何改变。

<!-- more -->

## 代码示例

### 导入`#!py copy`包（仅在深拷贝时需要用到）

```python
import copy
```

### 创建带有二级目录的字典

```python
dict1 = {"user": "runoob", "num": [1, 2, 3]}
```

这里的数组`#!py [1,2,3]`就是子对象（二级目录）。

### 赋值与深浅拷贝

```python
dict2 = dict1  # 浅拷贝：引用对象
dict3 = dict1.copy()  # 浅拷贝：深拷贝父对象（一级目录），子对象（二级目录）不拷贝，子对象是引用
dict4 = copy.deepcopy(dict1)  # 深拷贝：深拷贝父对象（一级目录）核子对象（二级目录）
```

### 修改原变量

```python
dict1["user"] = "root"
dict1["num"].remove(1)
```

### 查看赋值与深浅拷贝的结果是否改变

```python
# 输出结果
print("dict1", dict1)
print("dict2", dict2)
print("dict3", dict3)
print("dict4", dict4)
```

```python
dict1 = {"user": "root", "num": [2, 3]}
dict2 = {"user": "root", "num": [2, 3]}  # (1)!
dict3 = {"user": "runoob", "num": [2, 3]}  # (2)!
dict4 = {"user": "runoob", "num": [1, 2, 3]}  # (3)!
```

1. 赋值只是引用了对象，当原变量发生改变时，新变量也会随之发生改变。
2. 浅拷贝中的父对象`#!py 'user': 'runoob'`没有发生改变，但`#!py 'num': [1, 2, 3]`也变成了`#!py 'num': [2, 3]`。
3. 深拷贝中的父对象`#!py 'user': 'runoob'`和`#!py 'num': [1, 2, 3]`都没有改变。
