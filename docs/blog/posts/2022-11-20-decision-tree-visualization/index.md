---
title: 决策树可视化
authors: 
  - <PERSON>
date: '2022-11-20'
slug: decision-tree-visualization
categories:
  - 机器学习
  - 数据可视化
tags:
  - 机器学习
  - 数据可视化
toc: yes
description: 使用`pydotplus`和`graphviz`对决策树进行可视化。
---

# 决策树可视化

使用`pydotplus`和`graphviz`对决策树进行可视化。

![image-20221122084948130](index-image/image-20221122084948130.png)

<!-- more -->

## 安装包

安装包的方法可以参考[这篇帖子](https://blog.csdn.net/weixin_43490422/article/details/105739057)。

## 打印决策树

```python
# 打印预测用户打算在什么平台申领的决策树
from sklearn.tree import export_graphviz

dot_data = export_graphviz(
    clf,
    out_file=None,
    feature_names=["性别", "年龄", "XX"],
    class_names=clf.classes_,
    filled=True,
    rounded=True,
    special_characters=True,
)

import pydotplus

graph = pydotplus.graph_from_dot_data(dot_data)

# 添加环境变量，否则会报错找不到 Graphviz 的安装路径
import os

os.environ["PATH"] += os.pathsep + "C:/Program Files/Graphviz/bin"

# 导出为 svg 格式的图片，这样能够支持中文
graph.write_svg("文件名.svg")
```

如果报错找不到 Graphviz 的安装路径，记得把 Graphviz 的 bin 目录路径通过`os.environ["PATH"] += os.pathsep + 'C:/Program Files/Graphviz/bin'`添加到环境变量中。

## 导出结果

![image-20221122084948130](index-image/image-20221122084948130.png)

将决策树可视化可以帮助我们理解这颗决策树究竟是如何运行的。
