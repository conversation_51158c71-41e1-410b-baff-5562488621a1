---
title: CS50 Week 6 Python
authors: 
  - <PERSON>
date: '2022-06-14'
slug: cs50-week-6-python
categories:
  - Computer Science
  - Python
tags:
  - Computer Science
  - Python
toc: yes
description: "Python 基础知识。"
---

# CS50 Week 6 Python

Python 基础知识。

<!-- more -->

## Python 语法

### 打印文本

```python
print("hello, world")
```

- 不是`printf`；
- 不需要换行符`\n`，就能自动换行；
  - 如果不想自动换行，可以添加参数`end`，用`print("hello, world", end="")`。

- 行末没有分号。

### 条件语句

```python
if x < y:
    print("x is less than y")
elif x > y:
    print("x is greater than y")
else:
    print("x is equal to y")
```

- 条件语句没有圆括号`()`；
- 执行语句不需要用花括号括起来，但要注意缩进；
- `elif`就是`else if`。

### `while`循环

```python
while True:
    print("meow")
```

- `True`和`False`的首字母要大写。

```python
i = 0
while i < 3:
    print("meow")
    i += 1
```

- 不能用`i++`，但可以用`i += 1`。据说这是因为 Python 的开发者们认为：不需要多种语法来完成同样的事情。

### `for`循环

```python
for i in [0, 1, 2]:
    print("hello, world")
```

- `in`后面可以是一个列表。

```python
for i in range(3):
    print("hello, world")
```

- `range(3)`函数会生成一个长度为 3 的列表，从 0 开始，但不包括 3。

### 数据结构

- `bool`：`True`和`False`。

- `float`
- `int`：不用担心整数溢出的问题。
- `str`
- `range`：一列数字。
- `list`：列表。列表元素的值可以改变。
- `tuple`：元组。元组元素的值不可以改变。
- `dict`：字典，包含`key`和`value`。
- `set`：一个集合，它里面的所有元素都是唯一的，没有重复的元素。

## 库

运行一个 Python 程序，不需要像 C 语言那样编译再运行，只需要一步：

```bash
python hello.py
```

Python 是解释型语言，它会将我们的源代码一行一行地翻译成 CPU 能理解的语言。

### 使用库给图片加滤镜

```python
from PIL import Image, ImageFilter

before = Image.open("bridge.bmp")
after = before.filter(ImageFilter.BoxBlur(10))
after.save("out.bmp")
```

## Python 和 C：编程难度与运行效率

使用 Python 实现字典的读写、查询长度功能：

```python
words = set()


def check(word):
    if word.lower() in words:
        return True
    else:
        return False


def load(dictionary):
    file = open(dictionary, "r")
    for line in file:
        word = line.rstrip()
        words.add(word)
    file.close()
    return True


def size():
    return len(words)


def unload():
    return True
```

我们不需要定义复杂的数据结构，不需要从底层开始编写函数，这主要得益于 Python 中丰富的库。这些库是由一些开发者提供的，让我们能够站在巨人的肩膀上继续开发，免去了繁琐的底层代码设计。因此，Python 的代码通常十分易懂、易写。

C 语言的代码虽然需要写很多底层代码，但它的编译运行速度很快。许多对运行效率要求较高的大型项目都使用 C 语言进行开发。

## Input

```python
answer = input("What's your name? ")
print("hello, " + answer)
```

- `input()`：可以接收用户的输入，为字符串格式。

```python
x = int(input("x: "))
y = int(input("y: "))
print(x + y)
```

- 使用`int()`可以将字符串转换为整型格式，才能进行加减运算。

如果用户输入一个字符串（而不是数字），就不能做`int`的转化内，因此会报错，报错类型是`ValueError`：

```bash
$ python calculator.py
x: cat 
Traceback (most recent call last):
  File "/workspaces/20377622/calculator.py", line 1, in <module>
    x = int(input("x: "))
ValueError: invalid literal for int() with base 10: 'cat'
```

为了防止用户输入的不是数字，可以用`try`和`except`：

```python
try:
    x = int(input("x: "))
except ValueError:
    print("That is not an int!")
    exit()
try:
    y = int(input("y: "))
except ValueError:
    print("That is not an int!")
    exit()
print(x + y)
```

## 除法

在 Python 中，两个整数的除法如果不整除，自动会得到浮点数。而在 C 语言中是只取整数的。

```python
from cs50 import get_int

x = get_int("x: ")
y = get_int("y: ")

z = x / y
print(z)
```

```bash
$ python calculator.py
x: 1
y: 10
0.1
```

如果确实希望除法取整，可以用`z = x // y`：

在 C 语言中，`//`是注释。而在 Python 中，`//`是除法取整，`#`是注释。

## 浮点数的精度

Python 中的浮点数仍然不是完全精确的：

```python
from cs50 import get_int

x = get_int("x: ")
y = get_int("y: ")

z = x / y
print(f"{z:.50f}")
```

```bash
$ python calculator.py
x: 1
y: 10
0.10000000000000000555111512312578270211815834045410
```

## 自定义函数

自定义的函数要先被定义再被调用，否则会报错。

```python
for i in range(3):
    meow()


def meow():
    print("meow")
```

上面的代码会报错：`NameError: name 'meow' is not defined`。

Python 中没有`main`函数，我们可以自定义`main`函数，并在代码的最后调用`main`函数。

```python
def main():
    for i in range(3):
        meow()


def meow():
    print("meow")


main()
```

如果是写一些库文件，那么可以把下面的代码放在底部，防止每次调用库函数的时候都运行整个库文件：

```python
if __name__ == "__main__":
    main()
```

这在调试库文件的时候非常有用。

## 命令行参数

使用`sys`库中的`argv`模块，可以在命令行中接收参数。

```python
from sys import argv

if len(argv) == 2:
    print(f"hello, {argv[1]}")
else:
    print("hello, world")
```

```
$ python argv.py
hello, world
$ python argv.py David
hello, David
```

- `argv[0]`是程序名，即`argv.py`。
- `argv[1]`是`David`。

## 退出代码

用`sys.exit`可以指定退出代码：

```python
from sys import argv, exit

if len(argv) != 2:
    print("Missing command-line argument")
    exit(1)

print(f"hello, {argv[1]}")
exit(0)
```

## 更多强大的库

### 文本转语音

```python
import pyttsx3

engine = pyttsx3.init()
engine.say("hello, world")
engine.runAndWait()
```

### 人脸识别

```python
# Find faces in picture
# https://github.com/ageitgey/face_recognition/blob/master/examples/find_faces_in_picture.py

from PIL import Image
import face_recognition

# Load the jpg file into a NumPy array
image = face_recognition.load_image_file("office.jpg")

# Find all the faces in the image using the default HOG-based model.
# This method is fairly accurate, but not as accurate as the CNN model and not GPU accelerated.
# See also: find_faces_in_picture_cnn.py
face_locations = face_recognition.face_locations(image)

for face_location in face_locations:
    # Print the location of each face in this image
    top, right, bottom, left = face_location

    # You can access the actual face itself like this:
    face_image = image[top:bottom, left:right]
    pil_image = Image.fromarray(face_image)
    pil_image.show()
```

### 语音识别

```python
# Responds to a name
# https://pypi.org/project/SpeechRecognition/

import re
import speech_recognition

# Obtain audio from the microphone
recognizer = speech_recognition.Recognizer()
with speech_recognition.Microphone() as source:
    print("Say something:")
    audio = recognizer.listen(source)

# Recognize speech using Google Speech Recognition
words = recognizer.recognize_google(audio)

# Respond to speech
matches = re.search("my name is (.*)", words)
if matches:
    print(f"Hey, {matches[1]}.")
else:
    print("Hey, you.")
```

### 生成二维码

```python
import os
import qrcode

img = qrcode.make("https://youtu.be/xvFZjo5PgG0")
img.save("qr.png", "PNG")
os.system("open qr.png")
```
