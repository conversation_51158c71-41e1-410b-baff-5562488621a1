---
title: VS Code 加载 Web 视图时出错的解决方案
authors:
  - <PERSON>
date: "2023-02-02"
slug: vscode-error-could-not-register-service-workers-invalid-state
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
links:
  - "Stack Overflow: Windows 解决方案": https://stackoverflow.com/a/69704193/16760102
  - "Stack Overflow: Linux 解决方案": https://stackoverflow.com/a/69313758/16760102
---

# VS Code 加载 Web 视图时出错的解决方案

## 报错原因

在保存一个大型`jupyter notebook`文件时，自己突然关闭了标签页。再打开它时，VS Code 就报错：

```
加载 Web 视图时出错: Error: Could not register service workers: InvalidStateError: Failed to register a ServiceWorker: The document is in an invalid state..
```

![image-20230203004334063](index-image/image-20230203004334063.png){style="display:block; margin:0 auto;"}

并且也不能打开其他任何`jupyter notebook`文件，推测是 VS Code 程序出了问题。

## 解决方案

我自己曾经遇到过两次这个报错，第一次是在个人电脑 Windows 系统上，第二次是在 Linux 服务器上。下面分别介绍针对这两个系统的解决方案。

<!-- more -->

### Windows 系统

可以通过任务管理器关闭 VS Code 进程，再删除 VS Code 的缓存文件。如 Stack Overflow 上的回答：

![image-20230203004641643](index-image/image-20230203004641643.png)

### Linux 系统

可以通过命令行删除 VS Code 的缓存文件：

```bash
rm -rf ~/.config/Code/Cache
```

## 其他解决方案

在搜索资料的时候，还找到了一些解决方案，但有些对我的问题并不起作用（大多是因为找不到命令，需要额外安装其他程序），这里列出作为参考：

```bash
killall code
```

```bash
code --no-sandbox
```
