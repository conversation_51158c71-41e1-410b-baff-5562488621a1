---
title: 删除 LaTeX 编译生成的附加文件
authors: 
  - <PERSON>
date: '2023-02-17'
slug: delete-latex-auxiliary-files
categories:
  - LaTeX
tags:
  - LaTeX
links:  
  - "LaTeX-Workshop Wiki": https://github.com/<PERSON>-Yu/LaTeX-Workshop/wiki/Compile#cleaning-generated-files
  - "Should 'clean up auxiliary files' clean .synctex.gz file?": https://github.com/<PERSON>-Yu/LaTeX-Workshop/issues/1261#issuecomment-477985007
---

# 删除 $\LaTeX$ 编译生成的附加文件

编译 $\LaTeX$ 后生成的许多文件是不需要的，可以在 VS Code 中设置将它们自动删除。

```
"latex-workshop.latex.autoClean.run": "onBuilt", //自动清理不需要的文件
```


<!-- more -->

注意：`.synctex.gz` 文件是用于同步 `.tex` 和 PDF 文件的，如果将其删除则无法自动在`.tex` 和 PDF 文件之间进行跳转。

参考 [https://github.com/<PERSON>-Yu/LaTeX-Workshop/issues/1261#issuecomment-477985007](https://github.com/James-Yu/LaTeX-Workshop/issues/1261#issuecomment-477985007)

