---
title: 查看 PyTorch 是否使用 GPU
authors: 
  - <PERSON>
date: '2022-12-18'
slug: check-device-in-pytorch
categories:
  - 深度学习
  - PyTorch
tags:
  - 深度学习
  - PyTorch
toc: yes
katex: yes
description: 借助 torch.cuda 中的命令，可以帮助我们查看 PyTorch 是否使用 GPU。
---

# 查看 PyTorch 是否使用 GPU

借助 torch.cuda 中的命令，可以帮助我们查看 PyTorch 是否使用 GPU。

![image-20221218225825151](index-image/image-20221218225825151.png)

<!-- more -->

```python
import torch
```

## 当前设备索引

```python
torch.cuda.current_device()
```

![image-20221218225800410](index-image/image-20221218225800410.png)

## 可用的 GPU 数量

```python
torch.cuda.device_count()
```

![image-20221218225816075](index-image/image-20221218225816075.png)

## GPU 设备名字，设备索引默认从 0 开始

```python
torch.cuda.get_device_name(0)
```

![image-20221218225825151](index-image/image-20221218225825151.png)

## 检查 CUDA 是否可用

```python
torch.cuda.is_available()
```

![image-20221218225834009](index-image/image-20221218225834009.png)
