---
title: 访问服务器的 Jupyter Notebook
authors: 
  - <PERSON>
date: '2023-09-19'
slug: access-jupyter-notebook-on-server
categories:
  - Computer Science
tags:
  - Computer Science
links:
  - "如何优雅地使用 Jupyter Notebook？比如用来远程连接云服务器跑深度学习":  https://www.bilibili.com/video/BV1st4y1E76G/
  - "Jupyter 远程配置的 IP 问题":  https://murphypei.github.io/blog/2018/12/jupyter-remote-ip
---

# 访问服务器的 Jupyter Notebook

本文的目的是在服务器上运行 Jupyter Notebook，并在任何电脑上进行访问。

![image-20230920000609997](index-image/image-20230920000609997.png)

<!-- more -->

## 设置密码

在终端输入

```bash
jupyter notebook password
```

按照提示输入两次密码即可。

## 允许任意 IP 访问

默认的 `jupyter notebook` 命令只是开放在服务器上的，如果想要开放给公网访问，有两种设置方法。

### 方法一：在命令行中添加参数 `--ip=0.0.0.0`

```bash
jupyter-notebook --no-browser --ip=0.0.0.0
```

这种方法在每次启动 `jupyter notebook` 都需要添加 `--ip=0.0.0.0` 参数，否则无法在公网访问。

### 方法二：改配置文件

```bash
vim ~/.jupyter/jupyter_notebook_config.py
```

添加：

```python
# 允许任意的电脑访问这个 notebook
c.NotebookApp.ip = "0.0.0.0"
```

!!! tip "关于 ip 的配置"

	[有的教程](https://murphypei.github.io/blog/2018/12/jupyter-remote-ip)说下面的配置会报错，我试了一下，发现不会报错。
	
	```python
	# 允许任意的电脑访问这个 notebook
	c.NotebookApp.ip = '*'
	```

还可以根据需要添加：

```python
# 默认不打开浏览器
c.NotebookApp.open_browser = False
# 指定访问的端口
c.NotebookApp.port = 8888
```

## 在服务器的防火墙中开放 8888 端口

这一步也很重要，如果不开放端口，那么也无法通过公网访问。

![image-20230920000159918](index-image/image-20230920000159918.png)

## 查看效果

为了检验从公网访问的效果，我使用手机进行访问：

![image-20230920000552504](index-image/image-20230920000552504.png)

![image-20230920000609997](index-image/image-20230920000609997.png)
