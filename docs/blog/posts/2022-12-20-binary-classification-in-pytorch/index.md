---
authors: 
  - <PERSON>
date: '2022-12-20'
slug: binary-classification-in-pytorch
categories:
  - 深度学习
  - PyTorch
tags:
  - 深度学习
  - PyTorch
toc: yes
description: 在量化研究中，将预测收益率数值的回归问题转换为预测涨跌、预测超额收益的正负等二分类问题是十分常见的。在 PyTorch 中可以修改部分代码，让回归问题的网络结构在二分类问题中也同样适用。
---

# PyTorch 处理二分类问题

在量化研究中，将预测收益率数值的回归问题转换为预测涨跌、预测超额收益的正负等二分类问题是十分常见的。在 PyTorch 中可以修改部分代码，让回归问题的网络结构在二分类问题中也同样适用。

<!-- more -->

## 将收益率标签转换为 0 和 1

```python
# 由于是分类问题，因此将 y 大于 0 的标签设置为 1，小于 0 的标签设置为 0
trainy[trainy > 0] = 1
trainy[trainy < 0] = 0
testy[testy > 0] = 1
testy[testy < 0] = 0
```

注意，这里不要转换为`1.0`和`0.0`，否则可能会报错：

```
RuntimeError: expected scalar type Long but found Float
```

## 将收益率转换为超额收益率的正负

```python
# 由于是超额收益的分类问题，因此将 y 大于 trainy.mean() 的标签设置为 1，小于 trainy.mean() 的标签设置为 0
train_mean_y = trainy.mean().item()
trainy[trainy > train_mean_y] = 1
trainy[trainy < train_mean_y] = 0
test_mean_y = testy.mean().item()
testy[testy > test_mean_y] = 1
testy[testy < test_mean_y] = 0
```

## 全连接层最后的激活函数设为`sigmoid`，使得最终输出在 0 到 1 之间

```python
def forward(self, data):
    data_1 = self.Inception_1(data)  # N*30
    data_2 = self.Inception_2(data)  # N*30
    pool_cat = torch.cat([data_1, data_2], axis=1)  # N*60
    # 输出层
    data = self.fc(pool_cat)
    # 激活函数，使输出值在 0 到 1 之间
    data = torch.sigmoid(data)
    data = data.to(torch.float)

    return data
```

## 损失函数设为`Binary Cross Entropy`

```python
# 由于是分类问题，因此使用 Binary Cross Entropy 损失函数
criterion = nn.BCELoss()
```

注意，不要设为`nn.CrossEntropyLoss()`，否则可能会报错：

```
IndexError: Target 1 is out of bounds
```

这是因为`nn.CrossEntropyLoss()`适用于多分类（类别数大于 2）的问题。对于二分类问题，有专门的`nn.BCELoss()`作为二元交叉熵损失函数。

> BCELOSS: Binary Cross Entropy Loss
