---
title: PyTorch Lecture 1：安装 PyTorch
authors: 
  - <PERSON>
date: '2022-07-11'
slug: pytorch-lecture-1-installation
categories:
  - 深度学习
  - PyTorch
tags:
  - 深度学习
  - PyTorch
toc: yes
description: "在 Windows 系统上安装 PyTorch。"
---

# PyTorch Lecture 1：安装 PyTorch

在 Windows 系统上安装 PyTorch。

![image-20220712101909808](index-image/image-20220712101909808.png)

<!-- more -->

## 踩坑之一：安装过程中切换了网络环境

由于我的设备较为落后，且为了安装简便，我首先选择了 CPU 版本的 PyTorch。这可能会使我在运行深度学习项目时的速度会慢一些。

```bash
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

过程中需要下载`torch`等包，我感觉代理网络太慢，就关闭了代理，改成直连。但是，一旦网络发生变化，正在下载的包立即会停止下载，并继续下载后续的包。等所有包都下载完后，又提示我：有一个包还没下载，所以安装失败。

总结：安装过程不要切换网络环境！

## 踩坑之二：不知道如何下载历史版本的 PyTorch

安装 CPU 版本的 PyTorch 失败后，我决定下载 CUDA 版本的。根据电脑显卡的配置，下载对应的 CUDA。我的显卡型号是 NVIDIA GeForce MX150，支持 10.1 版本的 CUDA。

> 安装 CUDA 的过程可以参考[知乎-win10+mx150 环境下安装 cuda，cudnn，pytorch-gpu](https://zhuanlan.zhihu.com/p/131595687)。

官网的安装命令只提供了 CUDA 10.2、11.3 和 11.6 版本的，并且 10.2 版本的在 Windows 上已经不受支持。

![image-20220712100154701](index-image/image-20220712100154701.png)

我向 Datawhale 的同学们求助，很快得到了解决方案：查看历史版本的 PyTorch（https://pytorch.org/get-started/previous-versions/），找到支持 10.1 版本 CUDA 的 PyTorch。

![image-20220712100519248](index-image/image-20220712100519248.png)

查找`CUDA 10.1`，可以看到 v1.7.1 版本的 PyTorch。

![image-20220712100655137](index-image/image-20220712100655137.png)

在 Anaconda Prompt 中输入`conda install pytorch==1.7.1 torchvision==0.8.2 torchaudio==0.7.2 cudatoolkit=10.1 -c pytorch`，等待下载完成，最后提示我`requests`依赖不能被移除，又踩了第三个坑！

## 踩坑之三：没有创建新的 Conda 环境，而是在 base 中安装

![image-20220712100830563](index-image/image-20220712100830563.png)

我又向 Datawhale 的同学们求助，得知需要创建新的环境，在新的环境中安装 PyTorch。这样做的好处是：

- 首先可以避免“`requests`依赖不能被移除”的报错；
- 给 PyTorch 单独新建一个环境，方便后续管理环境中的包；
- 如果本次安装失败，可以之间删除这个新的环境，而不影响其他的环境（如 base）。

## 正确的安装方法

其实课程视频中的方法就是正确的，只不过自己在看视频的时候没有重视。在已经安装好 CUDA 后（之前的踩坑过程已经安装好 CUDA 了），进行如下操作：

0. 在**<u>网络环境稳定</u>**的情况下，**<u>以管理员身份打开</u>** `Anaconda Prompt (miniconda3)`。

1. 新建环境，取名为`PyTorch`。

   ```bash
   conda create -n PyTorch
   ```

2. 从`base`环境切换到`PyTorch`环境中。

   ```bash
   conda activate PyTorch
   ```

3. 输入从 PyTorch 官网复制的安装命令。

   ```
   conda install pytorch==1.7.1 torchvision==0.8.2 torchaudio==0.7.2 cudatoolkit=10.1 -c pytorch
   ```

这样就安装完成了。

在`PyTorch`环境下，打开 Python，运行`import torch`和`print(torch.cuda.is_available())`，显示`True`，说明已经安装成功。

![image-20220712101909808](index-image/image-20220712101909808.png)
