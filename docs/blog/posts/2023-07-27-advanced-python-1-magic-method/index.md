---
title: Python 进阶教程系列 1：双下划线的魔法方法
authors:
  - <PERSON>
date: "2023-07-27"
slug: advanced-python-1-magic-method
categories:
  - Python
tags:
  - Python
links:
  - "Magic Methods & Dunder - Advanced Python Tutorial #1": https://youtu.be/KSiRzuSx120
---

# Python 进阶教程系列 1：双下划线的魔法方法

在构建大型项目时，一些进阶的 Python 开发技术能够让我们编写可复用、可拓展、更优雅、更高效的代码，使得代码清晰、整洁且便于维护。本系列笔记参考了 YouTube [NeuralNine 频道](https://www.youtube.com/@NeuralNine) 的 [Python Advanced Tutorials](https://www.youtube.com/playlist?list=PL7yh-TELLS1FuqLSjl5bgiQIEH25VEmIc) 系列视频，内容涵盖魔法函数、装饰器、生成器、参数解析等内容。

本文是 Python 进阶教程系列 1，主要介绍了双下划线的魔法方法的作用及示例代码，包括`__init__`、`__str__`、`__len__`、`__getitem__`、`__add__`、`__call__` 和 `__format__` 等。

![image-20230724232129477](index-image/image-20230724232129477.png)

<!-- more -->

## 双下划线 Dunder

"Dunder"是"Double Under"（双下划线）方法的缩写。在 Python 中，魔法方法以双下划线开头和结尾，因此有时也被称为"Dunder Methods"。

例如，`__init__`、`__str__`、`__len__`和`__getitem__`等都是 Python 中常见的魔法方法，它们具有特殊的功能和用途。这些方法通过使用双下划线作为前缀和后缀来标识，以便与普通方法区分开来。

## 常见的 Python 魔法方法及其功能

### `__init__`

`__init__`: 初始化方法，用于创建对象并设置其初始状态。

示例代码：

```python
class MyClass:
    def __init__(self, name):
        self.name = name


obj = MyClass("John")
```

### `__str__`

`__str__`: 返回对象的字符串表示形式， ==可通过`str(obj)`或`print(obj)`调用== 。

示例代码：

```python
class MyClass:
    def __init__(self, name):
        self.name = name

    def __str__(self):
        return f"MyClass object with name: {self.name}"


obj = MyClass("John")
print(obj)  # 输出：MyClass object with name: John
```

### `__len__`

`__len__`: 返回对象的长度， ==可通过`len(obj)`调用== 。

示例代码：

```python
class MyList:
    def __init__(self, items):
        self.items = items

    def __len__(self):
        return len(self.items)


my_list = MyList([1, 2, 3, 4, 5])
print(len(my_list))  # 输出：5
```

### `__getitem__`

`__getitem__`: 获取对象的元素， ==可通过索引或切片调用== 。

示例代码：

```python
class MyList:
    def __init__(self, items):
        self.items = items

    def __getitem__(self, index):
        return self.items[index]


my_list = MyList([1, 2, 3, 4, 5])
print(my_list[2])  # 输出：3
print(my_list[1:4])  # 输出：[2, 3, 4]
```

### `__add__`

`__add__`：定义对象之间的加法操作。当 ==使用`+`运算符对两个对象进行相加== 时，解释器会尝试调用对象的`__add__`方法来执行加法操作。

示例代码：

```python
class Point:
    def __init__(self, x, y):
        self.x = x
        self.y = y

    def __add__(self, other):
        if isinstance(other, Point):
            return Point(self.x + other.x, self.y + other.y)
        else:
            raise TypeError("Unsupported operand type: +")


# 创建两个点对象
point1 = Point(1, 2)
point2 = Point(3, 4)

# 使用加法操作符相加
result = point1 + point2

# 打印结果
print(result.x, result.y)
```

通过使用`+`运算符对两个`Point`对象进行相加，解释器会自动调用`point1.__add__(point2)`，并返回结果。在上面的示例中，我们将点`(1, 2)`和点`(3, 4)`相加得到了新的点`(4, 6)`。

### `__call__`

`__call__`：用于使对象可以像函数一样被调用。当对一个对象 ==使用函数调用运算符`()`== 时，解释器会尝试调用对象的`__call__`方法。

示例代码：

```python
class Counter:
    def __init__(self):
        self.count = 0

    def __call__(self):
        self.count += 1
        print(f"Current count: {self.count}")


# 创建一个计数器对象
counter = Counter()

# 调用计数器对象，效果类似于函数调用
counter()  # 输出：Current count: 1
counter()  # 输出：Current count: 2
counter()  # 输出：Current count: 3
```

在上面的示例中，我们定义了一个`Counter`类，它具有一个`count`属性和一个`__call__`方法。在`__call__`方法中，我们将计数器的值加 1，并打印当前的计数值。

通过将对象当作函数进行调用，解释器会自动调用`counter.__call__()`，从而执行`__call__`方法中的逻辑。在上面的示例中，我们通过多次调用`counter()`，实现了计数器的功能。

通过实现`__call__`方法，我们可以使对象具有函数的行为，这在某些情况下非常有用。例如，可以将对象用作可调用的回调函数、装饰器或上下文管理器等。

### `__format__`

参考：[Python——详解\_\_str\_\_, \_\_repr\_\_和\_\_format\_\_](https://zhuanlan.zhihu.com/p/130442206)

## 其他魔法方法

《流畅的 Python》第 1 章中列出了 Python 中魔法方法：

![image-20230724232122893](index-image/image-20230724232122893.png)

!!! note "Python 进阶教程系列文章"

	- [x] [Python 进阶教程系列 1：双下划线的魔法方法](../advanced-python-1-magic-method/)
	
	- [x] [Python 进阶教程系列 2：装饰器](../advanced-python-2-decorator/)
	
	- [x] [Python 进阶教程系列 3：生成器](../advanced-python-3-generator/)
	
	- [x] [Python 进阶教程系列 4：命令行参数](../advanced-python-4-argument-parsing/)
	
	- [x] [Python 进阶教程系列 5：获取和修改被保护的属性](../advanced-python-5-encapsulation/)
	
	- [x] [Python 进阶教程系列 6：使用 `mypy` 进行类型提示](../advanced-python-6-type-hinting/)
	
	- [x] [Python 进阶教程系列 7：工厂模式](../advanced-python-7-factory-design-pattern/)
	
	- [x] [Python 进阶教程系列 8：代理模式](../advanced-python-8-proxy-design-pattern/)
	
	- [x] [Python 进阶教程系列 9：单例模式](../advanced-python-9-singleton-design-pattern/)
	
	- [x] [Python 进阶教程系列 10：组合模式](../advanced-python-10-composite-design-pattern/)