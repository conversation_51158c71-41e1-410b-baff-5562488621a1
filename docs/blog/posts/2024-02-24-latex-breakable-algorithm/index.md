---
title: LaTeX 可跨页的算法环境
authors: 
  - <PERSON>
date: '2024-02-24'
slug: latex-breakable-algorithm
categories:
  - LaTeX
tags:
  - LaTeX
links:
  - "制作一个可跨页的算法样例": https://www.latexstudio.net/index/details/index/mid/933.html
---

# $\LaTeX$ 可跨页的算法环境

本文记录了一个在 $\LaTeX$ 中生成可跨页的算法环境的代码。

## 效果

![image-20240224103632469](index-image/image-20240224103632469.png){width=500px}

<!-- more -->

## 代码

```tex
\documentclass{article}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{algorithm}
\usepackage{algorithmic}

% numbers with circle
% ------------------------------------------------------
\makeatletter
\newenvironment{breakablealgorithm}
{% \begin{breakablealgorithm}
\begin{center} %
\refstepcounter{algorithm}% New algorithm
%\hrule height.8pt depth0pt \kern2pt% \@fs@pre for \@fs@ruled
\hrule height.8pt depth0pt \kern2pt% \@fs@pre for \@fs@ruled
\renewcommand{\caption}[2][\relax]{% Make a new \caption
{\raggedright\textbf{\ALG@name~\thealgorithm} ##2\par}

\ifx\relax##1\relax

\addcontentsline{loa}{algorithm}{\protect\numberline{\thealgorithm}##2}%

\else

\addcontentsline{loa}{algorithm}{\protect\numberline{\thealgorithm}##1}%

\fi

\kern2pt\hrule\kern2pt

}

}{% \end{breakablealgorithm}

\kern2pt\hrule\relax% \@fs@post for \@fs@ruled

\end{center} %

}

\makeatother

% ------------------------------------------------------

\begin{document}

\section{This example of algorithm break}

This example is formulated as Algorithm~\ref{alg:csmr}.

% Algorithmic modifications
\makeatletter

% Renewcommand -----------------------------------------
\renewcommand{\algorithmicrequire}{\textbf{Input:}}
\renewcommand{\algorithmicensure}{\textbf{Output:}}
\newcommand{\algorithmicbreak}{\textbf{break}}
\newcommand{\BREAK}{\STATE \algorithmicbreak}
\newcommand{\algorithmiccontinue}{\textbf{continue}}
\newcommand{\CONTINUE}{\STATE \algorithmiccontinue}

\makeatother

%\begin{algorithm}[htb]

\begin{breakablealgorithm}
   \caption{Algorithm to Calculate the TDR}
   \label{alg:csmr}
   \begin{algorithmic}[1] %[1] denotes show line numbers
       \REQUIRE A tolerance $\delta >$ 0 and a given base point
       (${\bf p}, {\bf q}$).

        \ENSURE Set $R({\bf p}, {\bf q}) = +\infty ,$
        and choose a very large enough set new TDR $W^{\rm B}=\{\Delta {\bf w}|{\bf H}
        \Delta{\bf w}\geq {\bf h}\}$.

         \STATE  ($R({\bf p},{\bf q}) >\delta )$  \textbf{do}:
         solve the following mixed integer linear program (MILP) and then attain the
          optimal solution ${\bf u}^{*}, \Delta \bf w^{*}$ and optimal value ${R}({\bf p}, {\bf q})$

          \STATE $x \leftarrow 1$
          \IF{$n < 0$}
          \STATE $y \leftarrow 1 / x$
          \STATE $M \leftarrow -n$
          \ELSE
          \STATE $Y \leftarrow x$
          \STATE $M \leftarrow n$
          \ENDIF
          \WHILE{$M \neq 0$}
          \IF{$N$ is even}
          \STATE $Y \leftarrow X \times X$
          \STATE $M \leftarrow N / 2$
          \ELSE[$N$ is odd]
          \STATE $x \leftarrow Y \times X$
          \STATE $M \leftarrow N - 1$
          \ENDIF
          \ENDWHILE
          \ENSURE $y = x^n$
          \STATE $y \leftarrow 1$
          \STATE $X \leftarrow x$
          \STATE $N \leftarrow n$
          \WHILE{$N \neq 0$}
          \IF{$N$ is even}
          \STATE $X \leftarrow X \times X$
          \STATE $N \leftarrow N / 2$
          \ELSE[$N$ is odd]
          \STATE $y \leftarrow y \times X$
          \STATE $N \leftarrow N - 1$
          \ENDIF
          \STATE Compute the starting indexes $s_x$ and $s_y$ for the $j$-th image patch, i.e., $s_x = ceil(x_j/33)$ and $s_y = ceil(y_j/33)$;\\
          \STATE $a \leftarrow 1$
          \STATE Compute the ending indexes $e_x$ and $e_y$ for the $j$-th image patch, i.e., $e_x = ceil((x_j+w_j)/33)$ and $e_y = ceil((y_j+h_j)/33)$;\\
          \STATE $i \leftarrow 10$
          \RETURN $i$
          \WHILE{$s>=s_x$ and $s<=e_x$}
          \WHILE{$t>=s_y$ and $t<=e_y$}
          \STATE $p_{st} = 1$;
          \STATE $t = t+1$;
          \ENDWHILE
          \STATE $s = s+1$;
          \ENDWHILE
          \STATE j=j+1;
          \ENDWHILE
          \WHILE{There are channels to be sampled, i.e., $i<=c$}



   % \STATE $//$ Sample image patches in the $i$-th channel.

    \WHILE {$L_{s}^{dn} \leq \alpha \cdot L (t)$ and $i\leq \alpha \cdot Num\_u$ \textbf{do}}

    \WHILE{ $i=i+1
    L_{s}^{dn} =L_{s}^{dn} +\bf{P}_{\bf{\min}} ({\rm {\bf u\_index}} (i))$}


  \STATE $L_{s}^{up} =L_{s}^{up} +\bf{P}_{\bf{\min}} ({\rm {\bf u\_index}} (i))$

 \IF{$p_{st} = 1$}

    \STATE $\bf{T}^{\textbf{dn}} (l, t)=\bf{T}^{\textbf{dn}} (l, t)+\bf{P}_{\bf{\min}} ({\rm {\bf u\_index}} (i))\cdot \bf{G} (l, {\rm {\bf u\_index}} (i))$

    \ELSE

    \STATE Sample the image patch $\textbf{x}_{ist}$ with $\boldsymbol{\Phi}_l$, i.e., $\textbf{y}_{ilst}= \boldsymbol{\Phi}_l \textbf{x}_{ist}$;

    \ENDIF

    \STATE $\bf{T}^{\textbf{up}} (l, t)=\bf{T}^{\textbf{up}} (l, t)+\bf{P}_{\bf{\min}} ({\rm {\bf u\_index}} (i))\cdot \bf{G} (l, {\rm {\bf u\_index}} (i))$

    \ENDWHILE

    \STATE $\bf{P}^{\textbf{dn}} ({\rm {\bf u\_index}} (i), t)=\bf{P}^{\textbf{dn}} ({\rm {\bf u\_index}} (i), t)+\bf{P}_{\bf{\min}} ({\rm {\bf u\_index}} (i))$ 

 \ENDWHILE

 \STATE Concatenate all CS measurements sampled with $\boldsymbol{\Phi}_h$ and $\boldsymbol{\Phi}_l$ respectively, represent the values with 16-bit half-precision floats, and then obtain the half-precision CS measurement $\textbf{y}_{i} = [\textbf{y}_{ih}]_{\textbf{FP16}} \cup [\textbf{y}_{il}]_{\textbf{FP16}}$ for the $i$-th channel;

 \STATE $i=i+1$;

  \ENDWHILE


  \STATE Combine $\textbf{y}_{i}$ sampled with multiple MRs in each channel and then obtain the final half-precision CS measurement $\textbf{y}$.

% Combine $\textbf{y}_{i}$ in each channel and get the sampled CS measurement $\textbf{y}$ for the given image with the measurement matrices $\boldsymbol{\Phi}_h$ and $\boldsymbol{\Phi}_l$.

   \end{algorithmic}
%\end{algorithm}
\end{breakablealgorithm}

$\textbf{u\_index}$ is the unit index, which is ordered
according to the generation cost; $\bf{P}_{\bf{\min}} $ and
$\bf{P}_{\bf{\max}} $ are the minimum and maximum generation
levels, respectively; $f (\cdot) $ is the variable cost function; $Num\_l$ is
the number of transmission lines; $\bf{G}$ is the PTDF matrix; $l$ is
the transmission line index; $t$ is the period index; $\bf{G}_{
\bf{l}}^{\textbf{dn}} \textbf{\_order}$ and $\bf{G}_{
\bf{l}}^{\textbf{up}} \textbf{\_order}$ are the PTDF of lines $l$
from low to high and from high to low, respectively; $\bf{G}_{
\bf{l}}^{\textbf{dn}} \textbf{\_index}$ is the unit index, which is
ordered according to the PTDF of line $l$ from low to high; $\bf{G}_{\bf{l}}^{\textbf{up}}$

After obtaining the results of half-precision CS measurement with Algorithm~\ref{alg:csmr}, they are transmitted to high-performance servers for reconstructing CS images with the DNN-based CS method.

\end{document} 
```

