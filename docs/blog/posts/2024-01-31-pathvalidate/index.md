---
title: 使用 pathvalidate 处理不合法的文件或路径名字符串
authors: 
  - <PERSON>
date: '2024-01-31'
slug: pathvalidate
categories:
  - Python
tags:
  - Python
links:
  - "pathvalidate GitHub 仓库": https://github.com/thombashi/pathvalidate
  - "pathvalidate 官方文档": https://pathvalidate.rtfd.io/
---

# 使用 `pathvalidate` 处理不合法的文件或路径名字符串

在编程时经常需要处理文件和目录的命名，然而直接将字符串用作文件名或路径名时，可能会遇到一个常见问题：字符串中含有特殊字符或保留字，这可能导致在尝试保存文件时出现异常，如无法创建文件、路径解析错误等问题。例如，Windows 系统不允许文件名包含字符如 `\`、`/`、`:`、`*`、`?`、`"`、`<`、`>` 和 `|`。

本文介绍了 `pathvalidate` 库，它提供了一系列实用的函数，用于验证和清理文件名和路径名中的非法字符。这样我们就不必重复造轮子来处理这些特殊字符了。


<!-- more -->

## 安装

首先，需要安装 `pathvalidate`。可以通过以下命令进行安装：

```bash
pip install pathvalidate
```

## 使用示例

### 处理非法字符

导入 `pathvalidate` 后，你可以使用其提供的函数来验证或清理字符串。以下是一些基本的使用示例：

```python
from pathvalidate import sanitize_filename, sanitize_filepath

# 清理文件名中的非法字符
filename = "无效/文件*名？.txt"
clean_filename = sanitize_filename(filename)
print(clean_filename)  # 输出清理后的文件名

# 清理文件路径中的非法字符
filepath = "路径/到/无效*目录？"
clean_filepath = sanitize_filepath(filepath)
print(clean_filepath)  # 输出清理后的文件路径
```

```
无效文件名.txt
路径/到/无效目录
```

### 指定替换非法字符的字符串

`pathvalidate` 默认会将非法字符替换为空字符串 `""`，如果希望用 `_` 等字符进行替换，可以指定参数 `replacement_text='_'`，例如：

```python
from pathvalidate import sanitize_filename, sanitize_filepath

# 清理文件名中的非法字符
filename = "无效/文件*名？.txt"
clean_filename = sanitize_filename(filename, replacement_text="_")
print(clean_filename)  # 输出清理后的文件名

# 清理文件路径中的非法字符
filepath = "路径/到/无效*目录？"
clean_filepath = sanitize_filepath(filepath, replacement_text="_")
print(clean_filepath)  # 输出清理后的文件路径
```

```
无效_文件_名_.txt
路径/到/无效_目录_
```