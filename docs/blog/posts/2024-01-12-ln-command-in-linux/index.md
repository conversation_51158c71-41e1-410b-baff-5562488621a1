---
title: 在 Linux 中软链接至其他文件或目录
authors: 
  - <PERSON> Feng
date: '2024-01-12'
slug: ln-command-in-linux
categories:
  - Linux
tags:
  - Linux
links:
- "Linux 硬链接和软连接的区别与总结": https://www.cnblogs.com/Peter2014/p/7594504.html
---

# 在 Linux 中软链接至其他文件或目录

在 Linux 系统中，我们有时候需要创建一个文件或者目录的快捷方式，也就是软链接。本文介绍了如何在 Linux 中使用命令行创建软链接。

![image-20240114235136966](index-image/image-20240114235136966.png)

<!-- more -->

## 创建软链接

首先，打开你的命令行工具，然后进入到你需要创建链接的目录。在这个目录中，你可以使用 `ln` 命令创建一个软链接。例如，如果你想在当前目录下创建一个链接到 `/root/test` 的链接，并且在当前目录下新建的快捷方式文件夹也叫 `test`，你可以运行以下命令：

```bash
ln -s /root/test test
```

在这个命令中，`-s` 选项指示 `ln` 创建一个软链接。`/root/test` 是你要链接的目标目录，`test` 是在当前目录下创建的链接的名称。

运行这个命令后，你应该可以在当前目录下看到一个叫做 `test` 的链接，它指向 `/root/test`。

## 查看软链接所指向的目录

使用 `ls -l` 查看文件的详细信息，即可展示软链接所指向的目录。

## 使用示例

![image-20240114235136966](index-image/image-20240114235136966.png)

注意到，软链接指向的文件并不会占用磁盘空间。



