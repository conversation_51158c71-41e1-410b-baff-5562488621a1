---
title: 在 Python 中使用正则表达式替换文本
authors: 
  - <PERSON>
date: '2022-11-19'
slug: regular-expression-in-python
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 利用正则表达式高效地处理文本数据。
---

# 在 Python 中使用正则表达式替换文本

利用正则表达式高效地处理文本数据。

## 需求

去除一个字符串中**开头的数字和点号**。

<!-- more -->

## 代码示例


```python
# 导入包
import re
```



```python
string = "1. 示例文本"
print(string)
```

```
## 1. 示例文本
```

### 方法一：先编译正则表达式，再执行


```python
# 编译正则表达式
p = re.compile("(\d*). ")
# 执行替换，将符合正则表达式内容的部分替换成空字符串
new_string_1 = p.sub("", string, count=1)
print(new_string_1)
```

```
## 示例文本
```

- `(\d*). `代表数字（这个数字可以是任意长，如 0 位、个位数、十位数等等）后面跟着`. `。
- `count=1`表示模式匹配后替换的最大次数。
- `count=0`表示替换所有的匹配。

### 方法二：一步直接执行正则表达式匹配替换


```python
new_string_2 = re.sub(r"(\d*). ", "", string)
print(new_string_2)
```

```
## 示例文本
```

