---
title: CS50 Week 8 HTML, CSS, JavaScript
authors:
  - <PERSON>
date: "2022-07-05"
slug: cs50-week-8-html-css-javascript
categories:
  - Computer Science
tags:
  - Computer Science
toc: yes
description: "前端基础知识。"
---

# CS50 Week 8 HTML, CSS, JavaScript

前端基础知识。

<!-- more -->

## 互联网

协议（Protocol）是一套规则，便于计算机之间进行通信。

TCP/IP 是两套协议。

### IP

- IP：Internet Protocol。
- IP 地址是一个计算机在互联网中独一无二的地址。
- IP 地址地格式：`#.#.#.#`。其中的每一个数字都是 0-255 之间的整数。因此，表示一个完整的 IP 地址需要 4 个字节（或 32 bits），这样的 IP 地址是 Version 4 的（IPv4）。IPv6 则用 128 bits 来表示更多的地址。

### TCP

- TCP：Transmission Control Protocol，是一个发送和接受数据的协议。
- TCP 可以用相同的 IP 地址 + 不同的端口来提供不同的服务。例如，HTTP 是端口 80，HTTPS 是端口 443。
- TCP 可以将大型数据分割成小的数据，并给每个小数据贴上序号标签分别进行传送。如果有一部分小数据丢失了，则接收者可以再次请求丢失的数据。如果一直没找到丢失的数据，则整个大的数据都不会被接收。因此，TCP 可以保证数据的完整性。
- UDP 是另一种数据传输协议。它允许接收部分的数据，不能保证数据的完整性，但在直播、实时语音通话等场景下更加方便。因为这些场景允许部分数据的丢失，但不能完全中断连接。如果用 TCP 的话，如果某一个地方断了，那么会一直请求这个丢失的数据，这是不必要的。

### DNS

DNS：Domain Name System。它可以将文本的域名（例如 cs50.harvard.edu）转换为 IP 地址。

## Web

### HTTP

Hypertext Transfer Protocol。可以理解为服务器和浏览器之间通讯的协议。

- HTTPS 是 HTTP 的安全版本，它保证服务器和浏览器之间的数据是加密的。

### URL

Uniform Resource Locator.

`https://www.example.com/`

- `https://`是协议。
- 最后的`/`意味着请求默认的文件，如`index.html`。
- `example.com`是域名。`.com`是顶级域名。其他的`.edu`、`.pro`也是顶级域名。
- `www`是主机名（Hostname），也叫子域名。它指定了服务器。

### 查看浏览器和服务器之间的请求与响应活动

按`F12`进入检查，在“网络”标签页可以查看浏览器从服务器下载的各种资源，以及它们的状态。不同的状态对应的含义如下：

- `200 OK`
- `301 Moved Permanently`
- `302 Found`
- `304 Not Modified`
- `307 Temporary Redirect`
- `401 Unauthorized`
- `403 Forbidden`
- `404 Not Found`
- `418 I'm a Teapot`
  - An April Fool's joke years ago
- `500 Internal Server Error`
  - Buggy code on a server might result in this status code, like segfaults we might have seen in C.
- `503 Service Unavailable`
- …

## HTML

HTML 并不是一门“编程”语言，它只是用文本和标签组成的“结构”语言。

### 基本结构

```html
<!DOCTYPE html>

<html lang="en">
  <head>
    <title>hello, title</title>
  </head>
  <body>
    hello, body
  </body>
</html>
```

### 分段

```html
<p></p>
```

### 标题

```html
<h1></h1>
```

一级标题字号最大，最多可以到六级标题。

### 有序列表和无序列表

有序列表

```html
<ul>
  <li>foo</li>
  <li>bar</li>
  <li>baz</li>
</ul>
```

无序列表只需将`<ul>`换成`<ol>`。

### 表格

```html
<table>
  <thead>
    <tr>
      <th>Name</th>
      <th>Number</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Carter</td>
      <td>+1-617-495-1000</td>
    </tr>
    <tr>
      <td>David</td>
      <td>+1-949-468-2750</td>
    </tr>
  </tbody>
</table>
```

### 图片

```html
<img alt="Harvard University" src="harvard.jpg" />
```

`alt`可以在那些不能正常显示图片的时候，以文字呈现。

### 视频

```html
<video autoplay loop muted width="1280">
  <source src="halloween.mp4" type="video/mp4" />
</video>
```

### 嵌入其他网页

```html
<iframe
  allowfullscreen
  src="https://www.youtube.com/embed/xvFZjo5PgG0"
></iframe>
```

### 超链接

```html
<a href="https://www.harvard.edu">Harvard</a>
```

超链接所显现出的文字并不一定和它真正指向的地址相一致，这使得超链接可以用于网络钓鱼。

### 响应式布局

按`F12`检查网页，可以模拟各种设备（例如手机、平板电脑等）的访问效果。

### © 等特殊符号

用`&#169`可以显示版权符号。

## CSS

Cascading Style Sheets。用于改变网页内容的样式，美化网页。

### 框架

Bootstrap 等框架已经为我们提供了较为美观的样式风格。我们不需要重复写样式的代码，就能实现好看的前端界面。

## JavaScript

### 定义变量

```javascript
let counter = 0;
```

### 赋值

```javascript
counter = counter + 1;
counter += 1;
counter++;
```

### 条件语句

```javascript
if (x < y) {
} else if (x > y) {
} else {
}
```

```javascript
while (true) {}
```

### 循环语句

```javascript
for (let i = 0; i < 3; i++) {}
```

### DOM

Document Object Model，它是当前 HTML 界面的完整的树结构。JavaScript 可以访问和修改 DOM 中的变量。

JavaScript 让整个网页能够变得动态、实时更新。
