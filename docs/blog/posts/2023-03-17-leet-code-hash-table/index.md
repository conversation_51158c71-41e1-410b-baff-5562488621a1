---
title: LeetCode 哈希表
authors: 
  - <PERSON>
date: '2023-03-17'
slug: leet-code-hash-table
categories:
  - LeetCode
tags:
  - LeetCode
links:
  - "代码随想录": https://github.com/youngyangyang04/leetcode-master
---

# LeetCode 哈希表

记录哈希表算法题的常用技巧与解题方法。

<!-- more -->

## 对某个 key 的 value 加 1，若不存在这个 key 则先将这个 key 设为 0 再加 1

```python
count_dict[bar] = count_dict.get(bar, 0) + 1
```

参考：[https://stackoverflow.com/a/2626102/16760102](https://stackoverflow.com/a/2626102/16760102)

## 使用内置的计数器 `collections.Counter()` 快速统计频数

```python
import collections

count_dict = collections.Counter(nums)
```

## 统计频率最高的 k 个数

```python
import collections

count_dict = collections.Counter(nums)
topK = count_dict.most_common(k)
```

用内置的 `collections.Counter().most_common()`，比自己实现排序要方便快捷很多。