---
title: 深度学习 Cheat Sheet
authors: 
  - <PERSON>
date: '2023-06-04'
slug: deep-learning-cheat-sheet
categories:
  - 深度学习
tags:
  - 深度学习
---

# 深度学习 Cheat Sheet

本文整理了深度学习期末考试 Cheat Sheet。内容包括：

- 神经网络基础（激活函数、梯度下降优化方法、参数初始化）
- 前馈神经网络（Dense NN、反向传播算法）
- 卷积神经网络（CNN）
- 循环神经网络（RNN、LSTM、GRU、随时间的反向传播）
- 生成模型（自编码器、变分自编码器、GAN、WGAN）
- Transformer（注意力机制、多头注意力、位置编码）
- 强化学习基础：多臂老虎机（遗憾值、Eoplore Then Commit、Upper Confidence Bound）
- 强化学习进阶：马尔可夫决策过程、动作价值函数、贝尔曼方程、计算状态价值、Q-Learning

PDF 版 Cheat Sheet 在[这里](./deep-learning-cheat-sheet.pdf)。

<!-- more -->

![image-20230604173541745](index-image/image-20230604173541745.png)

![image-20230604173614972](index-image/image-20230604173614972.png)
