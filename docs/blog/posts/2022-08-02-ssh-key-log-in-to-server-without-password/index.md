---
title: 通过 SSH Key 免密登录服务器
authors:
  - <PERSON> Feng
date: "2022-08-02"
slug: ssh-key-log-in-to-server-without-password
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
toc: yes
description: "本文记录了使用 SSH Key 免密登录服务器的过程，可以方便登录公司或个人的远程服务器，无需每次登录都输入密码。"
---

# 通过 SSH Key 免密登录服务器

本文记录了使用 SSH Key 免密登录服务器的过程，可以方便登录公司或个人的远程服务器，无需每次登录都输入密码。

<!-- more -->

## 通过 SSH Key 免密登录服务器

### 在本地生成私钥和公钥

> 本文记录了使用 SSH Key 免密登录服务器的过程，可以方便登录公司或个人的远程服务器，无需每次登录都输入密码。

1. 在客户终端输入命令：（客户终端可以理解为本地终端，注意**不要**在登录服务器后用服务器的终端运行）

```bash
ssh-keygen
```

2. 一直按回车，将会在用户的`.ssh`文件夹中生成两个文件，分别是`id_rsa`和`id_rsa.pub`。

   前者是私钥文件，后者是公钥文件，均可用记事本打开查看，可以看到里面的内容是复杂的字符串。

### 在服务器终端生成 authorized_keys，用于与本地私钥进行匹配

1. 登录服务器（由于还没有成功配置免密登录，所以这一步还是需要用密码）。

2. 在服务器新建`.ssh`文件夹，在`.ssh`文件夹中新建文本文件，文件名为`authorized_keys`。
3. 将本地的`id_rsa.pub`中的文本内容复制到`authorized_keys`中，并保存`authorized_keys`。

### 在服务器终端设置文件和文件夹的读取权限

仍然是在服务器终端，输入两个命令：

```
chmod 600 ~/.ssh/authorized_keys
chmod 700 ~/.ssh
```

可以设置权限：

1. `authorized_keys`只允许拥有者进行读写。
2. `ssh`文件夹设为只允许拥有者进行读写和执行。

### 配置完成

关闭服务器后再次打开服务器，无需密码就可以成功登录了。
