---
title: CS50 Week 0 Scratch
authors:
  - <PERSON> Feng
date: "2022-05-24"
slug: CS50-Week-0-Scratch
categories:
  - Computer Science
tags:
  - Computer Science
toc: true
description: "基础编程知识。"
---

# CS50 Week 0 Scratch

基础编程知识。

<!-- more -->

## 二进制

计算机的最底层都是通过二进制实现的。只需要 0 和 1 的组合，计算机便可以完成如下功能：

- 数学运算。

  利用进制之间的转换即可完成。

- 显示字符。

  规定二进制与字符的对应规则（如`ASCII`，American Standard Code for Information Interchange），在二进制数字前加上前缀（例如`\`）来表示字符甚至 Emoji 等。

- 显示颜色。

  可以用三个位于 0-255 之间的数字分别代表 Red、Green 和 Blue 的比例来表示颜色。例如`RGB(100,150,200)`代表的颜色是<font color=#6597c8>▇</font>。

- 显示图片。

  有了颜色，我们可以对每一个像素（Pixel）赋予一个颜色，将许多像素合并起来就构成了一张图片。

- 显示视频。

  视频无非是多个图片快速播放时的形式。

- 播放音乐。

  可以用数字来量化一段音频的响度、持续时间等。具体的实现细节涉及数字信号和模拟信号的转换。

## 算法

算法是解决问题的方法或步骤。不同算法的计算效率不同。

## 伪代码

- 判断语句用到了布尔值（一个数学家的名字叫 Boole）。

- 循环语句能使用较短的代码进行更多次的运算（Reusable code）。

## Scratch

- 一个图形化编程环境。

- 可以自定义 Block 来编写函数。

- 只需要理解算法的执行思想，而不需要知道太多代码的语法细节，就可以编写很多有趣的程序。
- 非常适合青少年编程教育。
