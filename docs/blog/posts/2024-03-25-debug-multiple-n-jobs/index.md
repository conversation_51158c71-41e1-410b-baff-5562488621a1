---
title: 多线程下调试 Python 代码
authors: 
  - <PERSON>
date: '2024-03-25'
slug: debug-multiple-n-jobs
categories:
  - Python
tags:
  - Python
links:
  - "GitHub issue - 'debug adapter' error in jupyter notebook #1166": https://github.com/microsoft/debugpy/issues/1166#issuecomment-1370062103
  - "GitHub issue - Subprocess support not being disabled #1168": https://github.com/microsoft/debugpy/issues/1168#issuecomment-1377998813
---

# 多线程下调试 Python 代码

当启用 `n_jobs` 超过 1 时，直接调试 Python 代码可能会报错 `"Couldn't find a debug adapter descriptor for debug type 'Python Kernel Debug Adapter' (extension might have failed to activate)"`。

![image-20240325190005418](index-image/image-20240325190005418.png)

本文记录了一个解决方案，可以在 `n_jobs` 超过 1 的多线程环境下调试 Python 代码。


<!-- more -->

参考 [https://github.com/microsoft/debugpy/issues/1168#issuecomment-1377998813](https://github.com/microsoft/debugpy/issues/1168#issuecomment-1377998813) 的回答，首先运行：

```python
sys.modules["debugpy"].__file__
```

找到 `debugpy` 的路径。

在 `debugpy/server/api.py` 中，在代码的开头位置，就有：

```python
_config = {
    "qt": "none",
    "subProcess": True,
    "python": sys.executable,
}
```

我们需要修改为：

```python
_config = {
    "qt": "none",
    "subProcess": False,
    "python": sys.executable,
}
```

![image-20240325190548326](index-image/image-20240325190548326.png)
