---
title: Typora 设置默认代码语言
authors: 
  - <PERSON>
date: '2023-02-28'
slug: typora-default-language
categories:
  - Computer Science
tags:
  - Computer Science
links:
  - "AutoHotkey 官网": https://www.autohotkey.com/
  - "下载 AutoHotkey": https://autohotkey.com/download/ahk-install.exe
  - "怎么给 Typora 的代码块设置默认语言？": https://codeantenna.com/a/HLkBHeFGtT
  - "Typora 设置代码块的默认编程语言": https://blog.csdn.net/vision666/article/details/122645915
---

# Typora 设置默认代码语言

在 Typora 中可以插入代码块，但每次都需要手动添加语言。若经常需要插入同一种语言的代码块，可以借助第三方的快捷键工具 AutoHotkey，自动设置代码块的语言，提高工作效率。

<!-- more -->

## 步骤

1. 点击下载 [ahk](https://autohotkey.com/download/ahk-install.exe)。

2. 新建 `.txt` 文本文件，将以下代码粘贴到文本内。

    ```
    #IfWinActive ahk_exe Typora.exe
    {
        ; Ctrl+Alt+K javaCode    
        ; crtl 是  ^ , shift 是 + , k 是  k 键
        ^+k::addCodeJava()
    }
    addCodeJava(){
    Send,{```}
    Send,{```}
    Send,{```}
    Send,python
    Send,{Enter}
    Return
    }
    ```

3. 保存文件，并将后缀改为`.ahk`。

4. 双击启动`.ahk`文件。

## 注意事项

准备输入代码块之前，需要将输入法切换到英文。若在中文输入法下，输入的是` ··· `，而不是 ` ``` `。

