---
title: 在 pypika 中使用 Criterion.all() 的注意事项
authors: 
  - <PERSON>
date: '2024-08-16'
slug: pypika-criterion-all
categories:
  - Python
  - 数据库
tags:
  - Python
  - 数据库
links:
  - "本文代码": https://github.com/jeremy-feng/scripts/blob/master/pypika-criterion-all.ipynb
---

# 在 `pypika` 中使用 `Criterion.all()` 作为筛选条件占位符及其注意事项

`pypika` 能够通过编程方式动态生成 SQL 查询。本文介绍了 `Criterion.all()` 作为筛选条件占位符及其注意事项。


<!-- more -->

## 什么是 `Criterion.all()`？

`Criterion.all()` 是 `pypika` 提供的一个特殊的条件，它表示所有条件均为真。

例如，下面的代码中，用 `Criterion.all()` 将多个筛选条件结合起来，不需要手动编写 `AND` 了。

```python hl_lines="8-14"
from pypika import Criterion

customers = Table("customers")
q = (
    Query.from_(customers)
    .select(customers.id, customers.fname)
    .where(
        Criterion.all(
            [
                customers.is_registered,
                customers.age >= 18,
                customers.lname == "Jones",
            ]
        )
    )
)
```

```sql
SELECT "id","fname" FROM "customers" WHERE "is_registered" AND "age">=18 AND "lname"='Jones'
```

## `Criterion.all()` 作为筛选条件占位符

如果我们直接使用 `Criterion.all()`，并且括号里没有任何查询条件，相当于该条件对查询结果不施加任何过滤。这在某些动态生成查询的场景中非常有用。

让我们通过一个简单的代码示例来理解 `Criterion.all()` 的使用：

```python hl_lines="10"
from pypika import Criterion, Query, Table

# 定义表
users = Table("users")

filter_boy = False
if filter_boy:
    gender_filter = users.gender == "boy"
else:
    gender_filter = Criterion.all()
```

在这个示例中，首先定义了一个名为 `users` 的表。然后，根据 `filter_boy` 变量的值，动态生成查询条件。如果 `filter_boy` 为 `True`，则性别过滤条件为 `users.gender == "boy"`；否则，性别过滤条件为 `Criterion.all()`，即不过滤性别条件。

随后，代码继续构建查询：

```python hl_lines="5"
query = (
    Query.from_(users)
    .select(users.name)
    .where(
        gender_filter & (users.age == 18),
    )
)

print(query)
```

```sql
SELECT "name" FROM "users" WHERE "age"=18
```

此查询从 `users` 表中选择名字字段，并根据 `gender_filter` 和年龄为 18 的条件进行过滤。

这个示例表明，`Criterion.all()` 可以作为一个占位符，我们可以将条件设置为 `Criterion.all()`，从而有效地跳过该条件的过滤作用。

## 错误代码示例及注意事项

以下是一个导致报错的代码示例：

```python hl_lines="5"
query = (
    Query.from_(users)
    .select(users.name)
    .where(
        (users.age == 18) & gender_filter,
    )
)

print(query)
```

我们将 `gender_filter` 放到 `(users.age == 18)` 的后面，会报错：

```
TypeError: get_sql() got an unexpected keyword argument 'subcriterion'
```

==经测试，`Criterion.all()` 只能放在 `where` 语句的开头，而不能放在某个条件语句的后面。==
