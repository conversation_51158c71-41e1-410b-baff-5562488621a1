---
password: 'fc'
title: 基于分钟收益率分布的高频因子
authors: 
  - <PERSON> Feng
date: '2023-05-14'
slug: stock-factor-based-on-minute-return
categories:
  - 量化研究
tags:
  - 量化研究
hide:
  - toc
---

# 基于分钟收益率分布的高频因子

## 摘要

<!-- more -->

本文参考海通证券《行业轮动系列研究 9——高频数据在行业轮动中的应用》，基于个股分钟频收益率数据，构造已实现偏度和下行波动占比的日频因子。

使用 2021 年 1 月 1 日至 2022 年 12 月 31 日的全部 A 股数据进行实证检验后发现，已实现偏度因子和下行波动占比因子的分组收益并不单调，均呈现出 ==“中间组收益高、两边组收益低”的== 特点。本文对此现象进行分析后，将原始已实现偏度因子和下行波动占比因子进行 ==“去均值化后取绝对值，再取相反数”== 的非线性变换，使得变换后的因子呈现更加单调的分组收益。回测结果显示，变换后的已实现偏度因子和下行波动占比因子的 IC 均达到 $4\%$，多空对冲组合年化收益率分别为 $5.51\%$ 和 $6.03\%$。

然而，将多空对冲组合的 IC 拆解后发现：多空对冲组合的 IC 主要由空头部分贡献负 IC 所得，多头部分几乎没有正 IC。考虑到 A 股市场难以实施做空，因此多头组合的表现对实际投资更具有指导意义。本文构造了 ==基于日内极端低收益率出现频率的新因子== ，分析了其与上述已实现偏度因子和下行波动占比因子的逻辑区别与低相关性，并实证检验了该因子在多空对冲组合年化收益率为 $4.66\%$，且主要来自多头部分。

## 因子表达式与逻辑

相比于日频量价数据，分钟频数据中蕴涵了更高频的投资者交易行为信息。许多研究基于分钟频数据，从动量、反转、知情交易、行为金融等角度构造量价因子。本文参考的研究报告《行业轮动系列研究 9——高频数据在行业轮动中的应用》就是基于分钟频收益率数据，通过计算已实现偏度和下行波动占比这两个统计量，试图衡量标的资产在短期内价格跳动的程度。

基于股票 $i$ 的高频收益序列 $\left\{r_{i j}\right\}$，已实现偏度因子和下行波动占比因子的计算方法如下所示：
$$
\text{已实现偏度因子} = \frac{\sqrt{N} \sum_{j=1}^N r_{i j}^3}{\left(\sum_{j=1}^N r_{i j}^2\right)^{3 / 2}}
$$

$$
\text{下行波动占比} = \frac{\sum_{r_{i j}<0} r_{i j}^2}{\sum_{j=1}^N r_{i j}^2}
$$

已实现偏度因子和下行波动占比因子虽使用不同的统计量，但其核心逻辑一致，均是试图衡量标的资产在短期内价格跳动的程度。

1. 对于已实现偏度因子，负偏表明标的资产价格在该段时间出现了断崖式下跌；反之，正偏表示标的资产价格在该段时间出现了急速拉升。

2. 对于下行波动占比，下行波动占比较高表示标的资产价格在该段时间内较容易出现断崖式下跌；反之，下行波动占比较低表示标的资产价格在该段时间内较容易出现急速拉升。

偏度越低、下行波动占比越高，表明股票市场下跌风险越大，为什么股票在末来一个月的表现越好？为了解释已实现偏度的预测能力，有研究发现己实现偏度减小，即市场下跌风险增加，股票市场的交易活跃程度减弱，表现为换手率降低和流动性减弱。而预期的市场交易活跃程度下降导致正的风险溢价，即较高的股票市场超额收益率。（陈坚等，2018）[^1]

基于上述逻辑，本文对构造的因子进行测试时就需要思考：当标的资产价格出现了断崖式下跌或急速拉升时，我们应该预期标的资产未来一段时间会上涨或是下跌？若因子的分组测试结果单调，则上述逻辑能直接解释的股价表现；若因子的分组测试结果不单调，例如中间高、两边低的情况，则可以考虑在短期内价格较为平稳的标的资产在未来表现更好。

## 回测参数说明

数据范围：全体 A 股，平均每个交易日约 4470 只股票。

回测区间：2021 年 1 月 1 日至 2022 年 12 月 31 日，共 2 年。

分组方式：将所有股票按因子值从小到大排序后等分为 5 组，每组内等权配置个股，共构造 5 个投资组合。

构造多空对冲组合：做多第 5 组（因子值最大的 $20\%$ 股票）、做空第 1 组（因子值最小的 $20\%$ 股票）。

调仓频率：日频调仓。

交易费用：不计交易费用。

## 准备数据

构造本文因子的整体步骤为：使用分钟频的收益率数据，使用特定算符将其降频为日频因子。具体来说，在每个交易日，每只股票有 240 个分钟收益率观测值，使用计算已实现偏度、下行波动占比和日内极端低收益率出现频率的算符得到该股票在当日的日频因子值。

高频数据的存储、读取和计算需要消耗大量存储空间和计算时间。分钟频数据集占用磁盘空间较大：对于 2 年期 A 股全市场所有股票，以 `.csv` 格式存储开、高、收、低、量、额 6 个原始量价字段的分钟频数据，需占用约 30GB 的磁盘空间。若一次性读取回测期内所有股票的所有字段数据，将会使用大量内存，且对全量数据计算因子时的速度也较慢。

为了优化数据存储、编写算符、调试算符、批量计算因子和因子绩效评价等工程实践流程，本文使用微软开源的 Qlib 框架，使各因子的研究和生产流程更加标准和统一。Qlib 在量化研究流程中具有许多优势，例如：

1. 高效的数据存储方式。Qlib 使用二进制格式的 `.bin` 文件，按照每只股票的每个字段存储数据。`.bin` 文件具有读写速度快、节省存储空间等优势。在本研究中，原 `.csv` 格式的文件大小约为 30 GB，将其导入到 Qlib 数据文件夹后的文件大小仅 12 GB，且没有丢失任何有效信息。
2. 标准化的数据处理流程。在因子开发的过程中，我们只需要编写算符和处理的类，分别实现在时间序列和截面上的数据处理流程。例如，若要计算已实现偏度因子，只需要编写自定义算符，将一只股票在一天的分钟收益率数据转换为该股票在该日的已实现偏度，无需考虑分组计算、汇总合并、数据拼接等通用的数据处理过程。
3. 使用并行计算的后端框架加速计算因子。Qlib 默认使用 `joblib` 作为并行计算的后端框架，我们无需额外编写并行计算的代码，就可以最大化利用计算资源，并行地计算各股票的因子值。在笔者的 Macbook Pro M1 Pro 16G 内存版本的机器上，计算因子的全流程需要大约 7 分钟，CPU 利用率达 $80\%$。

将`.csv` 格式的文件导入为 Qlib 支持的 `.bin` 格式的文件的命令如下：

```bash
python qlib/scripts/dump_bin.py dump_all --csv_path  csv_data_dir_path --qlib_dir ~/.qlib/qlib_data/cn_data --include_fields open,high,low,close,volume,amount --date_field_name datetime --freq 1min
```

转换后的分钟频数据将存储在 `~/.qlib/qlib_data/cn_data` 目录下：

<img src="index-image/qlib-data-4076466.png" alt="qlib-data" style="zoom:50%;" />

## 计算 realized_skew 和 down_proportion 因子

编写 `RealizedSkew` 和 `DownProportion` 算符，计算每只股票在每个交易日的已实现偏度因子和下行波动占比因子。

```python
class RealizedSkew(Resample):
    def __init__(self, feature, freq):
        """
        已实现偏度

        Parameters
        ----------
        feature : Expression
            An expression for calculating the feature
        freq : str
            It will be passed into the resample method for resampling basedn on given frequency
        """
        self.feature = feature
        self.freq = freq

    def __str__(self):
        return "{}({}, {})".format(type(self).__name__, self.feature, self.freq)

    def _calculate_realized_skew(self, series):
        # 计算已实现偏度的分子
        numerator = np.sqrt(series.shape[0]) * np.power(series, 3).sum()
        # 计算已实现偏度的分母
        denominator = np.power(series, 2).sum() ** 1.5
        # 计算已实现偏度
        return numerator / denominator

    def _apply(self, obj):
        return obj.agg(self._calculate_realized_skew)


class DownProportion(Resample):
    def __init__(self, feature, freq):
        """
        下行波动占比

        Parameters
        ----------
        feature : Expression
            An expression for calculating the feature
        freq : str
            It will be passed into the resample method for resampling basedn on given frequency
        """
        self.feature = feature
        self.freq = freq

    def __str__(self):
        return "{}({}, {})".format(type(self).__name__, self.feature, self.freq)

    def _calculate_down_proportion(self, series):
        # 计算下行波动占比的分子
        numerator = np.power(series[series < 0], 2).sum()
        # 计算下行波动占比的分母
        denominator = np.power(series, 2).sum()
        # 计算下行波动占比
        return numerator / denominator

    def _apply(self, obj):
        return obj.agg(self._calculate_down_proportion)
```

## realized_skew 和 down_proportion 因子回测结果

 参照海通证券的研究报告，本文对已实现偏度因子和下行波动占比因子进行滚动 20 个交易日求均值，并预测未来 20 个交易日的收益率。

下图展示了已实现偏度因子预测未来 20 个交易日的收益率的回测表现。从分组回测可以看出，已实现偏度因子在最近 2 年的因子表现并不具有单调性，具体体现为：因子值过大或过小的股票，在未来表现欠佳；因子值处于中间组的股票，在未来表现更好。

![image-20230514155758403](index-image/image-20230514155758403-4076466.png)

![image-20230514223040141](index-image/image-20230514223040141-4076466.png)

对于下行波动占比因子，我们同样可以观察到类似的模式：构造因子值过大或过小的股票投资组合的收益更低，而构造因子值处于中间水平的股票投资组合的收益更高。

![image-20230514154933822](index-image/image-20230514154933822-4076466.png)

![image-20230514223115087](index-image/image-20230514223115087-4076466.png)

基于以上初步回测的观察，我们推测：已实现偏度因子和下行波动占比因子对未来 20 个交易日的收益率预测可能并不具有单调性，我们可以考虑通过非线性变换的方式，将这两个原始因子转换为与收益率具有更高线性相关的新因子。

## 计算非线性变换后的因子 realized_skew_abs_demean 和 down_proportion_abs_demean

我们考虑使用“减去均值后取绝对值，再取相反数”的非线性变换，将原始的已实现偏度因子和下行波动占比因子转换为新因子：
$$
-\left| x - CsMean(x) \right|
$$
其中，$CsMean()$ 代表对同一截面上所有股票的因子值求均值。

实现该非线性变换的处理类的代码如下：

```python
class CSAbsDemeanNewField(processor.Processor):
    """
    Minus the cross-sectional mean of the field, and then take the absolute value.

    """

    def __init__(self, groups=None, fields=None, new_fields: "tuple | list" = None):
        """

        Args:
            groups (:obj:`str` or :obj:`list`, optional): (Effective for MultiIndex columns)
                Subset groups of fields to process. It is the first level values of the column index.
                Will process all columns if not specified, or if the column index is not a MultiIndex.
            fields (:obj:`str`, :obj:`tuple` or :obj:`list`, optional): Field indexes to process.
                Note that if column index is a MultiIndex and `groups` is specified, then these are
                the second level values of the column index. Will process all fields (in `groups`
                for MultiIndex) if not specified.
            new_fields (:obj:`str`, :obj:`tuple` or :obj:`list`, optional): New field names.

        """
        self.groups = groups
        self.fields = fields
        self.new_fields = new_fields

    def transform(self, df):
        fields = self.get_transform_columns(df)
        for i, f in enumerate(fields):
            df[self.new_fields[i]] = (
                df[f].groupby("datetime").apply(lambda x: abs(x - x.mean()))
            )
        # 重新为列排序
        df.sort_index(axis=1, inplace=True)
        return df
```

## realized_skew_abs_demean 和 down_proportion_abs_demean 因子回测结果

经过上述非线性变换后得到新因子，使用新因子预测未来 20 个交易日的收益率。

从下图可以看出，已实现偏度因子在经过非线性变换后的 IC 为 $4\%$，ICIR 约为 $1$。相比原始已实现偏度因子，经过上述非线性变换后得到的已实现偏度因子分组收益率具有更强的单调性，多空对冲组合收益率更平缓。

> 注意：由于回测时采用日度调仓，而使用的预测标签为未来 20 个交易日的收益率，因此组合净值并不代表回测时投资组合的收益，但其单调性和平稳性仍能反映因子的选股能力。

![image-20230514165441999](index-image/image-20230514165441999-4076466.png)

![image-20230514185615938](index-image/image-20230514185615938-4076466.png)

对于下行波动占比因子，经过非线性变换后的因子也取得了更稳定的多空对冲收益，分组收益率也呈现出单调性。

![image-20230514184305351](index-image/image-20230514184305351-4076466.png)

![image-20230514185439703](index-image/image-20230514185439703-4076466.png)

## 因子回测结果解读

本文对已实现偏度因子和下行波动占比因子的回测结果解读如下：

1. 已实现偏度因子和下行波动占比因子过大或过小，均反映出标的资产价格出现了较多下跌或上涨的行情。经实证检验发现，原始已实现偏度因子和下行波动占比因子的分组收益呈现出“中间组收益高、两边组收益低”的特点。这一结果说明，股票在出现这两种极端行情时，未来收益表现均较差，而股票在呈现出平稳行情时，未来收益表现更佳。
2. 本文将原始已实现偏度因子和下行波动占比因子进行非线性变换，即：减去均值后取绝对值，再取相反数。所得的 2 个新因子均能够取得更稳定的多空对冲收益，分组收益率也呈现出单调性。

虽然新因子的多空对冲组合表现较为理想，但将其 IC 值拆解为多头部分和空头部分后发现，绝大部分 IC 均由空头部分贡献负 IC 所得，而多头部分几乎没有正 IC。这意味着，当股票在呈现出平稳行情时（对应因子值较高的股票，即多空对冲组合的多头部分），我们并不能很好地预测其今后的表现；当股票在呈现出极端行情时（对应因子值较小的股票，即多空对冲组合的空头部分），我们预期其今后会出现下跌。

## 基于日内极端低收益率出现频率的新因子 exRtn_minFre

上节分析表明，若仅做多因子值较大的 $20\%$ 股票，投资者则并不能取得打败市场的收益。在做空限制较多的 A 股市场，我们更希望因子在多头部分取得正收益。因此，有必要从其他角度刻画极端行情。

因此，本文从极端收益出现的次数出发，构建了基于日内极端低收益率出现频率的新因子 exRtn_minFre：一只股票在当天出现极端小的分钟收益率的次数。极端低收益率出现的定义是该分钟收益率小于当天分钟收益率的均值减去 1.96 倍当天分钟收益率的标准差。因此该因子的表达式为：
$$
Count(ret_i<Mean(Ret)-1.96*Std(Ret))
$$
exRtn_minFre 衡量了一只股票在某日出现快速下跌的次数，其值越大，说明该股票经常出现快速下跌的行情。

与前文构造的已实现偏度因子和下行波动占比因子不同的是，exRtn_minFre 因子衡量的是“快速下跌的次数”，而前文两个因子衡量的是“下跌的程度”，其不仅包括“快速下跌”，也包括“缓慢下跌”。当一只股票“缓慢持续”地下跌，投资者可能会把它卖掉。但当一只股票只是短暂地快速下跌（对应 exRtn_minFre 因子值处于中间水平），让投资者在短时间内产生大量亏损，这可能会让他变得“风险偏好”，也就是继续持有或补仓。

基于上述逻辑，我们预期 exRtn_minFre 因子能在多头部分取得正 IC，从而更具有投资实践意义。

## exRtn_minFre 因子回测结果

下图展示了 exRtn_minFre 因子的回测结果。可以看出，exRtn_minFre 因子的多空对冲年化收益与前文构造的已实现偏度因子和下行波动占比因子相近，但其多头部分收益显著更高，为多空对冲组合贡献了正 IC。

![image-20230514222313286](index-image/image-20230514222313286-4076466.png)

![image-20230514223510252](index-image/image-20230514223510252-4076466.png)

## 三个因子的相关性

下表展示了本文构造的三个因子的相关性，可以看出，经过非线性变换后的已实现偏度因子和下行波动占比因子相关性高达 $87\%$，而新构造的 exRtn_minFre 因子与前两个因子的相关性很低，仅为 $-6\%$ 和 $-2\%$。

|                            | realized_skew_abs_demean | down_proportion_abs_demean | exRtn_minFre_abs_demean |
| -------------------------- | ------------------------ | -------------------------- | ----------------------- |
| realized_skew_abs_demean   | 1                        | 0.87                       | -0.06                   |
| down_proportion_abs_demean | 0.87                     | 1                          | -0.02                   |
| exRtn_minFre_abs_demean    | -0.06                    | -0.02                      | 1                       |

## 所有多空对冲组合的回测绩效指标

realized_skew 因子：

| metric             | 2021   | 2022   | total  |
| ------------------ | ------ | ------ | ------ |
| annual_return      | 2.24%  | -1.11% | 0.61%  |
| annual_volatility  | 0.89%  | 1.01%  | 0.96%  |
| calmar_ratio       | 5.8    | -0.58  | 0.32   |
| count              | 234    | 222    | 456    |
| information_ratio  | 2.5    | -1.1   | 0.64   |
| max_drawdown       | -0.39% | -1.90% | -1.90% |
| profit_loss_ratio  | 1.07   | 0.83   | 0.92   |
| winning_percentage | 58.97% | 50.00% | 54.61% |

down_proportion 因子：

| metric             | 2021   | 2022   | total  |
| ------------------ | ------ | ------ | ------ |
| annual_return      | -1.26% | 1.94%  | 0.30%  |
| annual_volatility  | 1.08%  | 1.17%  | 1.12%  |
| calmar_ratio       | -0.54  | 3.32   | 0.13   |
| count              | 234    | 222    | 456    |
| information_ratio  | -1.18  | 1.66   | 0.26   |
| max_drawdown       | -2.33% | -0.58% | -2.33% |
| profit_loss_ratio  | 1.02   | 1.16   | 1.1    |
| winning_percentage | 44.44% | 53.15% | 48.68% |

realized_skew_abs_demean 因子：

| metric             | 2021   | 2022   | total  |
| ------------------ | ------ | ------ | ------ |
| annual_return      | 4.11%  | 6.99%  | 5.51%  |
| annual_volatility  | 0.55%  | 0.52%  | 0.54%  |
| calmar_ratio       | 11.32  | 49.4   | 15.16  |
| count              | 234    | 222    | 456    |
| information_ratio  | 7.47   | 13.49  | 10.16  |
| max_drawdown       | -0.36% | -0.14% | -0.36% |
| profit_loss_ratio  | 1.65   | 2.15   | 1.82   |
| winning_percentage | 66.67% | 82.43% | 74.34% |

down_proportion_abs_demean 因子：

| metric             | 2021   | 2022   | total  |
| ------------------ | ------ | ------ | ------ |
| annual_return      | 4.34%  | 7.81%  | 6.03%  |
| annual_volatility  | 0.59%  | 0.55%  | 0.58%  |
| calmar_ratio       | 8.96   | 34.27  | 12.45  |
| count              | 234    | 222    | 456    |
| information_ratio  | 7.4    | 14.1   | 10.37  |
| max_drawdown       | -0.48% | -0.23% | -0.48% |
| profit_loss_ratio  | 1.58   | 2.53   | 1.9    |
| winning_percentage | 68.38% | 81.53% | 74.78% |

exRtn_minFre_abs_demean 因子：

| metric             | 2021   | 2022   | total  |
| ------------------ | ------ | ------ | ------ |
| annual_return      | 5.75%  | 3.52%  | 4.66%  |
| annual_volatility  | 0.87%  | 0.60%  | 0.75%  |
| calmar_ratio       | 4.94   | 4.1    | 4      |
| count              | 234    | 222    | 456    |
| information_ratio  | 6.58   | 5.91   | 6.18   |
| max_drawdown       | -1.17% | -0.86% | -1.17% |
| profit_loss_ratio  | 1.71   | 1.39   | 1.57   |
| winning_percentage | 65.81% | 65.77% | 65.79% |

## 总结与讨论

本文参考海通证券《行业轮动系列研究 9——高频数据在行业轮动中的应用》，基于个股分钟频收益率数据，构造已实现偏度和下行波动占比的日频因子。将原始已实现偏度因子和下行波动占比因子进行非线性变换，使得变换后的因子呈现更加单调的分组收益。回测结果显示，变换后的已实现偏度因子和下行波动占比因子的 IC 均达到 $4\%$，多空对冲组合年化收益率分别为 $5.51\%$ 和 $6.03\%$。

为了使多空对冲组合更具实际投资意义，构造了基于日内极端低收益率出现频率的新因子，实证检验了该因子在多空对冲组合年化收益率为 $4.66\%$，且主要来自多头部分。

本文研究还有许多不足，主要体现为：

1. 所用数据长度有限。由于分钟频数据集规模较大，本文只使用了 2021 年至 2022 年共两年的数据构造因子并进行回测。股票市场在不同年份的风格可能不同，后续可以考虑使用更长的回测期进行更完善的检验。
2. 未考虑将因子对风格因子中性化。囿于时间有限，本文没有获取日频的风格因子，后续可考虑导入日频 Barra 风格因子后，分析各因子对风格因子中性化后的表现。
3. 构造投资组合时未考虑市值影响。本文在构建回测投资组合时采用简单等权方式，会对小市值风格有更多的暴露。后续可导入个股市值数据，采用市值加权的方式构建回测投资组合。
4. 未考虑交易费用等回测细节。为便于分析，本文在构建回测投资组合时未考虑交易费用等细节，所得回测结果与实际投资会有偏差。

[^1]: 陈坚，张铁凡，中国股票市场的己实现偏度与收益率预测。金融研究，2018, (09), 107-125)

