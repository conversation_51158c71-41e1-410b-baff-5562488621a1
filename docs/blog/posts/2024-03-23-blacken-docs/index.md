---
title: 使用 blacken-docs 对文档中的 Python 代码进行格式化
authors: 
  - <PERSON>
date: '2024-03-23'
slug: blacken-docs
categories:
  - Python
tags:
  - Python
links:
  - "blacken-docs GitHub 仓库": https://github.com/adamchainz/blacken-docs
---

# 使用 `blacken-docs` 对文档中的 Python 代码进行格式化

`ruff` 等代码格式化工具可以美化 Python 代码，但是不方便美化文档文本中的 Python 代码。如果想要格式化 markdown、字符串文档等中间的 Python 代码，经常需要手动地去统一格式。

本文介绍了 `blacken-docs` 这款工具，它可以轻松实现对文档中的 Python 代码进行格式化。


<!-- more -->

## 安装

```
python -m pip install blacken-docs
```

## 格式化某个文档

以本博客中的文档为例：

```
blacken-docs ./docs/code/pandas.md
```

如果文档中的代码有错误（而不仅仅是格式的问题），那么它会提示 `code block parse error Cannot parse:`，例如：

![image-20240323232310953](index-image/image-20240323232310953.png)

我们需要手动修复这些错误。（感谢 `blacken-docs`，不然我自己也不知道之前写的代码是有错误的），然后就可以格式化文档了：

![image-20240323232701232](index-image/image-20240323232701232.png)

效果还是很棒的，常见的格式化问题都能自动修复：

![image-20240323232748280](index-image/image-20240323232748280.png)

## 设置 pre-commit

`.pre-commit-config.yaml` 为：

```yaml
repos:
  - repo: local
    hooks:
      - id: blacken-docs
        name: blacken-docs
        description: Run `black` on python code blocks in documentation files
        entry: blacken-docs
        language: python
        files: '\.(rst|md|markdown|py|tex)$'
```

之后每次提交修改文件时，`blacken-docs` 都会帮我们格式化文档中的代码。

## 格式化所有文档

```bash
pre-commit run blacken-docs --all-files
```

如果文档中没有出现解析错误问题，那么所有文档都会被自动格式化。

![image-20240323235220387](index-image/image-20240323235220387.png)

