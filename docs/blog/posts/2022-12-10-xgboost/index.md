---
title: XGBoost
authors:
  - <PERSON>
date: "2022-12-10"
slug: xgboost
categories:
  - 机器学习
tags:
  - 机器学习
toc: yes
katex: yes
description: 推导 XGBoost 中的结构分数以及理解它对于构建 XGBoost 模型的作用。转载一篇写得非常棒的介绍 XGBoost 的文章，真正的通俗易懂。
links:
  - "通俗理解 kaggle 比赛大杀器 xgboost": https://blog.csdn.net/v_JULY_v/article/details/81410574
---

# XGBoost

推导 XGBoost 中的结构分数以及理解它对于构建 XGBoost 模型的作用。转载一篇写得非常棒的介绍 XGBoost 的文章，真正的通俗易懂。

<!-- more -->

## XGBoost 中的结构分数

![image-20221210213804467](index-image/image-20221210213804467.png)

![image-20221210213820017](index-image/image-20221210213820017.png)

![image-20221210213827756](index-image/image-20221210213827756.png)

## 学习资料

转载一篇写得非常棒的 XGBoost 文章，真正的通俗易懂。

文章作者是一家人工智能在线教育机构的创始人，向他热心的分享致敬。

引用一句文章中特别喜欢的一句话：

> 当你学习某个知识点感觉学不懂时，十有八九不是你不够聪明，十有八九是你所看的资料不够通俗、不够易懂。

<iframe  
 height=600 
 width=100% 
 src="https://blog.csdn.net/v_JULY_v/article/details/81410574"  
 frameborder=0  
 allowfullscreen>
 </iframe>
