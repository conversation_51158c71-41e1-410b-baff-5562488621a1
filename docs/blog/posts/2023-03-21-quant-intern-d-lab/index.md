---
password: 'fc'
title: 量化实习工作总结
authors: 
  - <PERSON> Feng
date: '2023-03-21'
slug: quant-intern-d-lab
categories:
  - 量化研究
tags:
  - 量化研究
---

# 量化实习工作总结

总结因子开发与多因子机器学习模型的工作。

<!-- more -->

## 问题背景

多因子模型是一个经典的资产定价模型，在量化研究中通常用于定量分析不同股票特征对股票未来收益的影响。近年来，随着因子投资的兴起，因子拥挤度不断提高，历史长期有效的因子逐渐失效，应用传统数据和模型对因子进行挖掘正逐渐饱和。如何不断迭代、更新对未来收益具有较强预测能力且相关性低的因子，是量化资产管理的重要课题。

在此背景下，我于实习期间完成了关于股票因子开发与合成的若干课题，包括：基于日频和分钟频量价数据的因子开发、基于风格相关性的衍生因子模型、基于 SHAP 的特征归因、指定单棵决策树的分裂特征、自定义 lightGBM 的损失函数等。

## 数据介绍

实习期间开发的因子均基于日频和分钟频量价数据，包括分钟收益率、分钟成交量、日收盘价等。因子回测标的为全市场所有股票，回测区间均为 2018 年 1 月 1 日至 2022 年 10 月 31 日。

<script type="module">
  import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
  mermaid.initialize({ startOnLoad: true });
</script>
<div class="mermaid">
graph TD;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
</div>

## 因子开发

### 因子开发流程

为统一数据的输入与输出格式、使因子测试结果可复现，我使用 iqlib 投研框架开发因子，关键的开发步骤为：

1. 配置数据源、回测对象、回测区间、用于对因子进行中性化的风格因子、预测标签等。

2. 使用 iqlib 中已有的运算符（operator），或者根据因子的构造逻辑自定义新的运算符，实现对原始数据在时间序列上的计算。典型的时间序列运算有：重采样计算平均值 `RsMean()`，时间序列上的标准化 `RsZscore()` 等。

3. 使用 iqlib 中已有的处理类（processor），或者根据因子的构造逻辑自定义新的处理类，实现对原始数据在横截面上的计算。典型的横截面运算有：横截面排序 `CSRank()`，横截面中性化 `CSRegressNeutralize()` 等。


在 D-Lab 项目中，我基于日频和分钟频的股票量价数据，补充了 30 余个运算符和处理类，共开发了 12 个股票日频因子（某类因子可能有多种衍生出的优化方式，也视为同一个因子），各因子的构造过程简要说明如下：

| 因子名称                 | 构造过程                                                     |
| :----------------------- | ------------------------------------------------------------ |
| 重拾自信 rcp             | 基于上涨和下跌的分钟序号、与日内收益率正交化、滚动均值、滚动标准差、排序加权等处理，构造日频因子 rcp。编写了 CPIntraday、RCP 和 RCPNew 的运算符和处理类。 |
| 适度冒险 moderate_risk   | 基于日内分钟成交量的均值和标准差判断当日的“激增时刻”和“耀眼 5 分钟”，并计算“耀眼波动率”，构造 moderate_risk 因子。编写了 CSAbsDemeanNewField、MonthlyShineVolitility、MonthlyShineReturn 和 SumOfFields 的运算符和处理类。 |
| 完整潮汐 complete_tide   | 基于“邻域成交量”的最高点和最低点判断“涨潮”和“退潮”时刻，结合价格变化速率构造“完整潮汐”因子。编写了 FullTide、StrongHalfTide、WeakHalfTide 的运算符和处理类。 |
| 勇攀高峰 climbing        | 基于分钟的开高低收价格，多列滚动计算每分钟的“更优波动率”和收益波动比。结合协方差、部分数据的协方差构建"日频重建"和"日频攀登"因子。结合滚动均值和标准差的处理构造"勇攀高峰"因子。编写了 BetterVolitility、DailyReconstruction、DailyClimbing 和 RollingNewField 的运算符和处理类。 |
| 球队硬币 team_coin       | 基于传统反转因子、隔夜距离反转因子，结合波动率、换手率判断“球队”型和“硬币”型股票，翻转部分因子值以修正反转因子。编写了 Overturn 的运算符和处理类。 |
| 飞蛾扑火 moth_fire       | 根据每分钟的“单利收益率”和“连续复利收益率”，计算“日跳跃度”因子和“日振幅”因子，并计算“日泰勒残项”。根据“日跳跃度”与截面均值的大小关系，判断“太阳”型振幅和“火把”型振幅，得到“修正振幅”因子。将多个“修正振幅”因子合成得到“飞蛾扑火”因子。编写了 DailyJump、DailyTaylorResidual 和 Overturn 的运算符和处理类。 |
| 草木皆兵 weed_weapon     | 基于个股收益率与市场收益率偏离水平，计算“偏离项”和基准项”并构造“日惊恐度”。基于“日波动率”、个人投资者者交易比和衰减后的“惊恐度”，结合滚动均值和标准差计算“草木皆兵 - 收益”因子和“草木皆兵 - 波动”因子，并等权合成为“草木皆兵”因子。编写了 DailyPanicLevel 和 ProductOfFields 的运算符和处理类。 |
| nos                      | 将日内分钟收益率数据在一天内进行时间序列 ZScore 标准化。将标准化后的日内分钟收益率进行正态性检验，分两种方法：1. 基于高斯核密度估计与标准正态分布的差异计算距离熵，得到 gos_gs。2. Shapiro–Wilk 正态性检验，得到 gos_sw。编写了 TSZScoreNormalize 的运算符和处理类。 |
| exRtn                    | 基于在险价值 VaR 和 Expected Shortfall 求日内极端收益率出现的次数和条件均值。编写了 ExtremeFrequency 和 ExtremeValue 的运算符和处理类。 |
| capacity_and_flexibility | 基于高斯混合分布求两个正态分布的均值和权重，将日内分钟收益率拆分成两个标准正态分布并基于高斯混合分布的参数估计构造 capacity_and_flexibility 因子。编写了 GMMMeanS, GMMMeanJ, GMMWeightS 和 GMMWeightJ 的运算符和处理类。 |
| vol_entropy              | 将日内成交量分桶后计算信息熵，衡量成交量分布的不均匀性，结合滚动均值和标准差衡量其在时间序列上的稳定性。编写了 VolEntropy 的运算符和处理类。 |
| 水中行舟 go_with_flow    | 根据开盘至今收益率与近 20 日合理收益类的相对大小，统计价格处于高位和低位的成交额，两者做差再除以流通市值得到高低额差。分别计算每只股票与其余所有股票的“高低额差”序列之间的 Spearman 相关系数的绝对值，衡量个股成交量变化独立于市场趋势。 |

## 因子开发示例：基于收益率分布的正态性构建收益率噪音偏离因子 nos

### nos 因子投资逻辑

收益率噪音偏离因子（nos）事实上是流动性因子，它的投资逻辑如下：

1. 从流动性的视角来看，每支股票分钟收益率序列的形成依赖于分钟内所有投资者买卖单的撮合。对于流动性好的股票，分钟内的每位投资者对它的收益率的影响微乎其微，Laplace(1810) [^1]指出若误差是由许多微小量的叠加，那么样本的测量误差应该服从正态分布。对于流动性差的股票，股票价格的变动往往落在 0 正负一个基点内，所以它们的收益率噪音自然会偏离正态分布。
2. 从大额投资者的视角来看，在真实的股票市场中，不论哪一种流动性的股票里，都有可能存在大额投资者，他们能影响甚至操纵股票价格。大额投资者对股票分钟收益率的影响远超过其他投资者，表现为收益率噪音偏离正态分布。大额投资者对某支股票收益率影响越大，收益率噪音就越偏离正态分布，这支股票潜在的风险也就越大，因此市场参与者对股票的风险溢价要求也就越高。

基于以上两个视角，我们将 nos 因子值与流动性和大额投资者的逻辑关系梳理如下表：

|          | 有大额投资者           | 无大额投资者           |
| -------- | ---------------------- | ---------------------- |
| 流动性大 | nos 较大，风险溢价较高 | nos 小，风险溢价低     |
| 流动性小 | nos 大，风险溢价高     | nos 较大，风险溢价较高 |

### nos 因子构造方法

假设股票价格 $P_t$ 遵循几何布朗运动，收益率均值为 $\mu$、标准差为 $\sigma$，$ W_t$ 是标准维纳过程

$$
d P_t / P_t=\mu d t+\sigma d W_t
$$

模型离散化后，分钟收益率为 $R_t=\Delta P_t / P_t$。假设同一支股票在某天内的分钟收益率是独立同分布的，定义收益率噪音 $e_t$ 如下

$$
\begin{gathered}
\Delta P_t / P_t=\mu \Delta t+\sigma \Delta W_t \\
e_t=\frac{R_t-E\left[R_t\right]}{\sigma} \sim N(0,1)
\end{gathered}
$$

根据 nos 因子的投资逻辑，我们通过两种方法衡量标准化后的分钟收益率偏离标准正态分布的程度：

1. Shapiro-Wilk 正态性检验，该检验的零检验是样本 $x_1,\cdots ,x_n$来自于一个正态分布的母体，其检验统计量是：

   $$
   W = \frac{(\sum_{i=1}^{n}a_{i}x_{(i)})^2}{\sum_{i=1}^{n}(x_i-\bar{x})^2}
   $$

   其中：

   1. $\overline {x}=(x_{1}+\cdots +x_{n})/n$是样本的平均值。
   2. 常量 $a_i$通过下面的公式得出：

   $$
   (a_1,\dots ,a_n)=\frac{m^{T}V^{-1}}{\sqrt{(m^{T}V^{-1}V^{-1}m)}},\quad m=(m_1,\dots ,m_n)^T
   $$

   其中 $m_1,\dots ,m_n$ 是从一个标准的正态分布随机变量上采样的有序独立同分布的统计量的期望值。V 是这些有序统计量的协方差。

2. 距离熵 (metric entropy) ：用非参的高斯核密度估计出样本点 $x_t$ 对应的分布函数，与标准正态分布密度函数相减并计算差值的平方和：

   $$
   metric\_ entropy = \sum_{t=1}^N\left(D_h\left(x, m_t\right)-N\left(0,1, m_t\right)\right)^2
   $$

   其中：

   1. $D_h\left(x, x_t\right)$ 表示基于样本 $x$ 计算得到的核密度估计概率密度函数在 $m_t$ 处的取值。

   2. $m_t = \min \left(x_t\right)+\frac{\max \left(x_t\right)-\min \left(x_t\right)}{N} t$，$N\left(0,1, m_t\right)$ 表示标准正态分布密度函数对应 $m_t$处的取值。

   3. 若样本点完全服从正态分布，那么 $metric\_ entropy = 0$。

   以某日的两只股票为例，分别绘制当日的分钟收盘价折线图与标准化后的分钟收益率的直方图，如下图所示。当日内收益率出现极端值时，高斯核密度估计与标准正态分布的差距较大，计算出的距离熵也较大。上图中的股票价格变化较为平稳，距离熵为$0.13$，下图中的股票价格在少数分钟出现突变，距离熵为$0.87$。

   ![nos](index-image/nos.svg)

## 股票因子回测方法与评价指标

### 对常见的行业、市值和风格因子进行中性化

不同行业、市值、风格的股票所构造出的因子分布可能会存在显著差异，因此有必要剔除因子中包含的其他因素。具体做法为：使用行业哑变量、对数市值、市场、成长、流动性等已有因子，对新开发的因子进行 OLS 回归，将回归得到的残差作为中性化后的因子值。以行业和市值中性化为例，回归模型如下：

$$
X_t=\sum_j b_{t, j} Indu_j+b_{t, mv} m v_t+\varepsilon_t
$$

其中，$X_t$ 为 $t$ 时刻单因子在截面上的因子暴露向量（$N\times1$）， $I n d u_j$ 为所有股票在 $j$ 行业的暴露向量（$N\times1$）， $m v_t$ 为股票在市值（或对数市值）上的暴露向量（$N\times1$），$b_{t, j} 、b_{t, m v}$ 为对应的行业和市值因子收益率， $\varepsilon_t$ 为回归取得的残差（$N\times1$），即中性化后的因子暴露。

由于中性化后的因子与现有因子库相关性很低，因此因子测试结果更能反映该因子能为现有因子库带来的增量预测效果。

### 单因子排序分层回测

常用排序法构造投资组合对单因子进行检验，具体步骤如下：

1. 排序：确定股票池并将全部股票在截面上按照因子值的取值大小排序
2. 分组：按因子值排名将全部股票分为 5 组。做多第一组，做空最后一组，该组合被称为价差组合 (Spread portfolio)。价差组合的收益的差异反映了围绕该变量构建的因子的收益率。
3. 定期更新：对投资组合进行再平衡 (re-balance)，频率为每日。

### 因子评价指标

#### IC：信息系数

IC 即信息系数（Information Coefficient），指的是因子在第 $t$ 期的暴露值 $X_t$（$N\times1$）与 $t+1$ 期的收益率 $R_{t+1}$ （$N\times1$）之间的相关系数，即

$$
I C_t=\operatorname{Corr}\left(X_t, R_{t+1}\right)
$$

IC 值衡量了个股在本期的因子暴露度和个股在下期的收益率之间的线性相关程度，也可认为其反映了利用该因子对下期股票收益率的预测能力。IC 的绝对值越大表明该因子越有效，选股能力越强。在实际应用中，Pearson 相关系数易受到极端值的影响，因此常用 Spearman 秩相关系数以得到更稳健的估计结果。

#### ICIR：信息比率

ICIR 即信息比率（Information Ratio），指的是多个预测期的 IC 均值与标准差之比，即

$$
IR \approx \frac{\overline{I C_t}}{\operatorname{std}\left(I C_t\right)}
$$

### 换手率

换手率又称股票周转率，指的是一段时间内的平均买入与卖出股票总金额与平均持有股票总金额的比值，即

$$
换手率 =\frac{买卖股票的总金额/2}{平均持有股票总额}
$$

换手率衡量了投资组合持仓变化的频率以及持有个股平均时间的长短。换手率越高，说明按照该因子发出的调仓信号会使得投资组合的交易行为更加频繁，这意味着需要付出更多的交易成本。

### 因子半衰期

因子半衰期是指当期因子对滞后期收益的预测能力（用 IC 值衡量）衰减为一半时所用的时间。我们可以通过绘制因子对各个滞后期收益率的 IC 值来衡量一个因子的稳定性。若因子的半衰期过小，说明因子滞后若干期的收益率的预测能力将会很快失效。

## 基于风格相关性的衍生因子模型

在已有存量因子库的基础上，基于各股票之间的风格因子相关性对因子进行衍生，有潜力生成一批高质量、低相关性的新因子。

我们首先计算全市场共 4000 余只股票的风格因子相关性，得到对称矩阵矩阵 $M$。其中，$M_{i,j}$ 表示第 $i$ 只股票与第 $j$ 只股票的风格因子相关性，缺失值用 $0$ 表示。原始因子 $F$ 是一个 $N\times 1$ 的向量。

$$
M c=\left[\begin{array}{cccc}
M c_{11} & M c_{12} & \ldots & M c_{1 N} \\
M c_{21} & M c_{22} & \ldots & M c_{2 N} \\
\ldots & \ldots & \ldots & \ldots \\
M c_{N 1} & M c_{N 2} & \ldots & M c_{N N}
\end{array}\right]
\qquad
F=\left[\begin{array}{c}
F_1 \\
F_2 \\
\cdots \\
F_N
\end{array}\right]
$$

为突出高相关性股票之间的交互影响，我们对风格因子相关性矩阵进行如下处理：

1. 将对角线元素设为 $0$，即只考虑股票之间的交互影响。
2. 对每行数据，仅取相关性最高的前 $20$ 项数据，将其余数据均设为 $0$。
3. 对每行数据进行归一化或等权化处理。

考虑股票之间的直接相关关系，可以将衍生因子定义为：

$$
F c_i=\sum_{j=1}^n M_{i j} F_j
$$

对因子库中的 142 个因子进行衍生，并进行单因子回测，部分结果如下：

![image-20230321015126765](index-image/image-20230321015126765.png)

## 基于 SHAP 的特征归因

将多个因子作为机器学习模型的特征输入，以预测未来收益率为目标，这一过程通常会习得因子与未来收益率之间的非线性关系。与传统的线性模型相比，机器学习模型通常无法直观体现特征与预测目标之间的预测逻辑。为理解机器学习模型中各特征和输出的影响大小和方向，我们采用 SHAP 这一可解释性技术对训练的 lightGBM 模型进行事后分析。

SHAP（SHapley Additive exPlanations）（Lundberg, 2022）[^2]是揭开机器学习模型黑箱的有力工具，其核心思想是计算特征对模型输出的边际贡献。SHAP 的优势在于：既能从全局层面评估特征的重要性，又能从个体层面评估每条样本每项特征对模型输出的影响，还能展示特征间的交互作用。

### 全样本特征归因

对全部样本，按 lightGBM 模型中的特征重要性从大到小排序，得到前 20 个因子如下图所示。图中绘制了影响较大的前 20 个特征的 SHAP 值，每个点代表一个样本。图中点的红色越深代表特征的值较大，蓝色越深代表特征的值较小；点的横坐标代表该特征的 SHAP 值，即该特征对预测值的影响。横坐标越大于 0，说明该特征对该样本的预测值的影响越正向；横坐标越小于 0，说明该特征对该样本的预测值的影响越负向。下图可以看出 pv_6 因子越大的股票倾向于具有越低的未来收益率。

![image-20230319215245046](index-image/image-20230319215245046.png)

### 单样本特征归因

对某只股票在单日的收益率预测值进行分析，可将预测值归因到各特征中。下图中，基准预测值为 $0.01\%$，即对全市场的收益率预测十分接近于 $0$。但 $pv_6$、$pv_{81}$等红色标记的因子对收益率有正向作用，而 $a_1$ 等蓝色标记的因子对收益率有负向作用。综合各因子的贡献，最终得到的预测值为 $1\%$。

![image-20230319231251988](index-image/image-20230319231251988.png)

### 特征间的交互作用

研究不同特征之间的交互作用，可以帮助我们更全面地理解机器学习模型形成预测的机制。下图展示了 q_5 和 pv_6 两个因子之间的交互作用。可以看出，当 q_5 小于 0 时，较大的 pv_6 意味着 q_5 对收益率的贡献为负；而当 q_5 大于 0 时，较大的 pv_6 意味着 q_5 对收益率的贡献为正。两种不同的结果为我们分析因子间的交互作用提供了思路，后续可进一步考察各因子之间的内在逻辑关系，以帮助我们更好地构建和筛选因子。

![image-20230319232309926](index-image/image-20230319232309926.png)

## 指定单棵决策树的分裂特征

在应用随机森林等集成树模型合成多因子时，对特征的抽取通常是随机的。若我们对某些特征有先验知识，更希望某些决策树只使用指定的特征，可以通过修改 sklearn 中实现特征采样的源码实现这一目的。

以下示例中，一共有 9 个特征，但我们可以指定这棵决策树只使用 [0、1、2、3、4] 这 5 个特征进行分裂，最终得到的决策树可视化如下图所示。

![image-20230319234537640](index-image/image-20230319234537640.png)

## 自定义 lightGBM 的损失函数

对于预测收益率数值的回归问题，通常使用默认的 MSE 作为损失函数。然而，那些真实收益率较高的股票对投资组合收益率的影响更大，因此有必要对高收益股票的预测损失提高相应的权重。我们引入截面收益率排序对均方误差进行加权，得到 WMSE 损失函数，其中的权重定义为：

$$
w_i=2^{-\frac{2(i-1)}{n-1}},\quad i=1,2,\cdots,n
$$

即截面收益率最高的个股权重为 $1$，截面收益率最低的个股权重为 $0.25$。由此权重计算得到的 WMSE 损失函数为：

$$
WMSE = \frac{1}{2} \sum_{i=1}^n w_i\left(\hat{y}_i-y_i\right)^2
$$

分别计算 WMSE 的一阶导数和二阶导数：

$$
\bigtriangledown=
\left[\begin{array}{c}w_1\left(\hat{y}_1-y_1\right) \\ \vdots \\ w_n\left(\hat{y}_n-y_n\right)\end{array}\right]

\qquad

\bigtriangledown_{2}=
\left[\begin{array}{c}
w_1 \\
\vdots \\
w n
\end{array}\right]
$$

将一阶导数和二阶导数编程为函数，传入 lightGBM 的 `fobj` 和 `feval`，即可按照自定义的 WMSE 损失函数进行迭代优化。

## 重构投研框架中的 PyTorch 模块

现有投研框架使用了统一格式、支持高效并行的数据类型 `DatasetH`，但由于框架内部修改了默认参数名和数据返回格式等原因，不能直接使用 PyTorch 中的深度神经网络、LSTM 等模块进行训练与预测。我通过阅读核心源码、修改数据输入与输出时的参数名与格式，将 PyTorch 的模块重构并整合到现有投研框架。

## 总结与收获

在 D-Lab 项目中，我熟练使用公司投研框架，完整地实践了从数据提取、数据预处理、构造特征、分组回测等投研流程，对股票中低频因子投资实践有了更深刻的理解。在多因子组合模型中，我应用机器学习方法完善了特征归因、添加先验信息、自定义损失函数等功能。

这些实践经历给我带来了许多收获：我学习了面向对象编程的思想，熟悉了 Python 中的类、继承等概念，为现有投研框架贡献了统一、高效的运算符和处理类；编写了通用的工具函数，如自动汇总所有因子回测结果等，帮助团队提高工作效率；仔细阅读了机器学习模块的源码，在课程理论的基础上补充了工程实践的技术。

感谢 DS\&BA 项目为我提供的宝贵实践机会，感谢艾方的同事们给予我的耐心且毫无保留的指导，你们让我对从事量化研究的行业增添了更多信心，也坚定了我继续深入量化研究的决心。

[^1]: LaPlace P S. Analytic theory of probabilities[J]. Paris: Imprimerie Royale, 1810: 1-8.
[^2]: Lundberg, S.M., Erion, G., Chen, H. et al. From local explanations to global understanding with explainable AI for trees. Nat Mach Intell 2, 56–67 (2020).
