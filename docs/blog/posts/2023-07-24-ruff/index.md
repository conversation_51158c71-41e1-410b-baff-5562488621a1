---
title: 使用 Ruff 自动检查代码错误
authors: 
  - <PERSON>
date: '2023-07-24'
slug: ruff
categories:
  - Python
tags:
  - Python
links:
  - "Ruff GitHub 仓库": https://github.com/astral-sh/ruff
  - "Ruff 官方文档": https://beta.ruff.rs/docs/
  - "Ruff VS Code 插件": https://github.com/astral-sh/ruff-vscode
  - "性能最快的代码分析工具，Ruff 正在席卷 Python 圈！": https://pythoncat.top/posts/2023-04-09-ruff
---

# 使用 `Ruff` 自动检查代码错误

在编写大型项目时，一些细节代码容易影响代码的正常运行。若花费太多时间检查变量命名、导入包等细节问题，则会大幅影响工作效率和心情。

`Ruff` 是一个代码分析工具，即 Linter，它可以用于检查代码中的语法错误、编码规范问题、潜在的逻辑问题和代码质量问题等，可以提供实时反馈和自动修复建议。

`Ruff` 的优点是速度非常快，且安装和使用都非常简单。使用 `Ruff` 可以帮助我们自动检查代码存在的错误（如变量未定义、缺失外部依赖包等），这一切都不需要真正花时间运行代码。

![Shows a bar chart with benchmark results.](index-image/232603516-4fb4892d-585c-4b20-b810-3db9161831e4.svg)

<!-- more -->

## 安装

```bash
pip install ruff
```

## 命令行使用方法

### 检查错误

```bash
ruff check .                        # Lint all files in the current directory (and any subdirectories)
ruff check path/to/code/            # Lint all files in `/path/to/code` (and any subdirectories)
ruff check path/to/code/*.py        # Lint all `.py` files in `/path/to/code`
ruff check path/to/code/to/file.py  # Lint `file.py`
```

### 检查并修复错误

```bash
ruff check --fix .
```

### 忽略 `Line too long`

```bash
ruff check --ignore E501 test.py
```

## VS Code 插件

见 [Ruff VS Code 插件 —— GitHub 仓库](https://github.com/astral-sh/ruff-vscode)。

## 对整个文件禁用 `ruff`

```python
# ruff: noqa
import os
```

或者在 `pyproject.toml` 中配置：

```toml title="ruff.toml"
exclude = ["*.py"]
```

这样就可以对所有的 `.py` 文件禁用 `ruff`。

## 配置格式化规则

以下部分转载自 [如何写出“高颜值”的 Python 代码](https://mp.weixin.qq.com/s/yKMFQl0UM0zb3nl7PFfE7w)。已获得作者授权。

`ruff`默认的格式化规则基本上兼容`black`，但如果你想要**「自定义」**调整部分的格式化规则，譬如在引号的使用上，你更倾向于使用单引号，在`ruff`中也可以很轻松的实现。

`ruff`中推荐以具体的**「项目」**为控制范围，通过在**「项目根目录」**中创建`pyproject.toml`或`ruff.toml`来编写具体的规则，譬如在下面的示例`ruff.toml`文件中，我们声明了优先使用单引号：

```toml title="ruff.toml"
[format]
# 使用单引号
quote-style = "single"
```

格式化结果中就会对应的优先使用单引号。

或是限制每行最多字符数量：

```toml title="ruff.toml"
# 限制每行最多 20 个字符
line-length = 20

[format]
# 使用单引号
quote-style = "single"
```

设置缩进使用单个`Tab`符（默认为 4 个空格）：

```toml title="ruff.toml"
[format]
# 使用单引号
quote-style = "single"
# 缩进使用单个 tab
indent-style = "tab"
```

且`ruff`还支持对 docstring 注释中的代码片段进行识别并格式化：

```toml title="ruff.toml"
[format]
# 使用单引号
quote-style = "single"
# 启用 docstring 代码片段格式化
docstring-code-format = true
```

更多有关`ruff`代码格式化功能的细节，请移步 [ruff 官方文档](https://docs.astral.sh/ruff/formatter/)了解更多。
