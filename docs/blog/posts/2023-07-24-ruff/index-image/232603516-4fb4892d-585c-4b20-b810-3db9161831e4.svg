
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" class="marks" width="585" height="167" viewBox="0 0 585 167"><g fill="none" stroke-miterlimit="10" transform="translate(85,5)"><g class="mark-group role-frame root" role="graphics-object" aria-roledescription="group mark container"><g transform="translate(0,0)"><path class="background" aria-hidden="true" d="M0,0h483v140h-483Z"/><g><g class="mark-group role-axis" aria-hidden="true"><g transform="translate(0.5,140.5)"><path class="background" aria-hidden="true" d="M0,0h0v0h0Z" pointer-events="none"/><g><g class="mark-rule role-axis-grid" pointer-events="none"><line transform="translate(0,0)" x2="0" y2="-140" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(149,0)" x2="0" y2="-140" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(297,0)" x2="0" y2="-140" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(446,0)" x2="0" y2="-140" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/></g></g><path class="foreground" aria-hidden="true" d="" pointer-events="none" display="none"/></g></g><g class="mark-group role-axis" role="graphics-symbol" aria-roledescription="axis" aria-label="X-axis for a linear scale with values from 0 to 65"><g transform="translate(0.5,140.5)"><path class="background" aria-hidden="true" d="M0,0h0v0h0Z" pointer-events="none"/><g><g class="mark-rule role-axis-tick" pointer-events="none"><line transform="translate(0,0)" x2="0" y2="0" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(149,0)" x2="0" y2="0" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(297,0)" x2="0" y2="0" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/><line transform="translate(446,0)" x2="0" y2="0" stroke="rgba(127,127,127,0.25)" stroke-width="1" opacity="1"/></g><g class="mark-text role-axis-label" pointer-events="none"><text text-anchor="middle" transform="translate(0,15)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">0s</text><text text-anchor="middle" transform="translate(148.6153846153846,15)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">20s</text><text text-anchor="middle" transform="translate(297.2307692307692,15)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">40s</text><text text-anchor="middle" transform="translate(445.84615384615387,15)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">60s</text></g></g><path class="foreground" aria-hidden="true" d="" pointer-events="none" display="none"/></g></g><g class="mark-group role-axis" role="graphics-symbol" aria-roledescription="axis" aria-label="Y-axis for a discrete scale with 6 values: Ruff, Autoflake, Flake8, Pyflakes, Pycodestyle, Pylint"><g transform="translate(0.5,0.5)"><path class="background" aria-hidden="true" d="M0,0h0v0h0Z" pointer-events="none"/><g><g class="mark-text role-axis-label" pointer-events="none"><text text-anchor="end" transform="translate(-10,15.166666666666671)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1" font-weight="bold">Ruff</text><text text-anchor="end" transform="translate(-10,38.5)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">Autoflake</text><text text-anchor="end" transform="translate(-10,61.833333333333336)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">Flake8</text><text text-anchor="end" transform="translate(-10,85.16666666666667)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">Pyflakes</text><text text-anchor="end" transform="translate(-10,108.5)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">Pycodestyle</text><text text-anchor="end" transform="translate(-10,131.83333333333331)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" opacity="1">Pylint</text></g></g><path class="foreground" aria-hidden="true" d="" pointer-events="none" display="none"/></g></g><g class="mark-rect role-mark layer_0_marks" role="graphics-object" aria-roledescription="rect mark container"><path aria-label="time: 0.2943; tool: Ruff" role="graphics-symbol" aria-roledescription="bar" d="M0,5.166666666666671h2.186875384615385v13h-2.186875384615385Z" fill="#6340AC"/><path aria-label="time: 6.175; tool: Autoflake" role="graphics-symbol" aria-roledescription="bar" d="M0,28.5h45.885v13h-45.885Z" fill="#6340AC"/><path aria-label="time: 12.26; tool: Flake8" role="graphics-symbol" aria-roledescription="bar" d="M0,51.833333333333336h91.10123076923077v13h-91.10123076923077Z" fill="#6340AC"/><path aria-label="time: 15.786; tool: Pyflakes" role="graphics-symbol" aria-roledescription="bar" d="M0,75.16666666666667h117.30212307692308v13h-117.30212307692308Z" fill="#6340AC"/><path aria-label="time: 46.921; tool: Pycodestyle" role="graphics-symbol" aria-roledescription="bar" d="M0,98.5h348.6591230769231v13h-348.6591230769231Z" fill="#6340AC"/><path aria-label="time: 62; tool: Pylint" role="graphics-symbol" aria-roledescription="bar" d="M0,121.83333333333331h460.70769230769235v13h-460.70769230769235Z" fill="#6340AC"/></g><g class="mark-text role-mark layer_1_marks" role="graphics-object" aria-roledescription="text mark container"><text aria-label="time: 0.2943; tool: Ruff; timeFormat: 0.29s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(8.186875384615385,15.666666666666671)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333" font-weight="bold">0.29s</text><text aria-label="time: 6.175; tool: Autoflake; timeFormat: 6.18s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(51.885,39)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333">6.18s</text><text aria-label="time: 12.26; tool: Flake8; timeFormat: 12.26s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(97.10123076923077,62.333333333333336)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333">12.26s</text><text aria-label="time: 15.786; tool: Pyflakes; timeFormat: 15.79s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(123.30212307692308,85.66666666666667)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333">15.79s</text><text aria-label="time: 46.921; tool: Pycodestyle; timeFormat: 46.92s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(354.6591230769231,109)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333">46.92s</text><text aria-label="time: 62; tool: Pylint; timeFormat: &gt; 60s" role="graphics-symbol" aria-roledescription="text mark" text-anchor="start" transform="translate(466.70769230769235,132.33333333333331)" font-family="-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;" font-size="12px" fill="#333333">&gt; 60s</text></g><g class="mark-text role-mark layer_2_marks" role="graphics-object" aria-roledescription="text mark container"/></g><path class="foreground" aria-hidden="true" d="" display="none"/></g></g></g></svg>
