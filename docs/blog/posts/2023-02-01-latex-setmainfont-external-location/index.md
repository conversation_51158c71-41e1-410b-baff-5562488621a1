---
title: LaTeX 设置字体时指定字体文件目录
authors:
  - <PERSON>
date: "2023-02-01"
slug: latex-setmainfont-external-location
categories:
  - LaTeX
tags:
  - LaTeX
links:
  - Stack Exchange - How to \setmainfont when 'randomfont.ttf' is in different folder?: https://tex.stackexchange.com/questions/230186/how-to-setmainfont-when-randomfont-ttf-is-in-different-folder
---

# $\LaTeX$ 设置字体时指定字体文件目录

## 问题与需求

在编写多个$\TeX$文档时，我们可能会同时导入相同的外部字体文件。

通常的做法是，将字体文件放在与当前文档所在的同级目录。

```bash title="重复存储多份 font.TTF" hl_lines="4 8"
$ tree
.
|-- tex_1
|   |-- font.TTF
|   |-- tex_1.pdf
|   `-- tex_1.tex
`-- tex_2
    |-- font.TTF
    |-- tex_2.pdf
    `-- tex_2.tex
```

这样的做法会使得`font.TTF`同时存在于`tex_1.tex`和`tex_2.tex`两个文档所在的目录下，而同一份字体文件是没有必要存两遍的。我们希望能将`font.TTF`存在一个公共目录，使得`tex_1.tex`和`tex_2.tex`都可以导入其中的字体。

## 解决方法

<!-- more -->

将`font.TTF`移动到项目根目录下的`fonts/`目录中。

```bash title="共用一份 font.TTF" hl_lines="4"
$ tree
.
|-- fonts
|   `-- font.TTF # (1)!
|-- tex_1
|   |-- tex_1.pdf
|   `-- tex_1.tex
`-- tex_2
    |-- tex_2.pdf
    `-- tex_2.tex
```

1.  所有 `tex` 文档都可导入这一份字体文件，避免了重复存储的问题。

并在`tex_1.tex`和`tex_2.tex`中使用`ExternalLocation`来指定字体文件目录。

```tex
\setmainfont[ExternalLocation=../fonts/]{font.TTF}
```

## 缺点

目前测试发现：

- 英文字体可以使用`#!tex \setmainfont[ExternalLocation=../fonts/]{font.TTF}`方法成功指定字体文件目录。
- 但中文字体需要用`#!tex \setCJKmainfont{font.TTF}`来指定字体，而`#!tex \setCJKmainfont`似乎不支持设置`ExternalLocation`参数，使用它会报错。因此外部的中文字体还是只能放到`tex`文档的同级目录。
