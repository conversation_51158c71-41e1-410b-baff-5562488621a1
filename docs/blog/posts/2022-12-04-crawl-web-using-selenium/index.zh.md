---
title: 使用 selenium 爬取豆瓣小组讨论信息
authors:
  - <PERSON>
date: "2022-12-04"
slug: crawl-web-using-selenium
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 使用 selenium 模拟浏览器访问网页，解析并提取需要的信息，实现网络爬虫。
links:
  - "豆瓣复旦校友上海租房小组": https://www.douban.com/group/515560/discussion
---

# 使用 selenium 爬取豆瓣小组讨论信息

使用 selenium 模拟浏览器访问网页，解析并提取需要的信息，实现网络爬虫。

![image-20230115235227306](index.zh-image/image-20230115235227306.png)

<!-- more -->

使用`selenium`模拟浏览器，爬取豆瓣复旦校友上海租房小组的帖子，网址在 [这里](https://www.douban.com/group/515560/discussion)。

## 导入必要的包

```python
from selenium import webdriver
from bs4 import BeautifulSoup
import pandas as pd
```

## 设置驱动、请求头和需要访问的 URL

```python
driver = webdriver.Chrome("D:\Program\chromedriver.exe")
# 初始化选项
options = webdriver.ChromeOptions()
# 设置自定义请求头
options.add_argument(
    'user-agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36 Edg/101.0.1210.53"'
)
# 设置 URL
url = "https://www.douban.com/group/515560/discussion"
driver.get(url)
```

## 用 BeautifulSoup 解析数据

```python
# 用 BeautifulSoup 解析数据
content = driver.page_source.encode("utf-8")
soup = BeautifulSoup(content, "lxml")
# 找到所有帖子的标题
title_content = soup.find_all(class_="title")
time_content = soup.find_all(class_="time")
```

## 提取出需要的数据

```python
# 只保存标题文字、超链接和时间
title_list = []
href_list = []
time_list = []
for title, time in zip(title_content[:-1], time_content):
    title_list.append(title.a["title"].strip())
    href_list.append(title.a["href"].strip())
    time_list.append(time.text)
```

## 将数据导出到本地 csv 文件

```python
# 将数据导出到本地 csv 文件
df = pd.DataFrame(
    list(zip(time_list, title_list, href_list)), columns=["最后回应时间", "标题", "网址"]
)
df.to_csv("豆瓣小组爬虫。csv", index=False, encoding="utf-8-sig")
```

## 展示爬取到的具体内容

```python
df
```

<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }

</style>

<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>最后回应时间</th>
      <th>标题</th>
      <th>网址</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>11-25 11:15</td>
      <td>复旦大学附近全配双南二室一厅</td>
      <td>https://www.douban.com/group/topic/279122048/</td>
    </tr>
    <tr>
      <th>1</th>
      <td>11-24 12:36</td>
      <td>合租转租仅女生 2100/月押一付一 近 B 站尚浦</td>
      <td>https://www.douban.com/group/topic/279076030/</td>
    </tr>
    <tr>
      <th>2</th>
      <td>11-24 09:46</td>
      <td>美岸栖庭单间实体房出租</td>
      <td>https://www.douban.com/group/topic/279068051/</td>
    </tr>
    <tr>
      <th>3</th>
      <td>11-22 16:14</td>
      <td>同济大学附近两居室次卧转租，限女生</td>
      <td>https://www.douban.com/group/topic/278976916/</td>
    </tr>
    <tr>
      <th>4</th>
      <td>11-21 09:51</td>
      <td>国顺路 310 弄南北两室，装修干净清爽，只要 5000，看房有钥匙联系 13588488922</td>
      <td>https://www.douban.com/group/topic/278905908/</td>
    </tr>
    <tr>
      <th>5</th>
      <td>11-18 20:36</td>
      <td>复旦，地铁 10，18 号线国权路站附近全配三室户出租</td>
      <td>https://www.douban.com/group/topic/278783611/</td>
    </tr>
    <tr>
      <th>6</th>
      <td>11-18 09:35</td>
      <td>转租 - 个人转租 - 浦东大道</td>
      <td>https://www.douban.com/group/topic/278748900/</td>
    </tr>
    <tr>
      <th>7</th>
      <td>11-18 09:26</td>
      <td>出租国顺路 375 弄 2 楼双南，两室一厅，6300/月精装修全配，看房有钥匙看房联系 13588...</td>
      <td>https://www.douban.com/group/topic/278748540/</td>
    </tr>
    <tr>
      <th>8</th>
      <td>11-16 10:42</td>
      <td>东方文苑小区出租，离复旦大学邯郸校区脚程不到 10 分钟</td>
      <td>https://www.douban.com/group/topic/278643617/</td>
    </tr>
    <tr>
      <th>9</th>
      <td>11-14 19:12</td>
      <td>转租国权路 1200/月女生无中介费</td>
      <td>https://www.douban.com/group/topic/278560381/</td>
    </tr>
    <tr>
      <th>10</th>
      <td>11-09 21:18</td>
      <td>复旦，同济，五角场，10/18 号线，寻考研 or 专硕在读 or 实习女室友（无中介费）～</td>
      <td>https://www.douban.com/group/topic/278274798/</td>
    </tr>
    <tr>
      <th>11</th>
      <td>11-09 15:51</td>
      <td>五角场好房出租</td>
      <td>https://www.douban.com/group/topic/278256396/</td>
    </tr>
    <tr>
      <th>12</th>
      <td>11-05 16:43</td>
      <td>五角场！！！十号线！！18 号线！！财经大学站！！！精装公寓 loft 层高 5 米只需要 3100</td>
      <td>https://www.douban.com/group/topic/278026628/</td>
    </tr>
  </tbody>
</table>

</div>

## 关闭浏览器

```python
driver.close()
```
