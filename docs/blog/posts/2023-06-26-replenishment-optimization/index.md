---
title: 饮料企业多工厂生产与补货优化
authors: 
  - <PERSON> Feng
date: '2023-06-26'
slug: replenishment-optimization
categories:
  - 运筹学
tags:
  - 运筹学
links:
  - "供应链与运营管理学会 - 杉数科技实践驱动研究竞赛": https://www.coap.online/competitions/1
---

# 饮料企业多工厂生产与补货优化

本文基于某饮料企业的工厂、仓库与商品相关的历史信息，结合随机模拟的售价与成本数据，构建了多工厂、多仓库的生产与补货优化模型。

数值试验表明，本文构建的优化后的生产与补货模型能够比基线模型（简单基于历史销量而固定生产量）多获得约 500 万元的利润，且在补货行为上更具优势。对工厂和仓库容量的灵敏度分析表明，工厂 2 和 DC4、5、7、14 多具有当前容量较小、运输成本低、历史销量高等特点，对它们进行扩容能够取得显著的回报增益。对整托约束的松弛表明，整托运输虽以节省运输成本为目的，但实际却可能造成运输资源的浪费，而考虑适当放松整托约束有潜力能够提高约 100 万元的利润。

![问题目标示意图](index-image/image-20230610124042686.png)

<!-- more -->

## 选题背景

### 问题背景

古话有云，兵马未动粮草先行。对于一家大型企业而言，一个良好的生产与补货计划犹如后方的补给线，在整条供应链中扮演着至关重要的角色。在大型企业中，业务领导者通常会进行分工，分管生产与补货决策流程中选品、补货、供应商选择等多个模块。各模块的负责人需要进行及时的信息对齐，以做出一致的生产与补货决策，生产合适数量的货物并将其在合适的时间从合适的供应商补到合适的仓库。

在真实的业务场景中，生产量与补货量的计算也大有学问。以快消品⾏业为例，考虑到该⾏业市场需求变化快、销售渠道多、销售环节复杂等特点，对变化需求的响应速度及补货运输效率构成了企业极为重要的竞争⼒。⽽每个渠道的生产与补货都受不同因素的影响，如产品季节性、消费者偏好、库存新鲜度、供应周期、需求场景、产能约束、库容约束、发运资源约束等。因此，如何在⾯临诸多不确定性的情况下制定最佳的生产与补货计划，对企业来说尤为重要。

### 问题描述

本项目旨在解决某饮料公司的智能生产与补货问题，尽可能避免人工决策带来的 SKU 滞销、浪费、占用库存、供应不足等情况，最大化企业利润，实现更好的产销协同。

该饮料企业设有 2 个生产工厂，18 个 DC 仓库（Distribution Center，即发运中心），生产并销售 72 种 SKU（Stock Keeping Unit，即最小存货单元）。收到来自客户的订单时，DC 的现货可以直接满足需求。当 DC 库存不足时，则产生缺货，需要从工厂补货。

在过去，该企业依靠计划员过往的经验进行生产与补货计划的决策。制定合理的生产与补货计划往往需要花费大量的精力进行手工调整，并且经考量，手工生产与补货决策正面临着如下难题：

1. SKU 种类及 DC 仓库个数较多，每天要决策的补货单元就有 SKU 数乘以仓库数，共涉及上千个决策变量。而员工的时间和精力都是有限的，一般只能重点决策几个主要的 SKU 与仓库组合，对于剩余的大部分组合，往往只能按照简单的逻辑来决策；

2. 各个 DC 所面临的各个 SKU 的销售波动大，常常受到天气状况、促销活动、体育赛事举办（可能会增加饮料需求）等影响，因此难以迅速应对由于不确定性因素的发生而导致的需求变化，进而无法对生产与补货作出最优化调整；

3. 生产与补货中的业务规则复杂，往往会遇到各种各样的限制，如工厂生产力有限、可发库存不足、DC 库容有限、工厂每日最少需要发一定数量的货；再比如出于业务操作的要求，必须要整托补货，甚至整车补货，人工很难照顾到如此多的业务约束，经常会出现为满足整托约束而导致补货过量 DC 爆仓的问题；

4. 现实业务中操作需要成本，而且不同的操作存在成本的差异，比如某个 DC 从工厂 A 补货会比从工厂 B 补货成本低，因此当工厂 A 的库存不足时，需要权衡不补货的缺货损失和从工厂 B 补货的多余成本损失。而人工处理精力有限，经常会由于不合理决策带来一些不必要的成本。

为探索上述问题可能的解决方案，我们使用杉数科技某供应链研究竞赛的数据集，构建数学模型并设计算法进行求解。该数据集描述了该企业在某地区的供应仓网结构和销售情况，我们将在数学模型章节对数据情况进行详细介绍。

### 问题目标

本项目希望在给定现有供应链网络的情况下，安排生产排期和补货策略以实现利润最大化。而考察现有的供应链网络可以发现，在生产和补货过程中，该饮料公司面临着众多运营层面的约束。

生产方面的约束主要有如下两个：

1. 工厂生产线需要一直运行，因此存在每日最低产量；

2. 由于人工和时间限制，每日产量亦存在上限。

补货方面的约束主要有如下四个：

1. 工厂每日生产但未发出的产品暂存在工厂仓库，工厂每日发货量不能超过该日生产量加之前库存量；

2. 超出该工厂发货量的订单将从另一个工厂调拨，超出两工厂发货总量的订单需要等待排期；

3. 产品通过托车运输，因此补货的最小单元是托；

4. 工厂发往 DC 需要经过人工拣选、装车、道路运输等环节，因此每天可运输总量有上限。

本项目需要考虑在上述多方面约束下，如何安排：

1. 工厂每天生产不同 SKU 数量（2 工厂 × 72 SKU）；

2. 工厂每天补货不同 SKU 数量（2 工厂 × 18 DC × 72 SKU）。

使得该饮料公司利润最大化。

其中，利润 = 收益 - （生产成本 + 运输费用 + 其他费用）

即：

**优化目标：**全局利润最大化

**决策变量：**

1. 工厂每天生产数量

1. 工厂每天发货数量

**约束条件：**

1. 工厂库容约束：工厂每日发货量之和有下限

2. 运输资源约束：工厂每日发货量之和有上限

3. 工厂货量约束：工厂各 SKU 每日发出量 $≤$ 当日产量 $+$ 当日库存

4. DC 库容约束：DC 原有总库存 $+$ 当日补货量 $-$ 当日销量 $≤$ DC 库容

5. DC 货量约束：DC 原有总库存 $+$ 当日补货量 $≥$ 当日销量

6. 整托补货约束：DC 补货的最小单元是托

![问题目标示意图](index-image/image-20230610124042686.png)

## 数学模型

本章将对数据集进行详细介绍，并结合上一章提出的问题描述和问题目标，综合各类约束和目标建立数学模型。

### 数据描述

#### 历史销售数据

历史销售数据由 72 个 SKU 商品在 18 个 DC 仓库的销售数据构成，包含从 2018 年 1 月 1 日到 2020 年 7 月 30 日每个 DC\*SKU 组合的日销售信息，共有 1080 个不重复的 DC\*SKU 组合共 83 余万条记录。

我们统计了各 DC 在不同年份的销量数据，发现 C2、DC6、DC5、DC4 所承担的销售量最高，我们推测可能与其仓库容量有关，这些 DC 也很可能在供应链中发挥着较为关键的作用。

![总体 DC 负担量](index-image/DC负担.png){width=50%}

对于总销量的时间序列数据进行分析，我们可以发现销量数据呈现出明显的以周为单位的周期性。

![销量时序](index-image/销量时序.png)

销量较多的品类主要有 SKU023、SKU045、SKU022 等。这些品类的销售量大，其销售高峰对于生产及补货影响也较大。

![销冠品类](index-image/销冠品类.png)

![D-S 网络](index-image/D-S网络.png)

我们进一步查看各 DC 各 SKU 的总销量，可以发现某些品类仍然集中地表现出销量优势，但不同 DC 对于各个 SKU 也有不同的侧重情况。DC 与 SKU 的二部图可以很好地展示这种销量分配关系，其中的连线粗细代表历史总销量。

#### 工厂信息

工厂信息包括工厂发送上下限以及工厂原有库存。其中，工厂发送上下限给出了工厂每日补货量的最小和最多补货量。工厂原有库存给出了两个工厂中各个 SKU 的起始库存。

| **factory_id** | **push_lb** | **push_ub** |
| -------------- | ----------- | ----------- |
| F001           | 159000      | 161000      |
| F002           | 69000       | 71000       |

Table: 工厂发送上下限

![工厂库存](index-image/工厂库存.png)

#### DC 数据

DC 数据包括 DC 可用容量以及 DC 原有库存。其中，DC 可用容量给出了各个 DC 的仓储容量上限。DC 原有库存记录了每个 DC 种各 SKU 的起始库存量。

从各个 DC 的仓储容量情况来看，各个 DC 的容量大小差异较大，DC002 容量最大，DC009 容量最小。

![DC 的仓储容量上限](index-image/DC容量.png)

在初始状态下，各个 DC 的原有库存和剩余库存（剩余库存=最大容量 - 原有库存）差异较大。在将工厂库存分配到每个 DC 时，应该考虑到每个 DC 的剩余库存大小来进行合理的资源配置。

![DC 的原有库存和剩余库存](index-image/DC库存.png)

#### 运输信息

运输信息包括单箱运输费用和转化率。其中，单箱运输费用给出了从两个工厂到各个 DC 的单箱运输费用。转化率给出了产品运输过程种不同计量单位的转换率，即箱、托和升之间的关系。

运输成本是补货中的主要成本。每个 DC 都可以从任一工厂补货，但由于距离远近，不同工厂到 DC 会有成本差距。DC 希望尽量从运输成本更低的工厂补货，只有当优先工厂库存不足才考虑从非优先工厂补货。不同工厂到 DC 之间运输成本差异较大，考虑补货时需要重点考虑，比如 F001->DC012 和 F002->DC012 的补货成本最高。

![工厂到 DC 之间的运输成本](index-image/运输成本.png){width=80%}

### 模型构建

综合各种考量之后，我们可以得出最大化总利润的优化目标和一系列约束条件，从而构建以下形式的生产和补货优化模型。

$$
\begin{aligned}
最大化总利润 & = 收益 - (生产成本 + 运输费用 + 其他费用) \\
            & = \sum 销量 \times 价格 - ( \sum 生产量 \times 生产成本 + \sum 补货量 \times 运输成本)
\end{aligned}
$$

1. 工厂库容 + 运输资源约束：工厂每日补货量之和有上下限

2. 工厂货量约束：工厂各 SKU 每日发出量≤当日产量 + 当日库存

3. DC 库容约束：DC 原有总库存 - 当日销量 + 当日补货量≤DC 库容

4. DC 货量约束：DC 原有总库存 + 当日补货量≥当日销量

5. 需求约束：DC 当日销量≤实际需求量

6. 整托补货约束：DC 补货的最小单元是托

数学形式的模型表述如下：

$$
\begin{aligned}
\underset{x_{i, k, t}, y_{i, j, k, t}, s_{j, k, t}}{max} 
& \underset{j, k}{\sum} s_{j, k, t} \cdot p_{k}-\underset{i, k}{\sum} x_{i, k, t} \cdot f_{k}-\underset{i, j, k}{\sum} y_{i, j, k, t} \cdot g_{i j} \\
\text { s.t. } \quad
& b_{i} \leq \underset{j, k}{\sum} y_{i, j, k, t} \leq a_{i}, \quad \forall i, t \\
& \underset{j}{\sum} y_{i, j, k, t} \leq x_{i, k, t}+c_{i, k, t}, \quad \forall i, k, t \\
& \underset{i, k}{\sum}\left(d_{j, k, t}-s_{j, k, t}+y_{i, j, k, t-\tau_{j, k}}\right) \leq e_{j}, \quad \forall j, t \\
& \underset{i}{\sum} y_{i, j, k, t}+d_{j, k, t+\tau_{j, k}} \geq s_{j, k, t+\tau_{j, k}}, \quad \forall j, k, t \\
& 0.9 \leq \frac{s_{j, k, t}}{h_{j, k, t}} \leq 1, \quad \forall j, k, t \\
& \frac{y_{i, j, k, t}}{r_{k}} \in N, \quad \forall i, j, k, t \\
& x_{i, k, t}, y_{i, j, k, t}, s_{j, k, t} \geq 0, \quad \forall i, j, k, t \\
\end{aligned}
$$

其中变量和模型参数如表 2 所示：

| 类型 | 变量 | 含义 | 备注 |
| :-: | :-: | :-------- | :--- |
| 下标 | $i$ | 工厂 | $i\in\{1,2\}$ |
| 下标 | $j$ | $DC$ | $j\in\{1,2,...,18\}$ |
| 下标 | $k$ | $SKU$ | $k\in\{1,2,...,72\}$ |
| 下标 | $t$ | 当日日期 |  |
| 模型参数 | $a_i$ | 工厂$i$的补货量上限 | 确定值 |
| 模型参数 | $b_i$ | 工厂$i$的补货量下限 | 确定值 |
| 模型参数 | $c_{i,k,t}$ | 工厂 i 中$SKU_k$在$t$日的库存 | 迭代计算 |
| 模型参数 | $d_{j,k,t}$ | $DC_j$中$SKU_k$在$t$日的库存 | 迭代计算 |
| 模型参数 | $e_j$ | $DC_j$的库容上限 | 确定值 |
| 模型参数 | $f_k$ | $SKU_k$的单位生产成本 | 模拟生成 |
| 模型参数 | $g_{i,j}$ | 工厂$i$到$DC_j$的单位运输成本 | 确定值 |
| 模型参数 | $h_{j,k,t}$ | $DC_j$中$SKU_k$在$t$日的销售需求 | 历史数据预测 |
| 模型参数 | $\tau_{j,k}$ | $DC_j$中$SKU_k$的补货提前期 | 简化处理 |
| 模型参数 | $p_k$ | $SKU_k$的单位售价 | 模拟生成 |
| 模型参数 | $r_k$ | $SKU_k$从 10L 到托的转化率 | 确定值 |
| 决策变量 | $x_{i,k,t}$ | 工厂$i$在$t$日生产的 SKUk 的数量 |  |
| 决策变量 | $y_{i,j,k,t}$ | 工厂$i$在$t$日向$DC_j$补货的$SKU_k$的数量 |  |
| 决策变量 | $s_{j,k,t}$ | $DC_j$中$SKU_k$在 t 日的销量 |  |

Table: 模型变量和参数解释

### 模型理论分析

上一节中构建的模型是一个线性规划模型，其中 $y_{i,j,k,t}$ 需要满足整托约束，具有整数规划的性质。模型可以用常见的求解器进行求解，计算速度较快。

值得注意的是，我们把销量也加入了决策变量中，这是为了解决 DC 库容约束受到不确定的销量影响这一问题。实际决策过程中，销量不是一个决策变量，DC 管理员根据需求情况，如果有库存就尽可能满足需求，不需要决策形成销量数字。但在我们的模型中，销量对 DC 库容等约束有影响，为了更加符合逻辑和增加灵活性，我们选择用历史数据预测需求，用需求约束销量，将销量放进决策变量中。由于决策目标是 Max 问题，会使求解得到的销量尽可能满足需求。

模型的另一个问题是整数规划中的单位换算问题。在原始数据中，DC 库存容量是对所有 SKU 的总和以 10L 计量，不适合转换为箱或托，无法直接将某个决策变量设置为整数。因此我们引入了各个 SKU 的箱/托到 10L 的转化率参数，统一用 10L 作单位，对补货量与转化率的比值进行整数约束。

## 求解算法设计

### 模型参数与决策变量

在整个模型求解过程中，会涉及模型参数和决策变量相关的一系列约束条件和优化目标，因此我们首先对这两者的变量名、含义、单位和维度进行解释说明，如表 3 所示。

| 类型 | 变量名 | 含义 | 单位 | 维度 |
| -- | ---- | ----- | - | ---- |
| 模型参数 | bwy_list | 工厂（2） | / | 工厂 |
| 模型参数 | dc_list | DC（18） | / | DC |
| 模型参数 | SKU_list | SKU（72） | / | SKU |
| 模型参数 | push_lb、push_ub | 工厂发货下限、工厂发货上限 | 10L | 工厂 |
| 模型参数 | bwy_inventory | 工厂原有库存 | 箱 | 工厂+SKU |
| 模型参数 | dc_inventory | DC 原有库存 | 箱 | DC+SKU |
| 模型参数 | demand | 需求 | 箱 | DC+SKU |
| 模型参数 | pt_box_rate | 一托所含箱数 | / | SKU |
| 模型参数 | box_volume_rate | 单箱体积 | / | SKU |
| 决策变量 | x_box | 补货箱数 | 箱 | 工厂+DC+SKU |
| 决策变量 | z_pt | 补货托数 | 托 | 工厂+DC+SKU |
| 决策变量 | produce | 生产量 | 10L | 工厂+SKU |
| 决策变量 | sales | 销量 | 箱 | DC+SKU |

Table: 各变量的含义、单位及维度

具体的算法代码设计中，我们创建了优化模型（replenish optimization）和模型对应的决策变量（x_box；z_pt；produce；sales），其对应的代码如下所示：

```python
# 创建环境
env = Envr()

# 创建模型：replenish optimization
M = env.createModel("replenish optimization")

# 创建决策变量
# 1. x_box：补货箱数
x_box = M.addVars(
    bwy_list, dc_list, SKU_list, vtype=COPT.INTEGER, lb=0, nameprefix="x_box"
)

# 2. z_pt：补货托数
z_pt = M.addVars(
    bwy_list, dc_list, SKU_list, vtype=COPT.INTEGER, lb=0, nameprefix="z_pt"
)

# 3. produce：生产量
produce = M.addVars(
    bwy_list, SKU_list, vtype=COPT.CONTINUOUS, lb=0, nameprefix="produce"
)

# 4. sales 销量
sales = M.addVars(dc_list, SKU_list, vtype=COPT.INTEGER, lb=0, nameprefix="sales")
```

最终，我们模型的决策变量 x_box 是一个 2×18×72 的矩阵；z_pt 是一个 2×18×72 的矩阵；produce 是一个 2×72 的矩阵；sales 是一个 18×72 的矩阵。

### 模型约束条件

1. 工厂维度：工厂发货量总和不能低于下限且不能超过上限

```python
# 工厂发货量之和不能低于下限
M.addConstrs(
    (
        quicksum(x_box[i, j, k] * box_volume_rate[k] for j in dc_list for k in SKU_list)
        >= push_lb[i]
        for i in bwy_list
    ),
    nameprefix="push_lb",
)

# 工厂发货量之和不能超过上限
M.addConstrs(
    (
        quicksum(x_box[i, j, k] * box_volume_rate[k] for j in dc_list for k in SKU_list)
        <= push_ub[i]
        for i in bwy_list
    ),
    nameprefix="push_ub",
)
```

2. 工厂+SKU 维度：工厂各 SKU 每日发货量$≤$（工厂各 SKU 当日生产量 + 工厂各 SKU 原有库存）

```python
M.addConstrs(
    (
        quicksum(z_pt[i, j, k] * pt_box_rate[k] * box_volume_rate[k] for j in dc_list)
        <= produce[i, k] + bwy_inventory[i, k] * box_volume_rate[k]
        for i in bwy_list
        for k in SKU_list
    ),
    nameprefix="factory_limit(10L)",
)
```

3. DC 维度：（DC 原有总库存+DC 当日总补货量-DC 当日总销量）$≤$DC 库容

```python
M.addConstrs(
    (
        quicksum(
            dc_inventory[j, k] * box_volume_rate[k]
            - sales[j, k] * box_volume_rate[k]
            + quicksum(z_pt[i, j, k] for i in bwy_list)
            * pt_box_rate[k]
            * box_volume_rate[k]
            for k in SKU_list
        )
        <= dc_capacity[j]
        for j in dc_list
    ),
    nameprefix="dc_capacity limit(10L)",
)
```

4. DC+SKU 维度：（DC 各 SKU 原有总库存+DC 各 SKU 当日补货量）$≥$当日销量

```python
M.addConstrs(
    (
        dc_inventory[j, k] * box_volume_rate[k]
        + quicksum(
            z_pt[i, j, k] * pt_box_rate[k] * box_volume_rate[k] for i in bwy_list
        )
        >= sales[j, k] * box_volume_rate[k]
        for j in dc_list
        for k in SKU_list
    ),
    nameprefix="dc supply limit",
)
```

5.  DC+SKU 维度：DC 各 SKU 销量$≤$DC 各 SKU 需求

在约束 5 上，为了进一步使得我们的模型能够更好地适应市场需求并尽可能满足多样化的需求，我们引入了更为严格的约束条件：0.9*DC 各 SKU 需求 $≤$ DC 各 SKU 销量 $≤$ DC 各 SKU 需求，目的是进一步限制模型的输出。这个额外的约束条件旨在确保模型更加准确、可靠，符合实际应用场景的规范和标准。

此外，引入这种更严格的约束条件有助于提高我们的模型在求解过程中的速度，限制模型的搜索空间，使其更加专注于解决核心问题，减少不必要的计算开销，最终提升模型的算法效率。

```python
M.addConstrs(
    (
        0.9 * demand[j, k] <= sales[j, k] <= demand[j, k]
        for j in dc_list
        for k in SKU_list
    ),
    nameprefix="demand_limit",
)
```

6. 工厂+DC+SKU 维度：整托运输

基于问题背景中的现实情况，我们要求补货总箱数除以每托的箱数应为整数，即每次补货都必须以满托的形式补货。

```python
M.addConstrs(
    (
        x_box[i, j, k] / pt_box_rate[k] == z_pt[i, j, k]
        for i in bwy_list
        for j in dc_list
        for k in SKU_list
    ),
    nameprefix="full_pallet",
)
```

### 模型目标函数

$$
\begin{aligned}
利润&=销售收入–(运输成本 + 生产成本)  \\
&= \Sigma(每个 DC 下每个 SKU 的销量 \times SKU 对应的售价)\\
&- \Sigma(每个工厂下每个 SKU 的生产量 \times SKU 对应的生产成本)\\
&-\Sigma(每个工厂发到每个 DC 的每个 SKU 的发货量 \times 工厂到 DC 的运费)
\end{aligned}
$$

相应代码实现如下：

```python
# 运输成本
trans_cost = quicksum(
    quicksum(x_box[i, j, k] * transport_tariff[i, j] for i in bwy_list for j in dc_list)
    for k in SKU_list
)
# 生产成本
total_produce_cost = quicksum(
    quicksum(produce[i, k] * produce_cost[k] / box_volume_rate[k] for i in bwy_list)
    for k in SKU_list
)
# 销售收入
sales_income = quicksum(
    quicksum(sales[j, k] * price[k] for j in dc_list) for k in SKU_list
)

# 设置目标
# 利润 = 销售收入 - 运输成本 - 生产成本
# 最大化利润
M.setObjective(sales_income - trans_cost - total_produce_cost, COPT.MAXIMIZE)
```

## 实验分析

本章将针对建立的优化模型进行实验分析并解读相应结果，说明模型的有效性与合理性。此外，我们还将模型结果与基线模型对比，进一步说明模型的优势。

利用 COPT 求解器求得模型的最优解为$4.003437\times{10^8}$，模型将输出三组决策变量的值：生产、发货和销售情况。这三组变量在一起共同构成了我们的多工厂生产与补货优化策略，如下图所示：

![模型结果](index-image/模型结果.png)

下面将就生产情况、补货情况与需求分析、生产成本与售价、模型与基线模型的对比四个方面展开讨论。

### 生产情况

鉴于模型的优化目标是最大化利润，且生产需要一定成本，故工厂生产商品应当是有目的性的，即需要发货是商品才需要生产。为了验证这一猜想，绘制工厂生产 - 补货关系图如下：

![生产补货关系图](index-image/生产补货关系图.png)

图 11 表示生产=0 和生产>0 的商品的补货量分布。其中右边的柱子表示生产>0 的商品的补货量都是>0 的，也就证明了我们的结论，即我们的决策结果不存在生产了但是不补货的情况。换句话说，在原有的基础上生产都是赋能市场需求的，该决策模型不会有多余的生产行为出现。

### 补货情况与需求分析

进一步地，我们希望分析我们的补货行为在何种程度上满足了市场需求，为此，定义 demand gap 如下：

$$
\text{demand gap} = 需求 - 实际销量
$$

![demandgap](index-image/demandgap.png)

需求和销量的差距意味着我们的模型满足了多少需求，从上图中可以看出补货量大于 0 的数据基本都能满足需求（除了单独一个情况，其余 gap 均<1，由于需求数据实际由模拟生成，因此可能不是整数，而销售以整箱卖出，因此 gap<1 即可视为全部满足需求）。其中唯一没有满足需求的情况，经查看数据发现其 DC 剩余库存为 0，说明其已尽力满足需求。因此从补货量大于 0 的数据的 Demand gap 分布图中，可以看出模型的补货行为尽力满足了市场需求。

### 生产成本与售价

我们的优化模型中，商品的生产成本和售价由随机模拟生成。我们将 72 个商品分为低价商品与高价商品两类，其中低价商品数占$\frac{5}{8}$（45 种），其成本为[200,500]间均匀分布的随机数；高价商品数占比为$\frac{3}{8}$（27 种），其成本为[600,1000]间均匀分布的随机数。商品的售价统一定义为生产成本的五倍。

将模拟的商品生产成本与售价输入模型，求解结果展示出我们的模型为了获取最大利润，尽可能地满足高价商品的补货需求：

![成本售价](index-image/成本售价.png)

其中：商品量表示原始的商品池中的商品数量，某一工厂对于某一 DC 进行某一 SKU 的补货，称为一次补货行为，补货量表示某一商品的总补货量。

图 13 展示了尽管高价商品的种数少于低价商品，但最终补货量>0 的数据中，高价商品补货行为量与低价商品补货行为量相等，且高价商品补货量大约是低价商品补货的补货量的 2 倍。这说明为了获取更高的利润，模型尽可能地满足高价商品的补货需求。

### 与基线模型的对比

最后，为了说明优化模型的合理性和实际应用价值，我们将模型与基线模型进行效果对比。我们的基线模型中各工厂各 SKU 的总生产量是固定的，即历史销量的平均值，根据各个工厂的容量等比例分配。我们仅需要决策各个 DC 各 SKU 的补货数量和销售数量，其余约束和目标函数与之前相同。如下所示：

$$
\begin{aligned}
produce(i,k) &= \text {第 i 个工厂生产第 k 个 SKU 的生产量} \\
& = \frac{\text {第 i 个工厂的容量} }{\text {所有工厂的容量总和}}\times \text {第 k 个 SKU 的历史销量的均值} \\
& =\frac{push\_ub_{i}}{\sum_j{push\_ub_{j}}}\times avg\_sales(k)
\end{aligned}
$$

![利润对比](index-image/利润对比.png)

结果显示我们的模型的利润大于基线模型，可以为企业获得额外 500 万元的利润。

此外，在总利润高于基线模型的条件下，优化后的模型的补货行为也更具优势。

![需求分布](index-image/需求分布.png)

从图 15 中可以看出，各 DC 各商品的需求分布呈现长尾分布，即很少的 DC-SKU 组合的需求量很大，绝大多数组合的需求量很小。在这种情况下，我们的补货行为很可能是稀疏的。如图 16 所示：橙色的柱子表示模型对该组合进行了补货，蓝色柱子则表示未进行补货。

![补货行为](index-image/补货行为.png)

图 16 显示我们的模型的补货行为覆盖的 DC 和商品类别更广，且具有较好的长尾发掘能力。对于需求较低的 DC 和商品组合，我们仍有较多的补货行为，而基线模型的补货行为则集中在需求较高的组合上。这体现了我们的补货策略的多样性，以及一定的抗风险能力。这是因为当一些低需求的组合需求突然变高时，我们仍有一定的补货行为，即考虑了市场需求突然变化的情况。

## 结论与展望

为了适应市场的变化以及饮料企业基于其商业决策的动态调整，我们需要对模型开展灵敏度分析，从而帮助饮料企业更好的平衡其成本、收益、风险的决策和应对能力，以便做出更有效、明智的决策。

在本章中，结合实际可行度和企业的投资回报率，我们将对工厂发货下限约束、DC 容量、整托约束进行不同程度的松弛，来观察关键参数的变化对模型输出收益的影响，使得企业更好的理解不同决策选项的风险和回报，有助于优化战略规划、使其更加可持续和适应不确定的市场环境。

### 工厂扩容的回报增益分析

考虑对目前已有的两个工厂扩容，即对工厂发货下限约束进行松弛，实际可行度高。为了适应不同的饮料高低价混合场景，在前节设定的两种价格配比设定下均进行试验，发现结果一致，更说明对此约束进行灵敏度分析的意义可适应饮料价格变化与多种销售决策，普适性强。

分别在初始的工厂发货下限约束下，以 10000 个单位（即 100 立方米）依次减少，查看模型输出收益的变化，如图 17 所示。

![两个工厂发货下限约束松弛与相应回报增益](index-image/工厂发货下限.png)

实验发现，无论是在何种价格配比设定下，对两个工厂扩容均可收获利润增益，且对当前容量较小的 Factory2 而言，扩容大约 10000 个单位（即 100 立方米）都可以获得约 200 万的利润。由图 17 可以发现 Factory2 的回报增益高于 Factory1。结合目前已有工厂容量状态，Factory1 为大型工厂而 Factory2 为小型工厂，对于小型工厂而言，扩容同样的空间、成本相对较低，因此对 Factory2 进行扩容性价比较高、投资回报率更高，能获得更高的回报增益。

### DC 扩容的回报增益分析

考虑对 DC 进行扩容可行度较高，通过实验将所有 DC 同步扩容一定倍数查看模型输出的变化来检验 DC 是否存在扩容空间，以及扩容 DC 能否带来理想的回报增益。

![18 个 DC 同步扩容与相应回报增益](index-image/所有DC同步扩容.png)

由图 18 所示，DC 存在一定的扩容空间，且从实际意义来看工厂到 DC 的运输成本相对较低，并不是影响收益的主要成本，因此扩容带来的回报更有意义。同时，图 18 展示了随着所有 DC 同步扩容倍数增加回报增速逐步变缓，因此建议 DC 扩容在 1.05 至 1.15 倍之间投资回报率较高，扩容到 1.15 倍之后可以将预算配置到其余方面，回报增益会更高。

结合实际可行度，考虑对所有 DC 同步扩容是不现实的且资源浪费，在实际商业决策中，更倾向于挑选投资回报率更高的关键 DC 进行扩容，因此开展实验单独对每个 DC 扩容 50%（即 capacity 为原来的 1.5 倍），可得如图 19 所示。

![18 个 DC 各自扩容 50% 得到的回报增益](index-image/DC单独扩容相同倍数.png)

同比扩容下，能得到明显回报增益的 DC 为 4、5、7、14，查看并总结这些 DC 的特点发现他们具有容量小（仍有扩容空间）、工厂到仓库的运输成本低、历史销量高等特点，价值空间大，因此建议在追加预算成本受限的情况下优先挑选这些 DC 进行扩容性价比更高，以期用较小的成本带来高收益回报。

### 整托容量回报增益分析

考虑约束的合理性，认为整托约束在销售能力较弱的 DC 上造成了一定资源浪费，使得货品无法达到最优的资源配置，为了验证我们这一猜测，开展实验先取消整托约束——即工厂调整补货形式由整托运输改为整箱运输，可得模型输出的利润回报增加了 1400 万左右，能够增加利润。这一预实验不仅证实了我们的猜测，且表明以节省运输成本为目的的整托运输约束限制实际上使得货品供给超出了一些 DC 的销售能力，从而造成一定程度上的资源浪费。

结合实际货物运输情况，整托运输相比整箱运输的优点在于其减少了货品的操作和管理过程从而降低货品处理时间、提高货品装卸效率、简化货品的追踪与管理工作，因此进一步考虑调整整托容量而非取消整托运输为整箱运输。

以一定比例逐步减小整托容量，查看模型输出收益的变化，如图 20 所示。

![整托容量调整与相应回报增益](index-image/整托约束容量调整.png)

由图 20 可得，调整整托容量的确可以增大回报收益，但是随着容量减少比例增加回报增速逐步变缓，因此建议结合实际需求调整整托容量为现有容量的 0.125 至 0.5 倍即可。适当减小整托容量不仅可以优化货品资源配置、提高资源利用率，还能降低运输和存储成本。

## 展望

### 相关参数真实数据缺失

由于缺乏实际的饮料市场上每个 SKU 的生产成本和售价情况的数据，模型的输出在一定程度上可能缺乏真实性。然而，我们通过仿真模拟对这两个方面的数据进行了处理，将它们作为模型的输入参数，从而使得模型能够动态适应市场的变化。通过将生产成本和售价作为输入参数，我们让模型能够在模拟的市场环境中进行推断和预测。这样的设计允许模型考虑不同 SKU 的成本和售价变化对市场表现的影响，进而生成更贴近实际情况的结果。虽然这种仿真模拟无法完全取代真实市场数据的准确性，但它为我们提供了一种近似的方法来评估和优化产品和策略。这也有利于我们不断努力改进模型，以便更好地反映真实市场的复杂性和变化性。

### 滑动窗口预测

在原有的数据集中，供应链优化问题也涉及到关于补货的滞后期，因此我们认为加入滑动窗口预测对提高模型实用性也十分重要。这一技术可以用来预测未来某个时间段（如一周、一个月等）内工厂的生产补货情况。然而，目前由于模型和现有数据的限制，我们尚无法实现滑动预测。
实施滑动窗口预测需要充足的历史数据以及适当的模型架构和算法，这些要求可能超出了目前的数据范围和模型选择。然而，如果我们可以收集到更多的数据，并不断完善模型，那么我们会在有充足数据的前提下进一步做出延展，使得模型能够更准确地预测未来时间段内的工厂生产补货情况，提供更有针对性的决策支持。
