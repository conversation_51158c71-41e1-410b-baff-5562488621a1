---
title: 使用 tqdm 美化进度条
authors: 
  - <PERSON>
date: '2022-12-30'
slug: tqdm
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 在项目中经常遇到耗时较久的循环，`tqdm`可以打印美观的进度条，同时输出循环中的变量信息。
---

# 使用 tqdm 美化进度条

在项目中经常遇到耗时较久的循环，`tqdm`可以打印美观的进度条，同时输出循环中的变量信息。

![高级用法](index-image/高级用法.gif)

<!-- more -->

## 简单用法

下面的方法是我一直使用的：

```python
from tqdm import tqdm
import time

pbar = tqdm(range(10))
for i in pbar:
    time.sleep(1)
    # 输出进度条
    pbar.set_description("正在执行第%s个循环" % i)
```

它可以打印单个循环的进度条。

![简单用法](index-image/简单用法.gif)

## 高阶用法

### 循环结束后隐藏进度条：

```python
pbar = tqdm(range(10), leave=False)
```

### 打印更多信息

```python
from tqdm import tqdm
import time

pbar = tqdm(range(10))
for idx, i in enumerate(pbar):
    time.sleep(0.3)
    acc_ = i * 10
    loss = 1 / ((i + 1) * 10)
    pbar.set_description(f"SimpleLoop [{idx+1}]")
    pbar.set_postfix(dict(acc=f"{acc_}%", loss=f"{loss:.3f}"))
```

![高级用法](index-image/高级用法.gif)
