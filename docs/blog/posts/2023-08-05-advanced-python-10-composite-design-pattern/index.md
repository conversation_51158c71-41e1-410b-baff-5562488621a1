---
title: Python 进阶教程系列 10：组合模式
authors: 
  - <PERSON>
date: '2023-08-05'
slug: advanced-python-10-composite-design-pattern
categories:
  - Python
tags:
  - Python
links:
  - "Composite Design Pattern - Advanced Python Tutorial #10": https://youtu.be/iSG87hpAFhQ
---

# Python 进阶教程系列 10：组合模式

本文是 Python 进阶教程系列 10，主要介绍了 Python 组合模式。

组合模式是一种结构型设计模式，它允许我们将对象组合成树形结构来表示“部分 - 整体”的层次结构。组合模式使得客户端能够以相同的方式处理单个对象和组合对象，从而使得客户端代码更加简单和通用。

![img](index-image/1264910-20171120151113415-350653786.png)


<!-- more -->

## 代码示例

在 Python 中实现组合模式通常需要使用抽象类和继承。我们定义一个抽象基类 `Component`，它包含了组合中的所有对象的通用操作。然后，我们创建两个子类 `Leaf` 和 `Composite`，`Leaf` 表示组合中的单个对象，而 `Composite` 表示组合中的组合对象。

```python
class Component:
    def __init__(self, name):
        self.name = name

    def add(self, component):
        pass

    def remove(self, component):
        pass

    def display(self, depth):
        pass


class Composite(Component):
    def __init__(self, name):
        super().__init__(name)
        self.children = []

    def add(self, component):
        self.children.append(component)

    def remove(self, component):
        self.children.remove(component)

    def display(self, depth):
        print("-" * depth + self.name)
        for child in self.children:
            child.display(depth + 2)


class Leaf(Component):
    def display(self, depth):
        print("-" * depth + self.name)


# Build a tree structure
root = Composite("root")
root.add(Leaf("Leaf A"))
root.add(Leaf("Leaf B"))

comp = Composite("Composite X")
comp.add(Leaf("Leaf XA"))
comp.add(Leaf("Leaf XB"))

root.add(comp)
root.add(Leaf("Leaf C"))

# Display tree
root.display(1)
```

```python
# -root
# ---Leaf A
# ---Leaf B
# ---Composite X
# -----Leaf XA
# -----Leaf XB
# ---Leaf C
```

组合模式可以很好地应用于那些需要处理树形结构的场景，例如操作系统的文件系统、图形用户界面中的控件等。它允许我们以一种通用的方式处理单个对象和组合对象，并能够简化客户端代码，提高代码的可维护性和可扩展性。

!!! note "Python 进阶教程系列文章"

	- [x] [Python 进阶教程系列 1：双下划线的魔法方法](../advanced-python-1-magic-method/)
	
	- [x] [Python 进阶教程系列 2：装饰器](../advanced-python-2-decorator/)
	
	- [x] [Python 进阶教程系列 3：生成器](../advanced-python-3-generator/)
	
	- [x] [Python 进阶教程系列 4：命令行参数](../advanced-python-4-argument-parsing/)
	
	- [x] [Python 进阶教程系列 5：获取和修改被保护的属性](../advanced-python-5-encapsulation/)
	
	- [x] [Python 进阶教程系列 6：使用 `mypy` 进行类型提示](../advanced-python-6-type-hinting/)
	
	- [x] [Python 进阶教程系列 7：工厂模式](../advanced-python-7-factory-design-pattern/)
	
	- [x] [Python 进阶教程系列 8：代理模式](../advanced-python-8-proxy-design-pattern/)
	
	- [x] [Python 进阶教程系列 9：单例模式](../advanced-python-9-singleton-design-pattern/)
	
	- [x] [Python 进阶教程系列 10：组合模式](../advanced-python-10-composite-design-pattern/)

