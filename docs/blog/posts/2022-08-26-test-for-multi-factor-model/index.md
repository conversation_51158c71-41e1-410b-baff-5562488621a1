---
title: 检验多因子模型
authors: 
  - Jeremy Feng
date: '2022-08-26'
slug: test-for-multi-factor-model
categories:
  - 量化研究
tags:
  - 量化研究
toc: yes
katex: true
description: "多因子模型可以用时间序列回归、截面回归和 Fama-Macbeth 回归进行检验。它们的适用情景不同，所得到的结果也需要不同的解读。"
---

# 检验多因子模型

$$
R_{it}^e =\alpha_i+\beta_i ^\prime \lambda_t+\varepsilon_{it},\quad t=1,2,\dots, T
$$

多因子模型可以用时间序列回归、截面回归和 Fama-Macbeth 回归进行检验。它们的适用情景不同，所得到的结果也需要不同的解读。

多因子模型反映的是资产的预期收益率和因子暴露在截面上的关系。它回答了这样的问题：在同一时刻，为什么有的资产收益率高，有的资产收益率低？

多因子模型认为，每个因子有一个预期收益率，资产在某个因子上的暴露让该资产可以获得相应的预期收益。不同资产在各个因子上的暴露程度不同，导致了不同资产有着不同的预期收益。

如果有一个多因子模型，它能很好地解释资产收益横截面差异，那么我们只需关心：

1. 各个资产在各个因子上的暴露程度（$N\times K$）。它是一个矩阵，矩阵的每一行记为$\beta_i^\prime$，其中$i=1, 2, \dots, N$。
2. 各个因子的预期收益率（$K\times 1$），记为$\lambda$。它是一个列向量，第$k$个元素是$\lambda _k$，代表第$k$个因子的预期收益率。

下面梳理可以对多因子模型进行检验的三种方法。

<!-- more -->

## 时间序列回归

时间序列回归适用于因子收益率已知的情况。关键的步骤如下：

### 第一步：用时间序列回归确定资产的因子暴露

用因子收益率（这是真实已知的，而不是预期的）作为自变量，用资产的超额收益作为因变量，做**时间序列回归**。也就是说，对每一个资产$i$，都运行一次下面的回归模型：
$$
R_{it}^e =\alpha_i+\beta_i ^\prime \lambda_t+\varepsilon_{it},\quad t=1,2,\dots, T
\tag{1}\label{1}
$$

- 上式可以求出资产$i$在各因子上的暴露程度$\beta_i$。

> 形象地理解就是：当因子收益率在时间序列上变动时，资产收益率随之变动的敏感程度有多大？

- 还可以求出资产$i$的定价错误，即$\alpha_i$。

### 第二步：将因子收益率取平均，得到因子的预期收益率

2. 每个因子的预期收益率，就等于这个因子已知的收益率在时间序列上的均值。

注意在第 1 步的回归当中，用到了真实已知的因子收益率，所以**时间序列回归只适用于那些已知收益率的因子**，比如 HML、SMB 这类风格因子，它们可以用排序法模拟投资组合（小市值股票的收益率减去大市值股票的收益率），进而得到因子的收益率。

> 对于 GDP 等宏观因子，没办法直接得到因子收益率，所以不能用时间序列回归来检验定价错误。

比如 GDP 在连续三年的时间序列是 0.9 万亿、1 万亿、1.1 万亿，怎么将这些数据用到上式中？如果直接把 0.9、1、1.1 当作$\lambda$，那么$\hat\beta_i$代表：如果 GDP 增加 1（万亿），资产$i$的超额收益会增加多少？注意这里的$\hat\alpha_i$并不是定价错误，而是为了拟合回归直线而产生的截距项。试想，如果$\lambda$的单位变成亿，也就是将 9000、10000、11000 代入回归模型中，那么$\hat\beta_i$的值会缩小为原来的 1/10000，但$\hat\alpha_i$的值不会变。

那些天然已知因子收益率的因子，它们的$\lambda$就是收益率，例如 10%、20% 等，在量纲和单位上就和$R_{it}^e$是一致的，这样进行回归，$\alpha_i$的意义才是定价错误。

## 截面回归

截面回归可以处理因子收益率未知的情况。关键步骤如下：

### 第一步：用时间序列回归确定资产的因子暴露


这一步用到的回归模型是：
$$
R_{it}^e =a_i+\beta_i^ \prime f_t+\varepsilon_{it},\quad t=1,2,\dots, T
\tag{2}\label{2}
$$
注意，公式$\eqref{1}$和公式$\eqref{2}$略有区别。

- 公式$\eqref{1}$中$\alpha_i$的意义是定价错误。公式$\eqref{2}$用了$a_i$是为了拟合回归直线而产生的截距项，因为自变量并不是因子收益率，所以$a_i$的意义并不是定价错误。
- 公式$\eqref{2}$中的$f_t$是因子的取值，而不是因子的收益率$\lambda_t$。因为我们不知道因子的收益率，只知道因子的取值。

> 这一步的目的是求出资产的因子暴露$\hat\beta_i$。

### 第二步：先对超额收益取平均，再截面回归

2. 对于每个资产，我们通过第 1 步得到了因子暴露$\hat\beta_i$，把它作为自变量。因变量为每个资产的超额收益在时间序列上的平均值$E_T[R_{i}^e]$。

   每个资产有一个数据点，横坐标是$\hat\beta_i$，纵坐标是$E_T[R_{i}^e]$。对所有资产进行截面回归，求出因子预期收益率。

这一步用到的回归模型是：
$$
E_T\left[R_{i} ^e\right]=\hat\beta_i ^\prime \lambda+\alpha_i,\quad i=1,2,\dots, N
\tag{3}\label{3}
$$

> 这一步的目的是求出因子的预期收益率$\lambda$，还能求出资产的定价错误$\alpha_i$。

公式$\eqref{3}$没有截距项，这是因为多因子模型假设当不存在模型偏误时，资产的预期收益率因该仅由因子的预期收益率决定。换句话说，假设这些因子能够完美地解释资产的预期收益，如果一个资产在所有因子上的暴露均为 0，那么这个资产的预期收益也应该是 0（假设没有定价错误）。如果有截距项，就代表所有资产的预期收益率都系统性地有着无法被因子预期收益率解释的部分。也有学者使用带截距项的回归（Cocharane, 2005）。

## 时间序列回归与截面回归的差异

### 适用的因子类型不同

上面已经介绍，时间序列回归只适用于那些已知收益率的因子（例如风格因子，HML），而截面回归既能处理已知收益率的因子，也能处理未知收益率的因子（例如宏观因子，GDP）。

### 得到的因子收益率的内涵不同

那么，对于风格因子 HML，既可以用时间序列回归，也可以用截面回归，两种方法得到的因子预期收益率有什么区别？

1. 用时间序列回归得到的因子预期收益率，就是每个时期的因子收益率的平均值。

> 对于某个$t$时刻，HML 因子的收益率的算法：
>
> 1. 学术上的做法：将账面市值比最高的 30% 的股票（High book-to-market）的收益率减去账面市值比最低的 30% 的股票（Low book-to-market）的收益率。
> 2. 业界简单做法：做多账面市值比最高的 10% 的股票（市值加权构造多头组合），做空账面市值比最低的 10% 的股票（市值加权构造空头组合），多空组合的收益就是 HML 因子的收益率。
>
> 对于其他$t$时刻，用同样的算法计算因子收益率。最后将所有$t$时刻的因子收益率求平均值，得到 HML 因子的预期收益率。

2. 用截面回归得到的因子预期收益率，是公式$\eqref{3}$中的$\lambda$。$\lambda$中的元素排除了其他因子的干扰，是各个因子纯自身的预期收益率。

$\lambda$的第$k$个元素是第$k$个因子的收益率，它可以理解成一个特殊的投资组合的收益率。这个特殊的投资组合满足如下性质：

- 对第$k$个因子的暴露为 1；
- 对其他因子的暴露为 0。

上面两个优秀的性质使得将$\lambda_k$作为第$k$个因子的预期收益率可以排除其他因子的干扰，因而纯粹是第$k$个因子自身的预期收益率。

> 关于截面回归得到的$\lambda$为什么能控制在其他因子上的暴露，可以参考《因子投资 方法与实践》P43-P44。

![image-20220825142521877](index-image/image-20220825142521877.png)

![image-20220825142534054](index-image/image-20220825142534054.png)

## Fama-Macbeth 回归

Fama-Macbeth 回归在业界被广泛应用。关键步骤如下：

### 第一步：用时间序列回归确定资产的因子暴露

第一步和截面回归相同，也是对$N$个资产做$N$次时间序列回归，得到每个资产$i$在全部因子上的暴露$\hat\beta_i$。

### 第二步：对每个时点做截面回归，再将因子预期收益率和定价错误的估计值取平均

第二步，在每个时间点$t$上，以资产的超额收益率$R_{it}^e$作为因变量，以$\hat\beta_i$作为自变量，进行截面回归。一共有$T$个时间点，因此一共进行$T$次截面回归。

在第$t$期，用到的回归模型如下：
$$
R_{it} ^e=\hat \beta_i ^\prime \lambda_t+\alpha_{it},\quad i=1,2,\dots, N
\tag{4}\label{4}
$$

将$T$次截面回归得到的$T$个$\hat\lambda_t$和$\hat\alpha_{it}$在时间序列上求平均值，作为因子的预期收益率和资产$i$的定价错误。
$$
\hat\lambda=\frac{1}{T}\sum_{t=1}^{T}\hat\lambda_t\\\
\hat\alpha_{i}=\frac{1}{T}\sum_{t=1}^{T}\hat\alpha_{it}
$$

## 截面回归和 Fama-Macbeth 回归的区别

### 从回归过程上

- 截面回归是先对各资产$T$期的的超额收益求均值，再估计因子预期收益率和定价错误；
- Fama-Macbeth 回归是先用各资产单期的超额收益估计因子预期收益率和定价错误，再对$T$期的因子预期收益率和定价错误的估计值求均值。

### 从$\hat\beta_i$的时变性上

- 截面回归中的$\hat\beta_i$在各期是不变的；
- Fama-Macbeth 回归中的$\hat\beta_i$可以在时间序列上发生变化。例如，用滑动窗口得到$\hat\beta_{it}$，就可以在 Fama-Macbeth 回归的每一个回归中使用不同的$\hat\beta_{i}$。

当截面回归中的自变量$\hat\beta_i$在全部$T$期上不变时，两种方法得到的估计是相同的，即“先均值，再估计”和“先估计，再回归”对估计的结果没有影响。

但是，Fama-Macbeth 回归进行了$T$次截面回归，其中的每次截面回归都有$\hat\beta_i$。如果我们允许$\hat\beta_i$在不同时点上发生变化，那么 Fama-Macbeth 回归可以更灵活地捕捉$\hat\beta_i$在不同时点上发生的变化。

因此，我们不需要规定某个资产在某个因子上的暴露一直是某个数值，而是可以允许因子暴露程度发生变化，这使得 Fama-Macbeth 回归在实际研究和投资实践中的应用更广泛。

### 从检验因子预期收益率和定价错误的过程上

- 截面回归只得到一个因子预期收益率$\hat\lambda$和定价错误$\hat\alpha_{i}$的估计值，其标准误公式较为复杂。结合估计值和标准误，再用$\chi^2$统计量和$t$统计量进行检验，过程较复杂。

- Fama-Macbeth 回归可以得到$T$个因子预期收益率$\hat\lambda_t$和定价错误$\hat\alpha_{it}$的估计值，对其求均值和标准误，就可以方便地检验其是否显著不为 0。
