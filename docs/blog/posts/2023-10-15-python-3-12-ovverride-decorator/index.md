---
title: Python 3.12 新特性：@override 装饰器
authors: 
  - <PERSON>
date: '2023-10-15'
slug: python-3-12-ovverride-decorator
categories:
  - Python
tags:
  - Python
links:
  - "官方文档 typing.override": https://docs.python.org/3/library/typing.html#typing.override
  - "Bilibili - Python 3.12 已经可以用@override 了！！！": https://www.bilibili.com/video/BV1Zm4y1g74j/
---

# Python 3.12 新特性：`@override` 装饰器

Python 3.12 引入了 `@override` 装饰器，可以用来指定该方法是用来覆盖基类方法的。

## 新特性简介

在继承基类后，如果我们想覆盖基类中的某个方法 `original()`，我们可以改写该方法。然而，如果我们不小心将方法名拼写错误为 `ooooriginal()`，即一个基类中不存在的方法，那么当调用子类的 `original()` 方法时，实际上会调用基类中的方法，而且程序不会产生错误。这种情况下，我们可能无法察觉到问题的存在。

为了解决这个问题，我们可以使用 `@override` 注解来明确表示方法的覆盖关系。这样程序会检查 `ooooriginal()` 方法是否在基类中存在。由于 `ooooriginal()` 方法并未在基类中定义，程序会报错，从而帮助我们发现错误。

简而言之，使用 `@override` 注解可以帮助我们检测覆盖方法是否正确，避免潜在的错误。

<!-- more -->

## 视频介绍

<iframe src="//player.bilibili.com/player.html?aid=704637823&bvid=BV1Zm4y1g74j&cid=1300420263&p=1&autoplay=0" allowfullscreen="allowfullscreen" width="100%" height="500" scrolling="no" frameborder="0" sandbox="allow-top-navigation allow-same-origin allow-forms allow-scripts" &autoplay=0> </iframe>

## 官方文档代码示例

```python
from typing import override


class Base:
    def log_status(self) -> None:
        ...


class Sub(Base):
    @override
    def log_status(self) -> None:  # Okay: overrides Base.log_status
        ...

    @override
    def done(self) -> None:  # Error reported by type checker
        ...
```

