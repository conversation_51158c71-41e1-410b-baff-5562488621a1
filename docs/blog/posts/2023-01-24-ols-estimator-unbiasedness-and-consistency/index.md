---
title: 普通最小二乘估计的无偏性和一致性
authors:
  - <PERSON>
date: "2023-01-24"
slug: ols-estimator-unbiasedness-and-consistency
categories:
  - 统计
  - 机器学习
  - 量化研究
tags:
  - 统计
  - 机器学习
  - 量化研究
links:
  - blog/posts/2023-01-23-ols-estimator/index.md
  - blog/posts/2023-01-25-ols-estimator-variance-and-gauss-markov-theorem/index.md
  - blog/posts/2023-01-30-ols-estimator-assumptions/index.md
---

# 普通最小二乘估计的无偏性和一致性

本文证明了普通最小二乘估计的无偏性和一致性。

无偏性：

$$
E\left[\beta^{O L S}\right]=\beta
$$

一致性

$$
\beta^{O L S}-\beta=\left(X^{\prime} X\right)^{-1} X^{\prime} u \stackrel{p}{\rightarrow} 0  \text { as } T \rightarrow \infty
$$

<!-- more -->

## 无偏性

为了证明普通最小二乘估计的无偏性，我们只需证明：

$$
E\left[\beta^{O L S}\right]=\beta
$$

或

$$
E\left[\beta^{O L S}-\beta\right]=0
$$

### 将普通最小二乘估计量中的$Y$展开为$X\beta+\mu$：

$$
\begin{aligned}
\beta^{O L S} & =\left(X^{\prime} X\right)^{-1} X^{\prime} Y \\
& =\left(X^{\prime} X\right)^{-1} X^{\prime}(X \beta+u) \\
& =\left(X^{\prime} X\right)^{-1} X^{\prime} X \beta+\left(X^{\prime} X\right)^{-1} X^{\prime} u \\
& =\beta+\left(X^{\prime} X\right)^{-1} X^{\prime} u
\end{aligned}
$$

### 再利用”误差的条件均值为零“这一假设，即$E[u \mid X]=0$

$$
\begin{aligned}
E\left[\beta^{O L S} \mid X\right] & =E\left[\beta+\left(X^{\prime} X\right)^{-1} X^{\prime} u \mid X\right] \\
& =\beta+E\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u \mid X\right] \\
& =\beta+\left(X^{\prime} X\right)^{-1} X^{\prime} E[u \mid X]\\
& =\beta
\end{aligned}
$$

### 最后根据迭代期望法则，证明无偏性

$$
E\left[\beta^{O L S}\right]=E\left[E\left[\beta^{O L S} \mid X\right]\right]=\beta
$$

## 一致性

为了证明一致性，我们只需证明：

$$
\beta^{O L S}-\beta=\left(X^{\prime} X\right)^{-1} X^{\prime} u \stackrel{p}{\rightarrow} 0 ? \text { as } T \rightarrow \infty
$$

### 在表达式中添加$\frac{1}{T}$，凑成平均的形式

$$
\beta^{O L S}-\beta=\left(\frac{1}{T} X^{\prime} X\right)^{-1} \frac{1}{T} X^{\prime} u
$$

!!! warning "注意"
括号的部分需要求逆，因此括号里面乘以$\frac{1}{T}$等价于在括号外面乘以$T$。

### 将$X^{\prime} X$转换成“单个样本向量相乘得到矩阵，再求和”的形式

$$
\begin{aligned}
 X^{\prime} X&=\left[\begin{array}{cccc}
1 & 1 & \ldots & 1 \\
X_{11} & X_{21} & & X_{T 1} \\
X_{12} & X_{22} & & X_{T 2} \\
\ldots & \ldots & & \ldots \\
X_{1 K} & X_{2 K} & & X_{T K}
\end{array}\right]\left[\begin{array}{ccccc}
1 & X_{11} & X_{12} & \ldots & X_{1 K} \\
1 & X_{21} & X_{22} & \ldots & X_{2 K} \\
\ldots & \ldots & \ldots & \ldots & \ldots \\
1 & X_{T 1} & X_{T 2} & \ldots & X_{T K}
\end{array}\right] \\
& =\left[\begin{array}{ccccc}
\mid & \mid & \mid & & \mid \\
X_1 & X_2 & X_3 & \ldots & X_T \\
\mid & \mid & \mid & & \mid
\end{array}\right]\left[\begin{array}{ccc}
- & X_1 & - \\
- & X_2 & - \\
- & X_3 & - \\
- & X_T & -
\end{array}\right]\\
&=\sum_{t=1} ^T \underbrace{X_t} _{(K+1) \times 1} * \underbrace{X_t ^ {\prime}} _ {1 \times(K+1)} \\
\end{aligned}
$$

### 将$X^{\prime} \mu$转换成“单个样本向量相乘得到向量，再求和”的形式

$$
\begin{aligned}
X^{\prime} u & =\left[\begin{array}{cccc}
1 & 1 & \cdots & 1 \\
X_{11} & X_{21} & & X_{T 1} \\
X_{12} & X_{22} & & X_{T 2} \\
\ldots & \cdots & & \ldots \\
X_{1 K} & X_{2 K} & & X_{T K}
\end{array}\right]\left[\begin{array}{c}
u_1 \\
u_2 \\
\ldots \\
u_T
\end{array}\right] \\
& =\left[\begin{array}{ccccc}
\mid & \mid & \mid & & \mid \\
X_1 & X_2 & X_3 & \ldots & X_T \\
\mid & \mid & \mid & & \mid
\end{array}\right]\left[\begin{array}{c}
u_1 \\
u_2 \\
\ldots \\
u_T
\end{array}\right] \\
& =\sum_{t=1}^T \underbrace{X_t}_{(K+1) \times 1} * \underbrace{u_t}_{1 \times 1} \\
&
\end{aligned}
$$

### 当样本量足够大时，平均值趋近于期望值

$$
\begin{gathered}
\beta^{O L S}-\beta=\left(\frac{1}{T} X^{\prime} X\right)^{-1} \frac{1}{T} X^{\prime} u \\
\frac{1}{T} X^{\prime} X=\frac{1}{T} \sum_{t=1}^T X_t X_t^{\prime} \stackrel{P}{\rightarrow} E\left[X_t X_t^{\prime}\right] \text { as } T \rightarrow \infty \\
\frac{1}{T} X^{\prime} u=\frac{1}{T} \sum_{t=1}^T X_t u_t \stackrel{P}{\rightarrow} E\left[X_t u_t\right] \text { as } T \rightarrow \infty
\end{gathered}
$$

### 再利用”误差的条件均值为零“这一假设，即$E[u \mid X]=0$

$$
\begin{aligned}
E[u \mid X]&=0\\
\Rightarrow E\left[X_t u_t\right]&=E_{X} \left[E\left[X_t u_t \mid X \right] \right]\\
&=E_{X} \left[X_t E\left[u_t \mid X \right] \right]\\
&=E_{X} [\underbrace{X_t}_{(K+1) \times 1} \underbrace{0}_{1 \times 1}]\\
&=\underbrace{0}_{(K+1) \times 1}
\end{aligned}
$$

因此

$$
\begin{aligned}
\beta^{O L S}-\beta&=  (\underbrace{\frac{1}{T} X^ {\prime} X} _ {E\left[X_t X_t ^ {\prime}\right]}) ^{-1} \underbrace{\frac{1}{T} X^ {\prime} u}_ {E\left[X_t u_t\right]}\\\
& \stackrel{p}{\rightarrow}\left(E\left[X_t X_t ^ {\prime}\right]\right) ^ {-1} E\left[X_t u_t\right] \\\
& \stackrel{p}{\rightarrow} \underbrace{0} _ {(K+1) \times 1}
\end{aligned}
$$
