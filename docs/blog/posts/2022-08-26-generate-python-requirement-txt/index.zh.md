---
title: 为 Python 项目生成 requirement.txt
authors: 
  - <PERSON>
date: '2022-08-26'
slug: generate-python-requirement-txt
categories:
  - Python
tags:
  - Python
toc: yes
description: "在部署项目时，可能需要生成 requirement.txt 文件，用于指定对某些包的依赖。Python 的`pipreqs`库可以方便地一键生成 requirement.txt 文件。"
links:
  - "stack overflow: Automatically create requirements.txt": https://stackoverflow.com/questions/31684375/automatically-create-requirements-txt
---

# 为 Python 项目生成 requirement.txt

在部署项目时，可能需要生成 requirement.txt 文件，用于指定对某些包的依赖。Python 的`pipreqs`库可以方便地一键生成 requirement.txt 文件。

<!-- more -->

## 方法

在`Anaconda Prompt`中输入`pipreqs /path/to/project`即可。

![image-20221106194408855](index.zh-image/image-20221106194408855.png)

## 使用`pipreqs`库一键生成`requirement.txt`文件

[stackoverflow](https://stackoverflow.com/questions/31684375/automatically-create-requirements-txt)上的回答是：

```bash
pip install pipreqs

pipreqs /path/to/project
```

## 可能的报错

运行上述代码，有可能会遇到三个报错：

### 字符错误

`UnicodeDecodeError: 'gbk' codec can't decode byte 0xaf in position 167: illegal multibyte sequence`

![image-20220826111101880](index.zh-image/image-20220826111101880.png)

> 解决办法：在命令后面加上`--encoding=utf8`。

```bash
pipreqs /path/to/project --encoding=utf8
```

### 网络环境错误

`requests.exceptions.SSLError: HTTPSConnectionPool(host='pypi.python.org', port=443): Max retries exceeded with url: /pypi/Pillow/json (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))`

![image-20221106194230256](index.zh-image/image-20221106194230256.png)

> 解决办法：关闭代理。

### 不是内部或外部命令

如果提示`不是内部或外部命令`，则需要把 Python Scripts 文件夹添加到系统环境变量：

![img](index.zh-image/508332-20210629173158029-1380940352.png)

```
C:\Users\<USER>\AppData\Roaming\Python\Python39\Scripts
```

![image-20221023184917110](index.zh-image/image-20221023184917110.png)