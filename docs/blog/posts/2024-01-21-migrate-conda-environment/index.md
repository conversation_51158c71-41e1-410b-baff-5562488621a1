---
title: 迁移 Conda 虚拟环境
authors: 
  - <PERSON>
date: '2024-01-21'
slug: migrate-conda-environment
categories:
  - Python
tags:
  - Python
links:
  - "Anaconda 不同平台，不同电脑之间的环境同步": https://www.jianshu.com/p/0cd679dae26d
  - "Conda 导入环境时 ResolvePackageNotFound 错误": https://blog.csdn.net/weixin_42240667/article/details/115773535
---

# 迁移 Conda 虚拟环境

在一个设备上搭建了 Conda 虚拟环境后，如果需要在另一个设备上使用相同的环境，可以制作 `environment.yml` 文件，方便快速地迁移，而不需要再次手动安装包。

本文记录了将 macOS 的 Conda 虚拟环境迁移到 Linux 的过程。

![image-20240121224354485](index-image/image-20240121224354485.png)

<!-- more -->

## 制作 `environment.yml` 文件

参考：[Anaconda 不同平台，不同电脑之间的环境同步](https://www.jianshu.com/p/0cd679dae26d)。

在 macOS 上输入：

```bash title="制作 environment.yml 文件"
conda activate py310 # py310 是已经搭建好的环境名称
conda env export > environment.yml
```

导出的`environment.yml`文件的内容：

```yaml  title="environment.yml"
name: py310
channels:
  - conda-forge
  - bioconda
  - defaults
dependencies:
  - appnope=0.1.3=pyhd8ed1ab_0
  - asttokens=2.4.1=pyhd8ed1ab_0
  - bzip2=1.0.8=h93a5062_5
  - ca-certificates=2023.11.17=hf0a4a13_0
  - comm=0.2.1=pyhd8ed1ab_0
  - debugpy=1.8.0=py310h1253130_1
  - decorator=5.1.1=pyhd8ed1ab_0
  - exceptiongroup=1.2.0=pyhd8ed1ab_2
  - executing=2.0.1=pyhd8ed1ab_0
  - importlib-metadata=7.0.1=pyha770c72_0
  - importlib_metadata=7.0.1=hd8ed1ab_0
  - ipykernel=6.29.0=pyh3cd1d5f_0
  - ipython=8.20.0=pyh707e725_0
  - jedi=0.19.1=pyhd8ed1ab_0
  - jupyter_client=8.6.0=pyhd8ed1ab_0
  - jupyter_core=5.7.1=py310hbe9552e_0
  - libcxx=16.0.6=h4653b0c_0
  - libffi=3.4.2=h3422bc3_5
  - libsodium=1.0.18=h27ca646_1
  - libsqlite=3.44.2=h091b4b1_0
  - libzlib=1.2.13=h53f4e23_5
  - matplotlib-inline=0.1.6=pyhd8ed1ab_0
  - ncurses=6.4=h463b476_2
  - nest-asyncio=1.5.9=pyhd8ed1ab_0
  - openssl=3.2.0=h0d3ecfb_1
  - packaging=23.2=pyhd8ed1ab_0
  - parso=0.8.3=pyhd8ed1ab_0
  - pexpect=4.8.0=pyh1a96a4e_2
  - pickleshare=0.7.5=py_1003
  - pip=23.3.2=pyhd8ed1ab_0
  - platformdirs=4.1.0=pyhd8ed1ab_0
  - prompt-toolkit=3.0.42=pyha770c72_0
  - psutil=5.9.8=py310hd125d64_0
  - ptyprocess=0.7.0=pyhd3deb0d_0
  - pure_eval=0.2.2=pyhd8ed1ab_0
  - pygments=2.17.2=pyhd8ed1ab_0
  - python=3.10.13=h2469fbe_1_cpython
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python_abi=3.10=4_cp310
  - pyzmq=25.1.2=py310hbb13138_0
  - readline=8.2=h92ec313_1
  - setuptools=69.0.3=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - stack_data=0.6.2=pyhd8ed1ab_0
  - tk=8.6.13=h5083fa2_1
  - tornado=6.3.3=py310h2aa6e3c_1
  - traitlets=5.14.1=pyhd8ed1ab_0
  - typing_extensions=4.9.0=pyha770c72_0
  - wcwidth=0.2.13=pyhd8ed1ab_0
  - wheel=0.42.0=pyhd8ed1ab_0
  - xz=5.2.6=h57fd34a_0
  - zeromq=4.3.5=h965bd2d_0
  - zipp=3.17.0=pyhd8ed1ab_0
  - pip:
      - annotated-types==0.6.0
      - anyio==4.2.0
      - cachetools==5.3.2
      - certifi==2023.11.17
      - charset-normalizer==3.3.2
      - click==8.1.7
      - colorama==0.4.6
      - conda-pack==0.7.1
      - contourpy==1.2.0
      - cycler==0.12.1
      - distro==1.9.0
      - fastapi==0.109.0
      - fonttools==4.47.2
      - h11==0.14.0
      - httpcore==1.0.2
      - httpx==0.26.0
      - idna==3.6
      - ipywidgets==8.1.1
      - jinja2==3.1.3
      - joblib==1.3.2
      - jsonpatch==1.33
      - jsonpointer==2.4
      - jupyterlab-widgets==3.0.9
      - kiwisolver==1.4.5
      - markdown-it-py==3.0.0
      - markupsafe==2.1.4
      - marvin==2.1.2
      - matplotlib==3.8.2
      - mdurl==0.1.2
      - numpy==1.26.3
      - openai==1.8.0
      - pandas==1.5.3
      - pillow==10.2.0
      - pretty-errors==1.2.25
      - pydantic==2.5.3
      - pydantic-core==2.14.6
      - pydantic-settings==2.1.0
      - pyparsing==3.1.1
      - python-dotenv==1.0.0
      - pytz==2023.3.post1
      - regex==2023.12.25
      - requests==2.31.0
      - rich==13.7.0
      - scikit-learn==1.4.0
      - scipy==1.11.4
      - sniffio==1.3.0
      - starlette==0.35.1
      - threadpoolctl==3.2.0
      - tiktoken==0.5.2
      - tqdm==4.66.1
      - typer==0.9.0
      - tzdata==2023.4
      - urllib3==2.1.0
      - uvicorn==0.26.0
      - widgetsnbextension==4.0.9
prefix: /Users/<USER>/miniconda3/envs/py310
```

!!! note

	1. 我在 `channels `中手动添加了清华源，希望可以更快速地下载包。
	
	2. 我将最后一行 `prefix` 删除了，因为觉得它可能没什么用。

## 解决 `ResolvePackageNotFound` 报错

如果遇到 `ResolvePackageNotFound` 报错，可以参考[Conda 导入环境时 ResolvePackageNotFound 错误](https://blog.csdn.net/weixin_42240667/article/details/115773535)，将“包名称第二个等号及后面的内容”删除。

最终放到 Linux 系统上的 `environment.yml` 文件为：

```yaml  title="environment.yml"
name: py310
channels:
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/pro
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2
  - bioconda
  - defaults
dependencies:
  - appnope=0.1.3
  - asttokens=2.4.1
  - bzip2=1.0.8
  - ca-certificates=2023.11.17
  - comm=0.2.1
  - debugpy=1.8.0
  - decorator=5.1.1
  - exceptiongroup=1.2.0
  - executing=2.0.1
  - importlib-metadata=7.0.1
  - importlib_metadata=7.0.1
  - ipykernel=6.29.0
  - ipython=8.20.0
  - jedi=0.19.1
  - jupyter_client=8.6.0
  - jupyter_core=5.7.1
  - libcxx=16.0.6
  - libffi=3.4.2
  - libsodium=1.0.18
  - libsqlite=3.44.2
  - libzlib=1.2.13
  - matplotlib-inline=0.1.6
  - ncurses=6.4
  - nest-asyncio=1.5.9
  - openssl=3.2.0
  - packaging=23.2
  - parso=0.8.3
  - pexpect=4.8.0
  - pickleshare=0.7.5
  - pip=23.3.2
  - platformdirs=4.1.0
  - prompt-toolkit=3.0.42
  - psutil=5.9.8
  - ptyprocess=0.7.0
  - pure_eval=0.2.2
  - pygments=2.17.2
  - python=3.10.13
  - python-dateutil=2.8.2
  - python_abi=3.10=4_cp310
  - pyzmq=25.1.2
  - readline=8.2
  - setuptools=69.0.3
  - six=1.16.0
  - stack_data=0.6.2
  - tk=8.6.13
  - tornado=6.3.3
  - traitlets=5.14.1
  - typing_extensions=4.9.0
  - wcwidth=0.2.13
  - wheel=0.42.0
  - xz=5.2.6
  - zeromq=4.3.5
  - zipp=3.17.0
  - pip:
      - annotated-types==0.6.0
      - anyio==4.2.0
      - cachetools==5.3.2
      - certifi==2023.11.17
      - charset-normalizer==3.3.2
      - click==8.1.7
      - colorama==0.4.6
      - conda-pack==0.7.1
      - contourpy==1.2.0
      - cycler==0.12.1
      - distro==1.9.0
      - fastapi==0.109.0
      - fonttools==4.47.2
      - h11==0.14.0
      - httpcore==1.0.2
      - httpx==0.26.0
      - idna==3.6
      - ipywidgets==8.1.1
      - jinja2==3.1.3
      - joblib==1.3.2
      - jsonpatch==1.33
      - jsonpointer==2.4
      - jupyterlab-widgets==3.0.9
      - kiwisolver==1.4.5
      - markdown-it-py==3.0.0
      - markupsafe==2.1.4
      - marvin==2.1.2
      - matplotlib==3.8.2
      - mdurl==0.1.2
      - numpy==1.26.3
      - openai==1.8.0
      - pandas==1.5.3
      - pillow==10.2.0
      - pretty-errors==1.2.25
      - pydantic==2.5.3
      - pydantic-core==2.14.6
      - pydantic-settings==2.1.0
      - pyparsing==3.1.1
      - python-dotenv==1.0.0
      - pytz==2023.3.post1
      - regex==2023.12.25
      - requests==2.31.0
      - rich==13.7.0
      - scikit-learn==1.4.0
      - scipy==1.11.4
      - sniffio==1.3.0
      - starlette==0.35.1
      - threadpoolctl==3.2.0
      - tiktoken==0.5.2
      - tqdm==4.66.1
      - typer==0.9.0
      - tzdata==2023.4
      - urllib3==2.1.0
      - uvicorn==0.26.0
      - widgetsnbextension==4.0.9
```

在 Linux 系统上输入：

```bash
conda env create -f environment.yml
```

等待安装即可。

## 验证虚拟环境

本次迁移虚拟环境是为了使用 Python 3.10 下的 `marvin` 包，我们可以在新建的 `py310` 环境中导入它：

![image-20240121224354485](index-image/image-20240121224354485.png)
