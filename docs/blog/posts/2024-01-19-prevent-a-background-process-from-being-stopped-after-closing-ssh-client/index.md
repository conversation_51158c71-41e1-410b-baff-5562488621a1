---
title: 断开 SSH 连接后继续执行任务
authors: 
  - <PERSON>
date: '2024-01-19'
slug: prevent-a-background-process-from-being-stopped-after-closing-ssh-client
categories:
  - Linux
tags:
  - Linux
links:
  - "How do I put an already-running process under nohup?": https://stackoverflow.com/questions/625409/how-do-i-put-an-already-running-process-under-nohup
  - "Linux 系统下如何保持进程在 SSH 客户端断开后仍继续运行？": https://www.cnblogs.com/xiaotong-sun/p/17461218.html
---

# 断开 SSH 连接后继续执行任务

我们经常需要通过 SSH 连接到远程 Linux 服务器来执行各种任务。但有时，我们希望即使在断开 SSH 连接后，这些任务也能继续运行。

本文介绍了如何将任务放入后台并使用 `disown` 命令使其在当前 Shell 终端窗口关闭后依然不会结束。

![image-20240119101805146](index-image/image-20240119101805146.png)

<!-- more -->

## 事前使用 `nohup` 命令

`nohup`，可以使运行的命令忽略 SIGHUP 信号。因此，即使退出登录，程序仍旧会继续执行。通常情况下，在 `nohup` 命令尾部加上 `&` 字符，才能将命令放至后台执行。具体示例如下所示：

```bash
nohup python hello.py > nohup.out &
```

在命令头尾分别加上 `nohup` 和 `&` 后回车，可以看到 `nohup` 输出了一行信息，再 Enter 键就跳回了 Shell 命令行，此时命令已经在后台执行了，`nohup` 将命令的输出重定向至当前目录的 `nohup.out` 文件中。同时，`nohup` 会将对应程序的 PID 输出，PID 可用于需要中断进程时结束进程。

### 程序监控

执行如下命令，可以持续的查看 `nohup.out` 的输出，达到监控程序的效果。

```bash
tail -f nohup.out
```

### 查看 PID

使用 ps 命令查找该进程的进程 ID（PID）。例如，执行以下命令可以查看所有运行中的 Python 进程：

```bash
ps -ef | grep python
```

### 结束进程

结束进程使用 kill 命令，其语法格式如下：

```bash
kill [signal] PID
```

其中，signal 表示要发送的信号类型，PID 表示要结束的进程 ID。

常用的信号类型包括：

-   15(SIGTERM)：默认信号，表示终止进程。
-   9(SIGKILL)：强制终止进程，不能被进程忽略或捕获。

我们通常使用以下命令即可：

```bash
kill -9 PID
```

### 缺点

使用 `nohup` 命令的一个缺点就是不方便进行程序监控，只能通过写入到文件中的方式来”曲线救国“，但是这种方式可能导致写入的文件非常大，占据大量磁盘资源。

## 事后使用 `disown` 命令

### 将正在运行中的任务放入后台

首先，如果任务已经在前台运行，可以通过按下 ++ctrl+z++ 来暂停它。然后，输入 `bg` 命令将暂停的任务放入后台继续运行。

```bash
Ctrl + Z
bg
```

### 将任务从当前会话中移除

为了确保任务在 SSH 会话关闭后仍然运行，可以使用 `disown` 命令。首先，通过 `jobs` 命令查看后台任务的编号，然后使用 `disown` 命令将其从当前会话中移除。

```bash
jobs
disown -h %jobid # 例如 disown -h %1
```

其中 `jobid` 是通过 `jobs` 命令查看到的后台任务编号。这样，即使 SSH 会话关闭，任务也会继续在后台运行。
