---
title: 手动计算简单的反向传播算法
authors: 
  - <PERSON>
date: '2023-04-10'
slug: backward-propagation-by-hand
categories:
  - 深度学习
  - PyTorch
tags:
  - 深度学习
  - PyTorch
---

# 手动计算简单的反向传播算法

反向传播算法是深度学习进行参数优化的基础。本文手动计算了多层感知机中损失函数对权重、净输入值的梯度，并与 PyTorch 的计算结果进行了验证。

反向传播算法的本质是矩阵微分和链式法则，这两个知识都不难理解，但刚接触反向传播算法时总容易被一些陌生的符号弄糊涂。理解反向传播算法的理论推导，最重要的是弄清楚各个向量、矩阵的维度，以及熟练它们之间的前向传播关系。最后多加练习，就能对反向传播算法的理解更加透彻。

## 理论推导

![image-20230411003621789](index-image/image-20230411003621789.png)

<!-- more -->

![image-20230411003633710](index-image/image-20230411003633710.png)

![image-20230411003646349](index-image/image-20230411003646349.png)

![image-20230411003701755](index-image/image-20230411003701755.png)

![image-20230411003739646](index-image/image-20230411003739646.png)

![image-20230411003800880](index-image/image-20230411003800880.png)

![image-20230411003811035](index-image/image-20230411003811035.png)

## MSE 损失，无激活函数

!!! tip "不包含偏置项"

	为方便计算，以下均假设偏置项为 $0$。

### 手动计算

假设真实的 $y$ 值为 $4$。

![image-20230411002719475](index-image/image-20230411002719475.png)

![image-20230411002732317](index-image/image-20230411002732317.png)

### PyTorch 计算

![image-20230411002908918](index-image/image-20230411002908918.png)

## MSE 损失，隐藏层使用 Logistic 激活函数

### 手动计算

假设真实的 $y$ 值为 $4$。

![image-20230411003140007](index-image/image-20230411003140007.png)

![image-20230411003159218](index-image/image-20230411003159218.png)

![image-20230411003332901](index-image/image-20230411003332901.png)

### PyTorch 计算

![image-20230411003238363](index-image/image-20230411003238363.png)

## Logit 损失，隐藏层使用 Logistic 激活函数

### 手动计算

假设真实的 $y$ 值为 $1$。

![image-20230411003359061](index-image/image-20230411003359061.png)

![image-20230411003409811](index-image/image-20230411003409811.png)

### PyTorch 计算

![image-20230411003447921](index-image/image-20230411003447921.png)
