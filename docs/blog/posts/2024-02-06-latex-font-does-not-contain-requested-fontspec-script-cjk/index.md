---
title: LaTeX 抑制缺失中文字体的警告
authors: 
  - <PERSON>
date: '2024-02-06'
slug: latex-font-does-not-contain-requested-fontspec-script-cjk
categories:
  - LaTeX
tags:
  - LaTeX
links:
  - "[LaTeX 中文使用] 抑制 xeCJK/fontspec 警告 no-script CJK": https://zhuanlan.zhihu.com/p/145429470
---

# $\LaTeX$ 抑制缺失中文字体的警告

本文记录了如何抑制由于缺失中文字体导致的宏包警告。

![image-20240206094050536](index-image/image-20240206094050536.png)


<!-- more -->

```
Font "STHeitiSC-Medium" does not contain requested
(fontspec)	Script "CJK".
```

这个警告并不会影响编译结果，但波浪线的警告标识让人看着有点儿不舒服。

解决方案，在 `\documentclass` 这一行语句的前面添加：

```tex
\PassOptionsToPackage{quiet}{xeCJK}
```

就可以抑制 `xeCJK` 宏包发出的警告。

参考：[[LaTeX 中文使用] 抑制 xeCJK/fontspec 警告 no-script CJK](https://zhuanlan.zhihu.com/p/145429470) 的评论区。
