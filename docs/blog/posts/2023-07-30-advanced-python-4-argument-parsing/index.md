---
title: Python 进阶教程系列 4：命令行参数
authors: 
  - <PERSON>
date: '2023-07-30'
slug: advanced-python-4-argument-parsing
categories:
  - Python
tags:
  - Python
links:
  - "Argument Parsing - Advanced Python Tutorial #4": https://youtu.be/alSgAbyC7K8
  - "argparse 官方文档": https://docs.python.org/zh-cn/3/library/argparse.html
---

# Python 进阶教程系列 4：命令行参数

本文是 Python 进阶教程系列 4，主要介绍了 `sys` 和 `argparse` 解析命令行参数。

命令行参数是指在运行程序时传递给程序的参数。例如，我们可以通过命令行参数来指定要处理的文件名、设置程序的配置选项等。

![image-20230727235705211](index-image/image-20230727235705211.png)

<!-- more -->

## 使用 `sys` 解析命令行参数

在实际的应用中，我们经常需要使用命令行参数来根据用户的输入执行不同的逻辑。通过 Python 内置的 `sys` 模块来解析和处理命令行参数，使我们能够编写更加灵活和交互的程序。

在 Python 中，命令行参数可以通过`sys`模块的`argv`属性来获取。`argv`是一个包含了命令行参数的列表。列表中的 ==第一个元素通常是脚本的名称== ，随后的元素则是传递给程序的参数，按照它们在命令行中出现的顺序。

下面是一个简单的示例，演示了如何使用`sys`模块解析命令行参数：

```python
import sys

# 获取命令行参数
args = sys.argv

# 输出脚本名称
print("脚本名称：", args[0])

# 输出其余命令行参数
print("其余参数：", args[1:])
```

假设我们将上述代码保存为 `test.py`，然后在命令行中执行以下命令：

```
python test.py hello world
```

我们将看到输出结果为：

```
脚本名称: test.py
其余参数: ['hello', 'world']
```

在上面的例子中，我们首先导入了 `sys` 模块。然后，通过访问 `sys.argv` 属性，我们获取了命令行参数。由于 `sys.argv` 是一个列表，所以我们可以使用索引访问其中的元素。我们打印了列表中的第一个元素，即脚本的名称，以及其余的元素，即传递给程序的参数。

!!! note "`sys.argv` 中的每个命令行参数都以字符串的形式存储"

	需要注意的是，`sys.argv` 中的每个命令行参数都以字符串的形式存储，即使我们在命令行中输入的是数字或其他类型的参数。如果我们需要将这些参数转换为其他类型，可以使用相应的方法，例如 `int()` 将字符串转换为整数，`float()` 将字符串转换为浮点数。

此外，我们还可以使用 `len(sys.argv)` 来获取命令行参数的数量。

## 使用 `argparse` 解析命令行参数

Python 中有一个名为 `argparse` 的标准库，它提供了一种简单而灵活的方式来解析命令行参数。[这篇帖子](../dijkstra-algorithm/#接收命令行参数运行主程序) 就使用了 `argparse` 库来接收命令行参数。

`argparse` 库提供了一个 `ArgumentParser` 类来处理命令行参数。我们可以创建一个 `ArgumentParser` 的实例，然后使用它的 `add_argument()` 方法来定义我们需要的参数。这个方法有很多参数可以设置，帮助我们对命令行参数进行更详细的配置。

在下面的示例中，我们创建了一个简单的命令行工具，用于计算两个数字的和。我们通过定义两个参数来实现这个功能，一个是要相加的第一个数字，另一个是要相加的第二个数字。

```python
import argparse


def sum_numbers(args):
    result = args.a + args.b
    if args.o:
        result *= -1
        print(f"The opposite value of sum of {args.a} and {args.b} is {result}")
    else:
        print(f"The sum of {args.a} and {args.b} is {result}")


def main():
    parser = argparse.ArgumentParser(
        description="A simple command line tool to calculate the sum of two numbers"
    )
    parser.add_argument("a", type=int, help="The first number to be added")
    parser.add_argument("b", type=int, help="The second number to be added")
    parser.add_argument(
        "-o",
        action="store_true",  # (1)!
        default=False,
        help="Whether to take the opposite value or not",
    )
    args = parser.parse_args()
    sum_numbers(args)


if __name__ == "__main__":
    main()
```

1.  `action="store_true"` 的作用是：如果命令行中输入了 `-o` ，则 `args.o` 参数会自动由默认值 `False` 变为 `True`。参考：[argparse 官方文档 - action](https://docs.python.org/zh-cn/3/library/argparse.html#action)。

在上面的代码中，我们首先导入了 `argparse` 模块。然后，我们定义了一个名为 `sum_numbers()` 的函数，它接受一个 `args` 参数，其中包含了通过命令行传入的参数。在函数中，我们将两个数字相加，并将结果打印出来。

接下来，我们定义了一个名为 `main()` 的函数，它是我们程序的入口点。在 `main()` 函数中，我们创建了一个 `ArgumentParser` 的实例，并指定了一个描述性的字符串。

然后，我们使用 `add_argument()` 方法来定义两个参数：`a` 和 `b`。这两个参数都是位置参数，也就是必须提供，不能省略。两个参数的类型为整数，并提供了帮助文本。

我们还提供了一个可选参数 `-o`，它用于对结果取相反数。它的默认值是 `False`。得益于 `action="store_true"`，如果命令行中输入了 `-o` ，则 `args.o` 参数会自动由默认值 `False` 变为 `True`。参考：[argparse 官方文档 - action](https://docs.python.org/zh-cn/3/library/argparse.html#action)。

最后，我们调用了 `parse_args()` 方法来解析命令行参数，并将结果传递给 `sum_numbers()` 函数进行计算。

我们可以通过在命令行中运行脚本并提供两个数字的值来测试这个命令行工具。例如，我们可以运行以下命令来计算 5 和 3 的和：

```bash
python test.py 5 3
python test.py 5 3 -o
```

运行结果将打印出来：

![image-20230727235705211](index-image/image-20230727235705211.png)

通过使用 `argparse` 来解析命令行参数，我们可以轻松地为我们的 Python 程序添加命令行接口，使其更加灵活和易用。

!!! note "Python 进阶教程系列文章"

	- [x] [Python 进阶教程系列 1：双下划线的魔法方法](../advanced-python-1-magic-method/)
	
	- [x] [Python 进阶教程系列 2：装饰器](../advanced-python-2-decorator/)
	
	- [x] [Python 进阶教程系列 3：生成器](../advanced-python-3-generator/)
	
	- [x] [Python 进阶教程系列 4：命令行参数](../advanced-python-4-argument-parsing/)
	
	- [x] [Python 进阶教程系列 5：获取和修改被保护的属性](../advanced-python-5-encapsulation/)
	
	- [x] [Python 进阶教程系列 6：使用 `mypy` 进行类型提示](../advanced-python-6-type-hinting/)
	
	- [x] [Python 进阶教程系列 7：工厂模式](../advanced-python-7-factory-design-pattern/)
	
	- [x] [Python 进阶教程系列 8：代理模式](../advanced-python-8-proxy-design-pattern/)
	
	- [x] [Python 进阶教程系列 9：单例模式](../advanced-python-9-singleton-design-pattern/)
	
	- [x] [Python 进阶教程系列 10：组合模式](../advanced-python-10-composite-design-pattern/)

