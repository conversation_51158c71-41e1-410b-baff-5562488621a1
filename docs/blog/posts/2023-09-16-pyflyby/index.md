---
title: 使用 pyflyby 自动管理导入包
authors: 
  - <PERSON>
date: '2023-09-16'
slug: pyflyby
categories:
  - Python
tags:
  - Python
links:
  - "pyflyby GitHub Repo": https://github.com/deshaw/pyflyby
  - "Pyflyby: Improving Efficiency of Jupyter Interactive Sessions": https://labs.quansight.org/blog/2021/07/pyflyby-improving-efficiency-of-jupyter-interactive-sessions
  - "jupyterlab-pyflyby GitHub Repo": https://github.com/deshaw/jupyterlab-pyflyby
---

# 使用 `pyflyby` 自动管理导入包

在编写 `Python` 代码时，尤其是在构建复杂的项目时，你是否遇到过这些问题：

- 忘记 `import` 某个包了；

- `import` 了很多包，但不知道哪些是可以删掉的？

在 `Python` 开发过程中，我们经常需要导入一些第三方包或自定义的模块。但是，手动导入这些包和模块有时候会变得非常繁琐和冗长。由 [D<PERSON> E<PERSON> Shaw group](https://www.deshaw.com/) 贡献的开源工具 [`pyflyby`](https://github.com/deshaw/pyflyby) 可以自动为我们管理这些导入，帮助我们轻松解决这些问题！

![Kapture 2023-09-16 at 13.23.00](index-image/Kapture 2023-09-16 at 13.23.00.gif){width=600px}

<!-- more -->

## 安装 `pyflyby`

```bash
pip install pyflyby
```

如果你还没有安装过 `ipython`，那么也需要安装它：

```bash
pip install ipython
```

## 在 `IPython/Jupyter Notebook` 中使用

如果只是对单个文件使用，可以在一个单元格中运行：

```
%load_ext pyflyby
```

如果想要对今后所有文件使用，可以在终端中输入下面的命令进行配置：

```bash
py pyflyby.install_in_ipython_config_file
```

## `Autoimporter + IPython`

下面的代码依赖于 `numpy`、`matplotlib` 和 `scipy`，我们在没有导入任何依赖的情况下运行它：

```python
plot(scipy.stats.norm.pdf(linspace(-5, 5), 0, 1))
```

![image-20230916131037653](index-image/image-20230916131037653.png)

`pyflyby` 自动为我们检查出了需要导入的包，并帮我们导入，然后成功运行了代码。

我们可以将红框中的代码复制到单元格中（记得把 `[PYFLYBY] ` 删去），就可以补充导入包的依赖了。

如果你使用 [JupyterLab](https://github.com/jupyterlab/jupyterlab)，还可以配合 [jupyterlab-pyflyby](https://github.com/deshaw/jupyterlab-pyflyby) 自动在第一个单元格插入应该补全的代码。由于我不使用 JupyterLab，因此我没有进行测试。下面的动图展示了使用效果：

![Fresh Jupyter session with two cells: the first imports Matplotlib, and the second plots using both Matplotlib and NumPy. Upon execution, jupyterlab-pyflyby automatically adds the missing NumPy import in the first cell, and then successfully renders the plot.](index-image/jlpfb.gif)

## `tidy-imports`

对于 `.py` 脚本，可以使用命令行：

```bash
tidy-imports test.py
```

![Kapture 2023-09-16 at 13.23.00](index-image/Kapture 2023-09-16 at 13.23.00.gif)

`pyflyby` 会在命令行中显示应该补充的导入语句，并且可以通过输入 `y` 或 `N` 来选择是否对源代码进行修改。如果选择 `y`，则 `pyflyby` 会自动为我们在源代码中补充导入语句。

!!! warning "`pyflyby` 并不能完全准确识别应该导入的包"

	某些导入的包并不常见，可能并不能被正确识别，因此需要再手动导入。这种情况应该是出现在一些小众、不常用的包上。对于常用的 `pd`、`np` 等数据分析包，应该都是支持的。毕竟 D. E. Shaw 也是顶尖量化对冲基金，想必也经常使用数据分析和科学计算工具。
	
	![image-20230916232302892](index-image/image-20230916232302892.png)

