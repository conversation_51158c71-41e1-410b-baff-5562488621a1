---
title: 常用的 Git 代码
authors: 
  - <PERSON>
date: '2023-07-25'
slug: git
categories:
  - Computer Science
tags:
  - Computer Science
links:
  - "Git 常用命令备忘": https://foofish.net/git-command.html
  - "阮一峰的网络日志：Commit message 和 Change log 编写指南": https://www.ruanyifeng.com/blog/2016/01/commit_message_change_log.html
  - "CSDN：Git 常用指令图解": https://blog.csdn.net/doubleintfloat/article/details/125508412
  - "在沙盒中学习 Git 分支": https://learngitbranching.js.org/?locale=zh_CN
---

# 常用的 Git 代码

本文记录了常用的 Git 命令，并转载了一些优质博文（见相关链接）作为补充。

## 三个基本概念

1. 工作区 (Workspace) 是计算机中项目的根目录
2. 暂存区 (Index) 像个缓存区域，临时保存你的改动
3. 版本库 (Repository) 分为本地仓库（Local) 和远程仓库 (Remote)

![simple](index-image/bg2015120901.png)


<!-- more -->

## 配置 Git

### 查看当前仓库的 gitconfig

```bash
git config --local --list

# 默认是 --local，因此可以省去，变成：
git config --list
```

或者

```bash
cat .git/config
```

### 查看用户级别的 gitconfig

```bash
git config --global --list
```

或者

```bash
cat ~/.gitconfig
```

### 配置用户名和邮箱

```bash
git config --global user.name "jeremy-feng"
git config --global user.email "<EMAIL>"
```

若不需要配置全局的用户名和邮箱，只需要配置当前仓库的用户名和邮箱，可以将 `--global` 删除。

### 删除 Git 配置项

要删除 Git 配置中的某个配置项，可以使用以下命令：

```
git config --unset <key>
```

其中 `<key>` 是要删除的配置项的键名。

如果你要删除全局配置中的某个配置项，可以使用 `--global` 参数：

```
git config --global --unset <key>
```

这将从全局配置文件中删除指定的配置项。

如果你要删除本地仓库的配置项，可以在仓库目录下执行命令，不需要使用 `--global` 参数。

如果你要删除所有的 Git 配置，可以删除配置文件。配置文件的位置取决于你的操作系统和安装方式。在大多数情况下，全局配置文件位于用户目录下的 `.gitconfig` 文件。你可以使用文件管理器或命令行删除该文件。

如果你在删除 Git 配置项时遇到警告 "warning: <key> has multiple values"，这意味着该配置项具有多个值。在 Git 中，配置项可以具有多个值，这种情况通常发生在你多次为同一个配置项设置了不同的值。

要删除具有多个值的配置项，可以使用 `--unset-all` 参数。这将删除所有与指定键名相关联的配置值。

```
git config --unset-all <key>
```

如果你要删除全局配置中的具有多个值的配置项，可以使用 `--global` 参数：

```
git config --global --unset-all <key>
```

请注意，使用 `--unset-all` 参数将删除所有与指定键名相关联的配置值，而不仅仅是删除其中一个值。因此，在执行此操作之前，请确保你了解删除配置项的后果，并且确定你想要删除所有值。

### 免密码提交

在终端输入：

```bash
git config --local credential.helper store
```

再次输入一次密码，即可保存密码，今后提交本仓库时都不需要密码了。

若要设置为用户范围内全局免密码，需要添加 `--global`：

```bash
git config --global credential.helper store
```

!!! warning "密码将以明文形式存放在磁盘！"

	- `store` 模式会将凭证用明文的形式存放在磁盘中，并且永不过期。 这意味着除非你修改了你在 Git 服务器上的密码，否则你永远不需要再次输入你的凭证信息。 这种方式的缺点是你的密码是用明文的方式存放在你的 home 目录下。
	
	- `cache` 模式会将凭证存放在内存中一段时间。 密码永远不会被存储在磁盘中，并且在15分钟后从内存中清除。可以设置 `--timeout <seconds>` 参数，可以设置后台进程的存活时间（默认是 “900”，也就是 15 分钟）。
	
	参考：[Git 工具 - 凭证存储](https://git-scm.com/book/zh/v2/Git-%E5%B7%A5%E5%85%B7-%E5%87%AD%E8%AF%81%E5%AD%98%E5%82%A8)

## 新建、克隆、推送、合并、删除

### 在已有文件夹下新建 Git 仓库

```bash
cd existing_folder
git init
git remote <NAME_EMAIL>:jeremy-feng/jeremy-feng.github.io.git
git add .
git commit -m "Initial commit"
git push -u origin master
```

### 克隆仓库

```bash
<NAME_EMAIL>:jeremy-feng/jeremy-feng.github.io.git
cd 项目名
touch README.md
git add README.md
git commit -m "add README"
git push -u origin master
```

### 添加和提交

```shell
git add -A && git commit -m 'update'
```

### 回滚某个文件

参考：[https://www.cnblogs.com/acm-bingzi/p/gitCheckout.html](https://www.cnblogs.com/acm-bingzi/p/gitCheckout.html)

先找到需要会滚到的 commit id：

```shell
git log <path/to/file>
```

再用 `git checkout` 回滚到该 commit id

```shell
git checkout <commit id> <path/to/file>
```

### `git rebase`

如果远程仓库修改了 A 文件，但本地仓库没有修改 A 文件，而是修改了 B 文件，此时直接从本地 push 到远程时会报错：

```
fatal: Need to specify how to reconcile divergent branches.
```

可以用 `git rebase` 先将本地仓库建立在远程的基础上。

```bash
git rebase <远程的 commid id，前 7 位即可，例如 42f44ae>
```

再 `git push` 即可。

### `git restore`

`git restore` 是 Git 版本控制系统中的一个命令，用于还原文件或目录的状态。它可以用于撤消对工作目录中文件的更改，或者将文件从暂存区还原到工作目录。

`git restore` 命令的使用方式如下：

```
git restore <file>
```

这个命令将会将指定的文件还原到最近的提交状态，也就是撤销对文件的修改。

你还可以使用 `--staged` 选项来将文件从暂存区还原到工作目录：

```
git restore --staged <file>
```

这个命令会将文件还原到最近的提交状态，并将其从暂存区移除。

除了还原文件，`git restore` 还可以用于恢复删除的文件。你可以使用 `--source` 选项指定要恢复的文件的来源：

```
git restore --source=<commit> <file>
```

这个命令会将指定提交中的文件还原到当前工作目录。

总的来说，`git restore` 命令是一个非常有用的工具，可以帮助你管理文件的状态，撤销修改以及恢复删除的文件。

### 删除`.git`仓库

```shell
rm -rf .git
```

- `-r`代表删除文件夹；
- `-rf`代表强制删除，即删除当前目录下的所有文件及目录，并且是直接删除，无需逐一确认。

## Commit message 的格式

每次提交，Commit message 都包括三个部分：Header，Body 和 Footer。

```bash
<type>(<scope>): <subject>
// 空一行
<body>
// 空一行
<footer>
```

其中，Header 是必需的，Body 和 Footer 可以省略。

不管是哪一个部分，任何一行都不得超过 72 个字符（或 100 个字符）。这是为了避免自动换行影响美观。

### Header

Header 部分只有一行，包括三个字段：`type`（必需）、`scope`（可选）和`subject`（必需）。

**（1）type**

`type`用于说明 commit 的类别，只允许使用下面 7 个标识。

- feat：新功能（feature）
- fix：修补 bug
- docs：文档（documentation）
- style：格式（不影响代码运行的变动）
- refactor：重构（即不是新增功能，也不是修改 bug 的代码变动）
- test：增加测试
- chore：构建过程或辅助工具的变动

如果`type`为`feat`和`fix`，则该 commit 将肯定出现在 Change log 之中。其他情况（`docs`、`chore`、`style`、`refactor`、`test`）由你决定，要不要放入 Change log，建议是不要。

**（2）scope**

`scope`用于说明 commit 影响的范围，比如数据层、控制层、视图层等等，视项目不同而不同。

**（3）subject**

`subject`是 commit 目的的简短描述，不超过 50 个字符。

- 以动词开头，使用第一人称现在时，比如`change`，而不是`changed`或`changes`
- 第一个字母小写
- 结尾不加句号（`.`）

### Body

Body 部分是对本次 commit 的详细描述，可以分成多行。下面是一个范例。

```bash
More detailed explanatory text, if necessary.  Wrap it to 
about 72 characters or so. 

Further paragraphs come after blank lines.

- Bullet points are okay, too
- Use a hanging indent
```

有两个注意点。

（1）使用第一人称现在时，比如使用`change`而不是`changed`或`changes`。

（2）应该说明代码变动的动机，以及与以前行为的对比。

### Footer

Footer 部分只用于两种情况。

**（1）不兼容变动**

如果当前代码与上一个版本不兼容，则 Footer 部分以`BREAKING CHANGE`开头，后面是对变动的描述、以及变动理由和迁移方法。

```bash
BREAKING CHANGE: isolate scope bindings definition has changed.

    To migrate the code follow the example below:

    Before:

    scope: {
      myAttr: 'attribute',
    }

    After:

    scope: {
      myAttr: '@',
    }

    The removed `inject` wasn't generaly useful for directives so there should be no code using it.
```

**（2）关闭 Issue**

如果当前 commit 针对某个 issue，那么可以在 Footer 部分关闭这个 issue。

```bash
Closes #234
```

也可以一次关闭多个 issue。

```bash
Closes #123, #245, #992
```

### Revert

还有一种特殊情况，如果当前 commit 用于撤销以前的 commit，则必须以`revert:`开头，后面跟着被撤销 Commit 的 Header。

```bash
revert: feat(pencil): add 'graphiteWidth' option

This reverts commit 667ecc1654a317a13331b17617d973392f415f02.
```

Body 部分的格式是固定的，必须写成`This reverts commit <hash>.`，其中的`hash`是被撤销 commit 的 SHA 标识符。

如果当前 commit 与被撤销的 commit，在同一个发布（release）里面，那么它们都不会出现在 Change log 里面。如果两者在不同的发布，那么当前 commit，会出现在 Change log 的`Reverts`小标题下面。

## commitlint.io 在线编写规范的 Commit message

[Commitlint.io](https://commitlint.io/) helps your project to ensure nice and tidy commit messages without needing any downloads or installations. It's designed with your workflow in mind by using zero change to your system.

## Commitizen

[Commitizen](https://github.com/commitizen/cz-cli)是一个撰写合格 Commit message 的工具。

安装命令如下。

```bash
$ npm install -g commitizen
```

然后，在项目目录里，运行下面的命令，使其支持 Angular 的 Commit message 格式。

```bash
$ commitizen init cz-conventional-changelog --save --save-exact
```

以后，凡是用到`git commit`命令，一律改为使用`git cz`。这时，就会出现选项，用来生成符合格式的 Commit message。

![img](index-image/bg2016010605.png)

## `.gitignore` 

若要忽略某些文件，不让`git`提交这些文件，可以在`.gitignore`文件设定规则。

### 新建 `.gitignore` 文件

```shell
touch .gitignore
```

### 添加规则

- 禁止上传文件夹
- 禁止上传某个后缀的文件

```txt
#idea
/.idea
#__pycache__
/__pycache__
#忽略 gitignore 自身
.gitignore
```

### `.gitignore` 文件不生效时的解决方案

在终端输入

```bash
git rm -r --cached .
```

把本地缓存删除，改变成未 track 状态。

如果只想把某些文件删掉，例如删除 `.xml` 文件，可以用

```bash
git rm -r --cached *.xml
```

#### 示例

目录结构：

![image-20221204195912121](index-image/image-20221204195912121.png)

`.gitignore`文件中的内容：

```
# 不上传 xml 文件
*.xml
# 不上传。gitignore 文件
.gitignore
```

### 忽略`.gitignore`文件本身的另一种方法

在`.git/info/exclude`添加`.gitignore`：

![image-20230118091542891](index-image/image-20230118091542891.png)

### 不忽略文件和文件夹

方法：在路径前加上 `!`。

```
*
!path/to/uploaded_file.txt
```

在上面的示例中，`*` 表示忽略所有文件和文件夹。`!path/to/uploaded_file.txt` 表示不忽略 `path/to/uploaded_file.txt` 文件。

## 添加 `submodule`

要将一个仓库中的一个目录映射到另一个仓库，你可以使用 Git 的 submodule 功能。下面是添加 submodule 的基本步骤：

1. 打开你想要添加 submodule 的仓库的本地副本。

2. 使用以下命令添加 submodule：
   ```bash
   git submodule add <URL of the repository you want to add> <path to the directory in your repository>
   ```

   例如：
   ```bash
   git submodule add https://github.com/username/another-repository.git path/to/directory
   ```

3. 添加并提交 submodule 更改：
   ```bash
   git add .
   git commit -m "Add submodule: <submodule name>"
   ```

4. 如果你将这些更改推送到远程仓库，确保在推送之前初始化并更新子模块：
   ```bash
   git submodule update --init --recursive
   ```

现在，你的主仓库中的指定目录应该映射到另一个仓库。

请注意，当其他人克隆你的仓库时，他们需要运行以下命令来初始化并拉取子模块中的内容：
```bash
git submodule update --init --recursive
```

这样他们才能获取到子模块的内容。

## 连接 GitHub 的问题

### 测试和 GitHub 的网络通信是否正常

`ssh -T -p 443 ******************`来测试和 GitHub 的网络通信是否正常，如果提示`Hi xxxxx! You've successfully authenticated, but GitHub does notprovide shell access.` 就表示一切正常了。

### 报错 The TLS connection was non-properly terminated.

```
git push origin master:master
fatal: unable to access 'https://github.com/XXX.git/': gnutls_handshake() failed: The TLS connection was non-properly terminated.
```

两种解决方法：

1. ```bash
	git config --global http.sslVerify false
	```

	这种方法曾经成功过，但后来再遇到时又报错了。

2. ```bash
	git push -u origin master --force
	```

	第一种方法失效时，第二种方法可以解决报错。

![image-20221221103707463](index-image/image-20221221103707463.png)

### 报错 fatal: unable to access xxx: Failed to connect to github.com port 443 after 21072 ms: Timed out

解决方法：

删除原有的 SSH Key，再重新生成一份 SSH Key，上传新的 SSH Key 到 GitHub。

### 为`Git`配置代理

```shell
git config --global http.sslverify false
git config --global http.https://github.com.proxy socks://127.0.0.1:10808
git config --global https.https://github.com.proxy socks://127.0.0.1:10808
```

![image-20230131200320933](index-image/image-20230131200320933.png)

```shell
# 删除重置上述参数
git config --global --unset http.sslverify
git config --global --unset http.proxy
git config --global --unset https.proxy
```

