---
title: 使用 pdb 调试代码
authors: 
  - <PERSON>
date: '2023-02-16'
slug: python-debug
categories:
  - Python
tags:
  - Python
links:
  - "pdb cheatsheet": https://github.com/nblock/pdb-cheatsheet
  - "Python 中调试 pdb 库用法详解": https://blog.csdn.net/weixin_44799217/article/details/125884722
  - "Python 之 pdb 调试": https://blog.csdn.net/weixin_42384444/article/details/119053011
---

# 使用 `pdb` 调试代码

`pdb` 是 Python 内置的一个调试库，为 Python 程序提供了一种交互的源代码调试功能，主要特性包括设置断点、单步调试、进入函数调试、查看当前代码、查看栈片段、动态改变变量的值等。

## 常用的 `pdb` 命令

![image-20230208010600684](index-image/image-20230208010600684.png)

<!-- more -->

![pdb](index-image/pdb.png)

## 注意事项

有的 Python 代码和 `pdb` 内置的命令有冲突，例如

```python
list()
```

如果想要在调试时使用这些有冲突的代码，可以在代码前加上`!`。例如

```
!list()
```

参考 [https://stackoverflow.com/a/23050282/16760102](https://stackoverflow.com/a/23050282/16760102)
