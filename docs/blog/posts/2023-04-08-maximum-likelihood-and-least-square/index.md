---
title: 极大似然估计与最小均方误差的等价性
authors:
  - <PERSON>
date: "2023-04-08"
slug: maximum-likelihood-and-least-square
categories:
  - 统计
tags:
  - 统计
---

# 极大似然估计与最小均方误差的等价性

在使用最小二乘法估计线性模型的参数时，我们通常会将目标函数写成最小化均方误差的形式：
$$
\hat\beta = \min_{\beta} \sum_{i=1}^n
{\color{red}{(y_i - x_i^T \beta)^2}}
$$

为什么我们要用 ==均方误差== 作为损失函数？而不是绝对值误差、绝对值的三次方误差等其他形式？本文推导了极大似然估计与最小均方误差的等价性，说明最小均方误差是一种合理的做法。

<!-- more -->

我们知道，估计误差与参数 $\beta$ 的关系为：

$$
e_i = y_i - x_i^T \beta
$$

==假设估计误差服从均值为 $0$ 且方差为 $\sigma ^2$ 的正态分布== ，那么在参数 $\beta$ 已知的情况下，单个样本的概率密度函数为：

$$
\begin{align*}
f(y_i| x_i,\beta)&=
f(e_i| x_i,\beta)\\
&= \frac{1}{\sqrt{2\pi\sigma^2}}\exp\left(-\frac{e_i^2}{2\sigma^2}\right) \\
&= \frac{1}{\sqrt{2\pi\sigma^2}}\exp\left[-\frac{ {\color{blue}{(y_i - x_i^T \beta)^2}}}{2\sigma^2}\right]
\end{align*}
$$

所有样本的似然函数为：

$$
L(\beta) = \prod_{i=1}^n f(y_i| x_i,\beta) = \prod_{i=1}^n \frac{1}{\sqrt{2\pi\sigma^2}}\exp\left[-\frac{ \left(y_i - x_i^T \beta \right) ^2}{2\sigma^2}\right]
$$

所有样本的对数似然函数为：

$$
\ell(\beta) = \log L(\beta) = -\frac{n}{2}\log(2\pi) -\frac{n}{2}\log(\sigma^2) - \frac{1}{2\sigma^2} \sum_{i=1}^n \left(y_i - x_i^T \beta \right)^2
$$

最大化似然函数，就等价于最小化对数似然函数的相反数：

$$
Q(\beta) = -\ell(\beta) = \frac{n}{2}\log(2\pi) + \frac{n}{2}\log(\sigma^2) + \frac{1}{2\sigma^2} \sum_{i=1}^n {\color{blue}{(y_i - x_i^T \beta)^2}}
$$

上式中只有 $\beta$ 是可变量，因此本质上就是最小化上式的最后一个求和项，即最小化均方误差：

$$
\hat\beta = \min_{\beta} \sum_{i=1}^n {\color{red}{(y_i - x_i^T \beta)^2}}
$$
