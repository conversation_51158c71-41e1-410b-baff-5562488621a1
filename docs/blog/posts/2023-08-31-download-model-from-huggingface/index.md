---
title: 从 Hugging Face 下载模型文件
authors: 
  - <PERSON>
date: '2023-08-31'
slug: download-model-from-huggingface
categories:
  - 深度学习
tags:
  - 深度学习
links:
  - "Download files from the Hub": https://huggingface.co/docs/huggingface_hub/main/en/guides/download
---

# 从 Hugging Face 下载模型文件

本文记录了如何用代码从 Hugging Face 下载模型文件。


<!-- more -->

## 复制模型名称

以 [Hello-SimpleAI/chatgpt-detector-roberta-chinese](https://huggingface.co/Hello-SimpleAI/chatgpt-detector-roberta-chinese) 为例，找到复制的图标，将模型名称复制下来。

![image-20230831175254624](index-image/image-20230831175254624.png)

## 使用 `huggingface_hub` 库下载模型文件

```python
from huggingface_hub import snapshot_download

snapshot_download(
    repo_id="Hello-SimpleAI/chatgpt-detector-roberta-chinese",
    local_dir="model",
)
```

`local_dir` 指定了下载后的模型应该存放的位置。我设定为`"model"`，它将在相对路径下新建一个`model`文件夹，将所有模型文件存放到`model`文件夹中。

![image-20230831175618961](index-image/image-20230831175618961.png)

### 设置是否使用快捷方式指向缓存中的大型文件

`huggingface_hub`首先会将下载到的文件存放在缓存目录`/Users/<USER>/.cache/huggingface/hub/models--Hello-SimpleAI--chatgpt-detector-roberta-chinese/blobs`中。

对于小型文件，会拷贝一份到之前指定的`model`文件夹。但对于大型文件，默认是用快捷方式的形式存放到`model`文件夹中。这样做的好处是，下次再下载模型文件时，若检测到缓存中已经存在文件，就不用再下载一遍了。

![image-20230831180146638](index-image/image-20230831180146638.png)

若不想以快捷方式的形式存储大型文件，而是直接以原文件的形式存储，可以设置 `local_dir_use_symlinks=False`。参考：[官方文档](https://huggingface.co/docs/huggingface_hub/main/en/guides/download#download-files-to-local-folder)。

## 下载数据集

设置 `repo_type="dataset"`：

```python
from huggingface_hub import snapshot_download

snapshot_download(
    repo_id="Hello-SimpleAI/HC3-Chinese",
    local_dir="data",
    repo_type="dataset",
    local_dir_use_symlinks=False,
)
```

