---
title: Black 格式化 Python 代码
authors:
  - <PERSON>
date: "2023-02-05"
slug: python-formatter-black
categories:
  - Python
tags:
  - Python
links:
  - "官方安装指南": https://black.readthedocs.io/en/stable/getting_started.html#installation
  - "Black: 一键美化 Python 代码": https://mp.weixin.qq.com/s/A0B6QzyzvFQZ4ISsxVUssQ
---

# Black 格式化 Python 代码

用 Black 自动格式化 Python 代码，编写规范、美观的 Python 代码，让阅读代码变成一种享受。

本文记录了在 VS Code 中安装 Black 时遇到的问题和解决方案。

![python-formatter-black](index-image/python-formatter-black.gif){width=200px}

<!-- more -->

## 安装方法

有很多关于 Black 的安装教程。大致分 3 步：

1. 安装 Black 包。

   ```shell
   pip install black
   ```

   如果需要格式化 Jupyter Notebooks，需要用

   ```shell
   pip install "black[jupyter]" # (1)!
   ```

   1. [官方安装指南](https://black.readthedocs.io/en/stable/getting_started.html#installation)

2. 在 VS Code 中安装 Black Formatter 扩展。链接：https://marketplace.visualstudio.com/items?itemName=ms-python.black-formatter

3. 进行必要的设置。例如：

- `format on save`

  ![image-20230205134313138](index-image/image-20230205134313138.png)

- `python formatting provider`

  ![image-20230205134335182](index-image/image-20230205134335182.png)

!!! tip "呼出 VS Code 设置的键盘快捷键"

	++ctrl+comma++

## 查看 Black 位置

### 源代码位置

```shell
pip show black
```

![image-20230206225923132](index-image/image-20230206225923132.png)

### 可执行文件位置

```shell
which black
```

![image-20230206230007574](index-image/image-20230206230007574.png)

## 出现的问题

如果报错：

![image-20230205134846787](index-image/image-20230205134846787.png)

在 Jupyter Notebook Cell 中需要点击 `使用 ... 格式化文档`，不要点击`格式化选定内容`。

也可以尝试：在 Jupyter Notebook 中，点击“设置单元格格式”（快捷键 ++shift+alt+f++）

![image-20230206225824284](index-image/image-20230206225824284.png)

对于 Python 脚本文件，可以正常格式化。

!!! tip

    如果还是没反应，记得选好 Python 解释器再尝试。没有设定 Python 解释器是不会自动格式化的。

## 命令行模式

还可以通过命令行一键格式化目录下的所有代码，包括 `.py` 和 `.ipynb`。并且可以提示哪些文件格式化失败。

```shell
black /home/<USER>
```

![image-20230206230107624](index-image/image-20230206230107624.png)
