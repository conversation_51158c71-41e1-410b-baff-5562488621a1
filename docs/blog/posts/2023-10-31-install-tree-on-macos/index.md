---
title: 在 MacOS 中安装 tree 命令
authors: 
  - <PERSON>
date: '2023-10-31'
slug: install-tree-on-macos
categories:
  - Computer Science
tags:
  - Computer Science
links:
  - "知乎-Failed to connect to raw.githubusercontent.com:443": https://zhuanlan.zhihu.com/p/115450863
---

# 在 MacOS 中安装 tree 命令

本文记录了在 MacOS 中安装 tree 命令的过程，主要涉及了在终端中临时使用代理的方法。

![image-20231031010538239](index-image/image-20231031010538239.png){width=500px}

<!-- more -->

在[这篇帖子](../generate-tree-chart-of-directory/)中，我们介绍了如何在 Linux 和 Wndows 上使用 `tree` 命令显示目录结构。但是，MacOS 上没有自带 `tree` 命令，因此需要安装。

## 安装命令

安转方法很简单，只需要用

```bash
brew install tree
```

但是，我在终端中运行这个命令时会报错：

```
Failed to connect to raw.githubusercontent.com:443
```

## 在终端中临时使用代理

参考 [知乎-Failed to connect to raw.githubusercontent.com:443](https://zhuanlan.zhihu.com/p/115450863)，在终端中运行（7890 需要换成你自己的端口）：

```bash
export HTTP_PROXY="http://127.0.0.1:7890"
export HTTPS_PROXY="http://127.0.0.1:7890"
export ALL_PROXY="socks5://127.0.0.1:7890"
```

![image-20231031003559606](index-image/image-20231031003559606.png)

即可在终端中临时使用代理。

再运行 `brew install tree` 即可成功安装 `tree` 命令。

![image-20231031002700809](index-image/image-20231031002700809.png)

![image-20231031010538239](index-image/image-20231031010538239.png)

