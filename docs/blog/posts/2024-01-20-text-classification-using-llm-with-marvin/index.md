---
title: 在 Python 中使用大语言模型进行文本分类
authors: 
  - <PERSON>
date: '2024-01-20'
slug: text-classification-using-llm-with-marvin
categories:
  - Python
tags:
  - Python
links:
  - "marvin 官方文档": https://www.askmarvin.ai/welcome/what_is_marvin/
  - "marvin GitHub 仓库": https://github.com/prefecthq/marvin
  - "instructor 官方文档": https://jxnl.github.io/instructor/
  - "instructor GitHub 仓库": https://github.com/jxnl/instructor/
  - "Pydantic is all you need - Jason Liu 中推荐 instructor 和 marvin": https://youtu.be/yj-wSRJwrrc?t=297
---

# 在 Python 中使用大语言模型进行文本分类

大语言模型可以做很多事情：给它一个提示词，它就能给出聪明的回复。但是，我们有时需要得到结构化的、具有严格类型要求的回答。例如我们需要判断一句话的情感得分，那么我们只需要得到一个数值，而不需要任何其他的元素。即使我们每次都在提示词中写上“请返回一个数值，例如 1.0。不要包含任何其他元素，只要一个数值，求你了”，模型仍然可能会返回各种奇怪的文本，这些文本在后续代码中极有可能出错。

`marvin` 是一个非常实用的 Python 包，它使用简单的代码和类型提示就能获取特定数据类型的返回。它的官网介绍说：

> This lets you focus on what you've always focused on: writing clean, versioned, reusable *code* and *data models*, and not scrutinizing whether you begged your LLM hard enough to output JSON or needed to offer it a bigger tip for the right answer.

本文借助 `marvin` 用 `gpt-3.5-turbo` 对文本进行二分类，判断一段文本是否由大语言模型生成，而不是人类生成。

![image-20240120232958730](index-image/image-20240120232958730.png){width=500px}


<!-- more -->

## 代码示例

首先导入必要的包，读取当前目录下的 `.env` 文件存放的 API Key，并且指定使用 `gpt-3.5-turbo` 模型。

 `.env` 文件的内容如下：

![image-20240120233520292](index-image/image-20240120233520292.png)

```python
import os

import marvin
from dotenv import load_dotenv

# Loading Environment Variables Using the `dotenv` Package
load_dotenv()
marvin.settings.openai.base_url = os.environ["OPENAI_API_BASE"]
marvin.settings.openai.api_key = os.environ["OPENAI_API_KEY"]
marvin.settings.openai.chat.completions.model = "gpt-3.5-turbo"
```

`marvin` 的使用方式可以参考[官方文档](https://www.askmarvin.ai/welcome/what_is_marvin/)，下面是一个二分类任务的代码，返回结果必须是布尔值 `True` 或者 `False`：

```python
text = "作为一个认知智能模型，我不具备价值判断能力，也无法对不同导演的文艺风格做出评论。"
category = marvin.classify(
    text,
    bool,
    instructions="Is this text generated by a large language model?",
)
```

## 分类结果

下面展示了两个分类结果，确实是分类正确的。

![image-20240120232655353](index-image/image-20240120232655353.png){width=500px}

![image-20240120232708598](index-image/image-20240120232708598.png)

我们使用 1000 条样本进行实验，并计算模型的评价指标：

![image-20240120232851331](index-image/image-20240120232851331.png){width=500px}

```python
import matplotlib.pyplot as plt
from sklearn.metrics import (
    ConfusionMatrixDisplay,
    accuracy_score,
    confusion_matrix,
    f1_score,
    precision_score,
    recall_score,
)

y_true = df_test_for_gpt["label"]
y_pred = df_test_for_gpt["pred"]

# 计算准确率、精确率、召回率、F1 值
acc = accuracy_score(y_true, y_pred)
prec = precision_score(y_true, y_pred)
rec = recall_score(y_true, y_pred)
f1 = f1_score(y_true, y_pred)

# 输出结果，格式化为百分比形式
print("准确率：{:.2%}".format(acc))
print("精确率：{:.2%}".format(prec))
print("召回率：{:.2%}".format(rec))
print("F1 值：{:.2%}".format(f1))
cm = confusion_matrix(y_true, y_pred, normalize="true")
labels = ["Human", "Machine"]
display = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=labels)
display.plot(
    include_values=True,
    cmap="Blues",
    ax=None,
    xticks_rotation="horizontal",
    values_format=".2%",
)
plt.show()
```

![image-20240120232958730](index-image/image-20240120232958730.png){width=500px}

可以看到，GPT 的分类结果中，容易把人类生成的文字识别为机器生成的，也就是假阳率（False positive rate）较高。

## 后记

`marvin` 让我看到了 GPT 应用的更多可能性：类型和结构规整的返回值，完全可以作为某些服务的 API 了。

类似的工具还有 `instructor`。它们目前都不支持 Python3.8 及以下的版本，如果由于 Python 版本问题而无法安装，可以新建一个虚拟环境来安装使用。
