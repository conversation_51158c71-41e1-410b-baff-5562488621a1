---
title: 为目录生成树形结构图
authors:
  - <PERSON>
date: "2022-08-26"
slug: generate-tree-chart-of-directory
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
toc: yes
description: 树形结构图可以帮助人们理解项目的文件结构。
---

# 为目录生成树形结构图

树形结构图可以帮助人们理解项目的文件结构。

![image-20230117104248511](index.zh-image/image-20230117104248511.png)

<!-- more -->

## Linux 系统

在 Linux 系统下的`bash`，直接使用`tree`即可。

## Windows 系统

Windows 系统使用的是`powershell`，如果只用`tree`的话，只会生成目录名，而不会生成文件名。

用`tree /F`可以生成文件名。

在 Windows 的 Git Bash 下，没有安装`tree`，会提示`tree: command not found`。

解决方法：

到 http://gnuwin32.sourceforge.net/packages/tree.htm 下载`tree.exe`文件。

![image-20221204200234793](index.zh-image/image-20221204200234793.png)

将解压后的`tree.exe`复制一份到`C:\Program Files\Git\usr\bin`中。这样就能够像 Linux 系统中一样运行`tree`的命令了。

## 常用命令

只需要查看 1 层文件夹，而不需要展开文件夹内部的子文件夹和文件，可以用：

```bash
tree -L 1
```

展示所有文件，包括`.git`这类隐藏文件：

```bash
tree -aL 1
```

其他命令可使用：

```bash
tree --help
```

查看帮助。

![image-20221204200625934](index.zh-image/image-20221204200625934.png)
