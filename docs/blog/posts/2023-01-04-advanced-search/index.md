---
title: 高级搜索技巧
authors: 
  - <PERSON> Feng
date: '2023-01-04'
slug: advanced-search
categories:
  - Computer Science
tags:
  - Computer Science
toc: yes
katex: yes
description: 搜索引擎和 GitHub 中一些高级搜索的方法。
---
# 高级搜索技巧

搜索引擎和 GitHub 中一些高级搜索的方法。

<!-- more -->

## 搜索引擎中的高级搜索方法

### 1.site:

site 是最常用的搜索指令，它是用来搜索某个域名下的所有文件 (注意：文件须是搜索引擎收录的文件)。

### 2.双引号

把搜索词放在双引号，代表**完全匹配搜索**。搜索结果返回的页面包含双引号中出现的所有词，连顺序也必须完全匹配。百度和谷歌都支持这个指令。

### 3.减号

减号 (-) 代表搜索不包含减号后面的词的页面。

**注意**：减号前面有空格而后面没有空格，紧跟着需要排除的词。百度和谷歌都支持这个指令。

### 4.星号

星号 (\*) 在计算机里的术语叫通配符，就是匹配全部的意思。百度不支持\*号搜索指令。比如在 Google 中搜索”郭\*纲”，其中\*号代表了任何文字。返回的结果不仅包含了郭德纲，还包含了其他。

### 5.inulr:

inurl:指令用于搜索查询词出现在 URL(链接) 中的页面。

### 6.inanchor:

这个指令返回的结果是导入链接锚文字中包含搜索词的页面，百度不支持该指令。这个指令可以帮助 SEOer 去研究竞争对手页面有哪些外链，可以找到很多行业外链资源平台。

### 7.intitle:

该指令返回的是页面 title 中包含关键词的页面。百度和 Google 都支持该指令。SEOer 都会把关键词放进 Title 中，因此使用 intitle 指令找到的文件才是更准确的竞争页面。而没有出现在 title 中的大部分是并没有针对关键词进行优化，也不是有力的竞争对手。

### 8.alltitle:

该标签返回的结果是页面标题中包含多组关键词的文件，如：alltitle:SEO 搜索引擎优化就相当于 intitle:SEO intitle:搜索引擎优化返回的是标题中既包含”SEO”也包含”搜索引擎优化”的页面。

### 9.allinurl:

与 alltitle 类似。allurl:SEO 搜索引擎优化就相当于 iknurl:SEO inurl:搜索引擎优化。

### 10.filetype:

该指令用于特定的文件格式。百度和 Google 都支持该指令。

### 11.link:

link 是以前 SEO 常用的指令，用来搜索某个 URL 的反向链接，既包括内部链接，也包括外部链接。但是现在 Google 对这个指令只返回其索引库中的一部分，而且是近乎随机的一部分，所以用这个指令查反链几乎没有用。百度则不支持该指令。

### 12.linkdomain:

该指令曾经是 SEOer 必用的外链查询工具，随着雅虎放弃自己的搜索技术，这个指令已经作废。这个指令只适用于雅虎。

### 13.related:

该指令只适用于 Google，返回的结果是与某个网站有关联的页面。这种关联到底指的是什么，Google 并没有明确说明，一般认为指的是有共同外部链接的网站。

## GitHub 的高级搜索方法

| 搜索条件         | 使用方法                   | 备注                                                     |
| ---------------- | -------------------------- | -------------------------------------------------------- |
| location:        | location:china             | 匹配填写的地址在 china 的开发者                          |
| language:        | language:python            | 匹配开发语言为 Python 的项目                             |
| followers:       | followers:>=1000           | 匹配拥有超过 1000 名关注着的项目                         |
| in:name          | in:name Wangrongsheng      | 匹配用户名为 Wangrongsheng 的开发者                      |
| in:descripton    | in:descripton python       | 匹配仓库描述里面有 Python 的项目                         |
| in:readme        | in:readme python           | 匹配 README 描述中有 Python 的项目                       |
| stars:           | stars:>=500                | 匹配收藏数量超过 500 的项目                              |
| forks:           | forks:>=500；forks: 10..20 | 匹配分支数量超过 500 的项目；匹配分支数量为 10-20 的项目 |
| size:            | size:>=5000                | 匹配仓库大于 5000KB 的仓库                               |
| created:         | created:>2023-01-01        | 匹配 2023 年以后创建的仓库                               |
| pushed:          | pushed:>2023-01-01         | 匹配仓库最近一次提交在 2023 年以后的仓库                 |
| license:         | license:apache-2.0         | 匹配使用 apache-2.0 协议的仓库                           |
| user:            | user:google                | 匹配用户 Google 上传的仓库                               |
| org:             | org:spring-cloud           | 匹配列出 org 的 spring-cloud 仓库                        |
| Awesome + 关键字 | 神器关键字                 | 帮助找到优秀的工具列表                                   |
