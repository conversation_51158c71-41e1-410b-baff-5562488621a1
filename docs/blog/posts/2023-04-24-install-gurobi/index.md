---
title: 安装 Gurobi 优化器
authors: 
  - <PERSON>
date: '2023-04-24'
slug: install-gurobi
categories:
  - 运筹学
  - Python
tags:
  - 运筹学
  - Python
links:
  - "Gurobi 安装包": https://www.gurobi.com/downloads/gurobi-software/
  - "申请学术许可": http://www.gurobi.cn/NewsView1.Asp?id=4
  - "Conda Gurobi 安装包": https://anaconda.org/Gurobi/gurobi/files
---

# 安装 Gurobi 优化器

本文记录了 Mac 安装 Gurobi 优化器的过程。

<!-- more -->

## 下载安装包

到[https://www.gurobi.com/downloads/gurobi-software/](https://www.gurobi.com/downloads/gurobi-software/)下载安装包：

![image-20230424202912561](index-image/image-20230424202912561.png)

## 申请学术许可

根据[http://www.gurobi.cn/NewsView1.Asp?id=4](http://www.gurobi.cn/NewsView1.Asp?id=4)的说明，填写申请表并签名，并同学籍验证报告一起发送到指定邮箱。我是晚上申请的，第二天一早就收到了许可证书。

## 安装学术许可证书

在终端输入：

```bash
gurobi.sh
```

会要求输入许可证书，输入正确后会生成一个名为 `gurobi.lic` 证书文件。根据提示，在 Mac 下，需要将证书文件放在 `/Library/gurobi/` 下。

![image-20230424204255321](index-image/image-20230424204255321.png)

## 在 Python 中使用 Gurobi

为了在 Python 中使用 Gurobi，需要在 Conda 环境中安装 Gurobi 的包。到[这里](https://anaconda.org/Gurobi/gurobi/files)找到与 Gurobi 版本和 Python 版本对应的安装包并下载。

建议在新建一个 Conda 环境。例如我将这个环境取名为 `gurobi`，并安装 3.9 版本的 Python：

```bash
conda create -n gurobi python=3.9
```

再将下载的文件安装到环境中：

```bash
conda activate gurobi
conda install ./gurobi-10.0.1-py39_0.tar.bz2
```

## 测试是否安装成功

```python
import numpy as np
import gurobipy as gp
from gurobipy import GRB
```

$$
\begin{eqnarray}
\text{minimize}  && 2x_1 &+& 3x_2 &+& 3x_3 &+& x_4 &-& 2x_5 & \\
\text{subject to}&& x_1  &+& 3x_2 & &    &+& 4x_4 &+& x_5  & =2 \\
            && x_1  &+& 2x_2 & &    &-& 3x_4 &+& x_5  & =2 \\
           &-& x_1  &-& 4x_2 &+& 3x_3 & &  & & & =1 \\
           &&x_1,&\cdots,&x_5&\geq 0.
\end{eqnarray}
$$

```python
c = gp.tuplelist([2, 3, 3, 1, -2])
b = gp.tuplelist([2, 2, 1])
A = gp.tuplelist(
    [
        [1, 3, 0, 4, 1],
        [1, 2, 0, -3, 1],
        [-1, -4, 3, 0, 0],
    ],
)
```

```python
m = gp.Model("Exercise")
x = m.addVars(
    range(5),
    vtype=GRB.CONTINUOUS,
    name="x",
)
cons = m.addConstrs(
    (x.prod(A[i]) == b[i] for i in range(len(b))), name="constrs"  # <= >=
)
m.setObjective(x.prod(c), GRB.MINIMIZE)
```

```python
m.optimize()
```

![image-20230424205423331](index-image/image-20230424205423331.png)
