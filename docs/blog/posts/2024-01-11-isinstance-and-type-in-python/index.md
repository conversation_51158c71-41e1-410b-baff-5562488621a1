---
title: Python 中的 isinstance 和 type 的区别
authors: 
  - <PERSON>
date: '2024-01-11'
slug: isinstance-and-type-in-python
categories:
  - Python
tags:
  - Python
---

# Python 中的 `isinstance` 和 `type` 的区别

在 Python 中，`isinstance` 和 `type` 都是用于检查对象类型的函数，但它们的使用场景和结果有所不同。本文介绍了 Python 中的 `isinstance` 和 `type` 的区别。

<!-- more -->

## `isinstance`

`isinstance` 函数用于检查一个对象是否是某个类或其子类的实例。它的语法如下：

```python
isinstance(object, classinfo)
```

其中，`object` 是要检查的对象，`classinfo` 是类名或类名元组。如果 `object` 是 `classinfo` 的 ==实例或其子类的实例== ，则返回 `True`，否则返回 `False`。

例如：

```python
class A:
    pass


class B(A):
    pass


a = A()
b = B()

print(isinstance(a, A))  # True
print(isinstance(b, A))  # True
print(isinstance(a, B))  # False
print(isinstance(b, B))  # True
```

## `type`

`type` 函数用于获取一个对象的类型。它的语法如下：

```python
type(object)
```

其中，`object` 是要获取类型的对象。`type` 函数返回的是对象的实际类型，而不是其父类或子类的类型。

例如：

```python
class A:
    pass


class B(A):
    pass


a = A()
b = B()

print(type(a))  # <class '__main__.A'>
print(type(b))  # <class '__main__.B'>
```

## 总结

`isinstance` 和 `type` 的主要区别在于，`isinstance` 检查的是对象的继承关系，而 `type` 只检查对象的直接类型。因此，在需要检查对象的继承关系时，应该使用 `isinstance`，而在只需要获取对象的直接类型时，应该使用 `type`。
