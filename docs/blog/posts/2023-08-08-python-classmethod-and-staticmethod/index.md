---
title: Python 中的类方法和静态方法
authors: 
  - <PERSON>
date: '2023-08-08'
slug: python-classmethod-and-staticmethod
categories:
  - Python
tags:
  - Python
links:
  - "Python 中的类方法和静态方法": https://jianghushinian.cn/2021/10/13/Python-%E4%B8%AD%E7%9A%84%E7%B1%BB%E6%96%B9%E6%B3%95%E5%92%8C%E9%9D%99%E6%80%81%E6%96%B9%E6%B3%95/
---

# Python 中的类方法和静态方法

Python 中常见的方法分三种：实例方法、类方法和静态方法。实例方法最为常见，也最容易理解，而另外两种方法则稍难理解。本文介绍了类方法和静态方法的用途，并提供了代码示例。

<!-- more -->

## 实例方法

首先我们来简单回顾下 Python 中实例方法的定义：

```python
class Hello(object):
    def say_hello(self, name):
        print("hello", name)
```

如果要调用 `Hello` 类中定义的 `say_hello` 实例方法，需要先实例化一个实例对象 `Hello()`，再进行调用：

```python
h = Hello()
h.say_hello("world")
# > hello world
```

## 类方法

回顾了实例方法，我们再来看下类方法如何使用。

类方法定义如下：

```python
class Hello(object):
    @classmethod
    def print_class_name(cls):
        print(cls.__name__)
```

对比实例方法，我们可以发现，类方法在定义上与实例方法有两处不同：首先在方法上面多了一个装饰器 `@classmethod` 标识这个方法为类方法，而不是实例方法；然后是方法参数由 `self` 变成了 `cls` 表示这个方法接收到的是一个类，而不是实例对象。

类方法的调用无需对类进行实例化，可以直接通过类来调用：

```python
Hello.print_class_name()
# > Hello
```

通过 `cls.__name__` 的方式我们打印了 `Hello` 类的类名。类方法中 `cls` 等价于 `Hello`，就像实例方法中 `self` 等价于实例对象 `Hello()` 一样。同 `self` 一样，`cls` 也是约定俗成的变量名，我们也可以不写成 `cls` 而写成任何我们喜欢的变量名。

类方法其实也可以通过实例对象进行调用：

```
h = Hello()
h.print_class_name()
# > Hello
```

得到的结果是一样的，其实当我们用实例对象调用类方法的时候，Python 解释器会自动帮我们获取到实例对象所属的类，然后将其当作参数传给类方法。

现在我们了解了类方法的语法，那么什么时候需要用到类方法呢？

类方法最常用的两个场景一个是类的继承，另一个是构造方法。

#### 类的继承中使用类方法

首先来看如何在类继承中使用 `cls` 来做子类的名称绑定。

```python
class Animal(object):
    def __init__(self, name):
        self.name = name

    @classmethod
    def shout(cls):
        print(f"{cls.__name__} shout")


class Cat(Animal):
    pass


class Dog(Animal):
    pass


cat = Cat("cat")
cat.shout()
# > Cat shout

dog = Dog("dog")
dog.shout()
# > Dog shout
```

这里我们定义一个动物类 `Animal`，其有一个 `shout` 方法用来打印哪种动物在叫。`Cat` 和 `Dog` 类都继承自 `Animal` 类，当通过 `Cat` 类或 `Dog` 类的实例对象调用 `shout` 类方法时，就会分别打印出 `Cat shout` 和 `Dog shout`。这样我们就实现了只需在父类中定义一个类方法，便可以在子类调用父类方法的时候根据类型的不同自动获取子类的类型名称。如果不使用类方法来定义，我们可能会写出如下代码：

```python
class Animal(object):
    def __init__(self, name):
        self.name = name

    def shout(self):
        print("Animal shout")


class Cat(Animal):
    def shout(self):
        print("Cat shout")


class Dog(Animal):
    def shout(self):
        print("Dog shout")


cat = Cat("cat")
cat.shout()
# > Cat shout

dog = Dog("dog")
dog.shout()
# > Dog shout
```

虽然同样能实现功能，但这种实现大大增加了工作量，一是子类需要重写父类的方法，二是将类名 `Cat`、`Dog` 都进行了硬编码，这种写法极不推荐。

#### 使用类方法实现构造方法

我们定义一个 `Poerson` 类，它的 `__init__` 方法接收两个参数 `first` 和 `last` 分别用来接收用户的 `first_name` 和 `last_name`，它还有一个 `name` 方法用来获取用户的全名。

```python
class Person(object):
    def __init__(self, first, last):
        self.first_name = first
        self.last_name = last

    def name(self):
        return self.first_name + self.last_name


p = Person("江湖", "十年")
print(p.name())
# > 江湖十年
```

假设我们现在有一个爬虫程序，能够在网络上的某个网站中爬取到很多的用户名，这些用户名可能是 `江湖-十年`、`江湖_十年` 或者 `江湖十年` 这三种形式。每一个用户名我们都需要实例化一个 `Person` 对象，由于用户名有三种形式，我们可以考虑使用一个函数来处理不同用户名，然后将处理后的用户名传给 `Person` 类来生成 `Person` 对象，可以写出如下代码：

```python
def generate_person(name):
    if "-" in name:
        first, last = name.split("-")
    elif "_" in name:
        first, last = name.split("_")
    else:
        first, last = name, ""
    return Person(first, last)


p1 = generate_person("江湖 - 十年")
p2 = generate_person("江湖_十年")
p3 = generate_person("江湖十年")
print(p1.name())
# > 江湖十年
print(p2.name())
# > 江湖十年
print(p3.name())
# > 江湖十年
```

这样，不管我们爬取到的用户名是哪种形式，都可以通过 `generate_person` 函数先预处理一下获取到 `first_name` 和 `last_name`，然后实例化 `Person` 对象进行返回。

其实有一种更好的方式对用户名进行预处理，那就是使用类方法：

```python
class Person(object):
    def __init__(self, first, last):
        self.first_name = first
        self.last_name = last

    def name(self):
        return self.first_name + self.last_name

    @classmethod
    def new(cls, name):
        if "-" in name:
            first, last = name.split("-")
        elif "_" in name:
            first, last = name.split("_")
        else:
            first, last = name, ""
        return cls(first, last)


p1 = Person.new("江湖 - 十年")
p2 = Person.new("江湖_十年")
p3 = Person.new("江湖十年")
print(p1.name())
# > 江湖十年
print(p2.name())
# > 江湖十年
print(p3.name())
# > 江湖十年
```

我们将 `generate_person` 函数移动到 `Person` 类内部作为一个类方法，并将其重命名为 `new`，最后通过 `return` 一个 `cls` 实例来返回 `Person` 实例对象。

熟悉 `Java` 等静态语言的同学应该很容易想到，这个类方法 `new` 实际上就是其他编程语言中的 `构造函数`。所以我们也就通过类方法的形式，在 Python 中实现了构造方法（函数）。

相比于使用 `generate_person` 函数生成 `Person` 实例对象，使用 `new` 构造方法的好处是代码封装性更强，也更容易理解。构造函数本就应该属于类的一部分，而不是单独写一个函数来实现。

爬虫框架 `Scrapy` 的 `Item Pipeline` 中有一个叫 [`from_crawler`](https://docs.scrapy.org/en/latest/topics/item-pipeline.html#from_crawler) 的类方法，实际上就是 `Scrapy` 提供给我们的一个构造方法。

## 静态方法

Python 中的静态方法是一种特殊的方法，它与类相关联，但与实例无关。在 Python 中，类方法和实例方法都需要至少一个参数来引用类或实例本身。然而，静态方法不需要引用类或实例本身，因此它在使用时更加灵活。

要定义一个静态方法，只需要在方法上方加上`@staticmethod`装饰器。下面是一个示例：

```python
class MathUtils:
    @staticmethod
    def add(a, b):
        return a + b


print(MathUtils.add(3, 5))  # 输出 8
```

在这个示例中，我们定义了一个 MathUtils 类，并在其中定义了一个静态方法`add`。我们可以直接通过类名调用该方法，而不需要先创建类的实例。这使得静态方法非常适合用于实现通用的功能，这些功能不依赖于类的任何特定实例。

静态方法也可以在实例中调用，但是在调用时需要使用实例的类来引用该方法。下面是一个示例：

```python
class MathUtils:
    @staticmethod
    def add(a, b):
        return a + b


math = MathUtils()
print(math.add(3, 5))  # 输出 8
```

在这个示例中，我们首先创建了 MathUtils 的实例`math`，然后通过该实例调用静态方法`add`。实际上，我们传递给`add`方法的第一个参数是`math`的类`MathUtils`本身。

总的来说，静态方法是一种非常方便的方法类型，它可以使我们在不使用类实例的情况下定义和调用方法。这种灵活性使得静态方法非常适合用于实现通用的功能，或者用于不需要引用类或实例本身的方法。在编写 Python 代码时，我们应该根据实际需要选择并恰当使用静态方法。
