---
title: Python 使用 * 以强制调用者使用关键字参数
authors: 
  - <PERSON>
date: '2023-10-04'
slug: python-keyward-only-argument
categories:
  - Python
tags:
  - Python
---

# Python 使用 `*` 以强制调用者使用关键字参数

在 Python 中，函数可以接受不同类型的参数，包括位置参数和关键字参数。位置参数必须按照特定的顺序传递给函数，而关键字参数可以根据参数名指定。

在某些情况下，我们可能希望定义一个函数，其中一部分参数只能以关键字形式指定。为此，我们可以使用独立的 `*` 号来分隔这些参数。

<!-- more -->

下面是一个示例函数，其中参数 `a` 则可以使用位置或关键字形式指定，而参数 `b` 和 `c` 只能以关键字形式指定：

```python
def example(a, *, b, c):
    print(f"a: {a}, b: {b}, c: {c}")
```

在上述函数中，`*` 号之前的所有参数可以用 ==位置或关键字形式== 指定，而`*`号之后的参数 ==只能以关键字形式== 指定。

可以通过以下两种方式调用上述函数：

1. 使用关键字形式指定所有参数：

    ```python
    example(a=1, b=2, c=3)
    ```

    输出：`a: 1, b: 2, c: 3`

2. 使用位置形式和关键字形式分别指定参数：

    ```python
    example(1, b=2, c=3)
    ```

    输出：`a: 1, b: 2, c: 3`

3. 但是如果尝试使用位置形式指定 `b` 参数，将会出现错误。例如：

    ```python
    example(1, 2, c=3)
    ```

    这会导致 `TypeError: example() takes 1 positional argument but 2 positional arguments (and 1 keyword-only argument) were given` 错误。

这种方式有助于提高函数的可读性和可维护性。通过强制使用关键字形式指定某些参数，可以保证调用者清楚地表达函数的意图。
