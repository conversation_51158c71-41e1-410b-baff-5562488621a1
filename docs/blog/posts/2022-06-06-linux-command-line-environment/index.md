---
title: Linux 命令行—配置环境
authors:
  - <PERSON> Feng
date: "2022-06-06"
slug: Linux-Command-Line-Environment
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
toc: yes
description: "配置命令行。"
---

# Linux 命令行—配置环境

配置命令行。

<!-- more -->

## Nano：命令行编辑器

Nano 是一个命令行编辑器。它和记事本很像，特点是：

- 只能通过命令行来运行。
- 只接受来自键盘的输入。

## 设置 Bash Profile

### 用 Nano 编辑 bash_profile

```bash
nano ~/.bash_profile
```

其中，`~`代表根目录。

进入文件后，可以写上字符串，例如：

```bash
"Welcome, Jeremy"
```

使用`source ~/.bash_profile`引入刚才编辑的 profile 文件，屏幕上会打印“Welcome, Jeremy”。这个功能可用作欢迎语。

### Alias：给命令设置别称（快捷键）

在 bash_profile 中，添加：

```bash
alias pd="pwd"
alias hy="history"
alias ll="ls -la"
```

就可以：

- 用`pd`打印当前工作路径。
- 用`hy`打印历史命令。
- 用`ll`打印所有文件和文件夹名，包括隐藏的文件和文件夹，包括文件的详细信息。

总之，给命令设置别名可以让我们用更少的字符实现和同样的功能。

### 修改命令提示符的样式

在 bash_profile 中，添加

```bash
export PS1=">> "
```

即可将命令提示符的样式（默认是`$`）修改为`>> `，即每个命令行的开头都是`>> `。

### 环境变量

#### 设置环境变量

1. 在 bash_profile 中，添加

   ```bash
   export USER="Jeremy"
   ```

   即可将`USER`设置为`Jeremy`。

2. 可以用

   ```bash
   echo $USER
   ```

   来查看`USER`变量的值。注意变量前面需要加`$`。

#### HOME 环境变量

在命令行中输入

```bash
$ echo $HOME
```

可以查看 HOME 环境变量。

#### PATH 环境变量

在命令行中输入

```bash
$ echo $PATH
```

可以查看 PATH 环境变量。

PATH 变量是一些路径，这些路径中存放了一些命令（Command），例如`pwd`、`ls`等等。我们可以在命令行中直接输入`pwd`，计算机知道`pwd`这个命令是来自某个 PATH 的。

#### 查看环境变量

1. 在命令行中输入

   ```bash
   $ env
   ```

   可以查看所有环境变量。

2. 下面的命令，是通过`grep`（正则表达式）将 PATH 环境变量输出来。

   ```bash
   env | grep PATH
   ```

   它和

   ```bash
   $ echo $PATH
   ```

   效果是一样的。
