---
title: 一行代码实现探索性数据分析
authors: 
  - <PERSON>
date: '2022-11-19'
slug: exploratory-data-analysis-with-dataprep
categories:
  - Python
tags:
  - Python
toc: yes
description: 使用`DataPrep`轻松实现探索性数据分析。
---

# 一行代码实现探索性数据分析

使用`DataPrep`轻松实现探索性数据分析。

![image-20221119170146290](index-image/image-20221119170146290.png)

<!-- more -->

## 安装`DataPrep`

```shell
pip install dataprep
```

## 一行代码生成探索性数据分析报告

```python
import pandas as pd
from dataprep.eda import create_report

df = pd.read_csv(
    "https://raw.githubusercontent.com/mwaskom/seaborn-data/master/iris.csv"
)
report = create_report(df)
report  # show report in notebook
report.save("My Fantastic Report")  # save report to local disk
report.show_browser()  # show report in the browser
```

> 更多教程可以参考[官方文档](https://docs.dataprep.ai/api_reference/dataprep.html)。

浏览器中会自动打开生成的报告文档，其中包含了基本的描述性统计图表、相关性情况和缺失值情况等。

![image-20221119170146290](index-image/image-20221119170146290.png)
