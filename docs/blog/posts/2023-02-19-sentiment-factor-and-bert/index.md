---
title: 舆情因子和 BERT 情感分类模型
authors:
  - <PERSON>
date: "2023-02-19"
slug: sentiment-factor-and-bert
categories:
  - 量化研究
tags:
  - 量化研究
links:
  - "舆情因子和 BERT 情感分类模型 - 华泰证券": https://crm.htsc.com.cn/doc/2020/10750101/dcf59b7d-dff9-4097-9ccf-ffe864fedc6b.pdf
---

# 舆情因子和 BERT 情感分类模型

本文总结了研报 [舆情因子和 BERT 情感分类模型 - 华泰证券](https://crm.htsc.com.cn/doc/2020/10750101/dcf59b7d-dff9-4097-9ccf-ffe864fedc6b.pdf) 的主要内容。

## 思路与框架

1. 基于 Wind 金融新闻数据，提取其中的情感正负面标签，构建日频的新闻舆情因子。
2. 使用回归法、IC 值分析法和分层测试法，检验新闻舆情因子。
3. 基于已有情感标注的 Wind 金融新闻数据，测试 BERT 模型在金融情感分类任务的表现。

<!-- more -->

## 新闻舆情因子构建过程

1. 对原始新闻数据进行预处理（如剔除特定新闻、文本合并等）。

2. 每只股票 $i$ 在每个自然日 $t$ 的情感得分为

   $$
   S_{i, t}=\text { 正面新闻数量 - 负面新闻数量 }
   $$

3. 每只股票 $i$ 在每个交易日 $T$ 的新闻舆情因子为

   $$
   F_{i, T}=\sum_{t=T-29}^T w_t S_{i, t}, \quad w_t=\frac{30-(T-t)}{30}
   $$

   上式中的 $w_t$ 使得新闻舆情因子是个股情感得分的**线性衰减加权和**。

4. 剔除证券行业的个股。

5. 对新闻舆情因子进行行业市值中性。

## 单因子测试方法

### 回归法

将第 $T$ 期的因子暴露度向量与 $T + 1$ 期的股票收益向量进行线性回归：

$$
r^{T+1}=X^T a^T+\sum_j Indus_j^{\mathrm{T}} b_j^T+ln\_m k t^{\mathrm{T}} b^T+\varepsilon^T
$$

其中，回归系数 $a^T$ 即为因子在 $T$ 期的因子收益率，还能得到该因子收益率在本期回归中的显著度水平，即假设检验的 t 值。

#### 细节处理

1. 预测目标：$r^{T+1}$ 可选未来 20 个交易日内个股收益率。
2. 中位数去极值：避免极端值对回归结果造成较大的影响。
3. 中性化：回归方程中加入了 $Indus_j^{\mathrm{T}}$ 和 $ln\_m k t^{\mathrm{T}}$，考虑了行业和市值对个股收益率的影响。（个人认为研报中的符号没有表述清楚，不明白研报中的符号究竟是标量还是向量。）市值取对数是为了让其更加符合正态分布，否则极少数大市值的公司会使得市值呈右偏分布。
4. 标准化：将因子暴露度减去其现在的均值、除以其标准差。让不同因子的暴露度之间具有可比性。
5. 缺失值处理：例如填充缺失值。
6. 采用加权最小二乘回归（WLS）：使用个股流通市值的平方根作为权重。

#### 因子评价方法

!!! tip "因子评价方法"

    1. t 值序列绝对值均值——因子显著性的重要判据；
    2. t 值序列绝对值大于 2 的占比——判断因子的显著性是否稳定；
    3. t 值序列均值——与第 1 点结合，能判断因子 t 值正负方向是否稳定；
    4. 因子收益率序列均值——判断因子收益率的大小。

### IC 值分析法

因子的 IC 值是指因子在第 T 期的暴露度向量与 T+1 期的股票收益向量的相关系数，即

$$
I C^T=\operatorname{corr}\left(r^{T+1}, X^T\right)
$$

#### 细节处理

上式中因子暴露度向量 $X^T$ 一般不会直接采用原始因子值，而是经过去极值、中性化等手段处理之后的因子值。

在实际计算中，使用 Pearson 相关系数可能受因子极端值影响较大，使用 Spearman 秩相关系数则更稳健一些，这种方式下计算出来的 IC 一般称为 Rank IC。

#### 因子评价方法

!!! tip "因子评价方法"

    1. Rank IC 值序列均值——因子显著性；
    2. Rank IC 值序列标准差——因子稳定性；
    3. IC_IR(Rank IC 值序列均值与标准差的比值）——因子有效性；
    4. Rank IC 值序列大于零的占比——因子作用方向是否稳定。

### 分层回测法

#### 构建分层组合

依照因子值对股票进行打分，在每个截面期核算因子值，构建分层组合。例如将股票池
内所有个股按处理后的因子值从大到小进行排序，等分 N 层，每层内部的个股等权重配置。

#### 计算多空组合收益

用 Top 组每天的收益减去 Bottom 组每天的收益，得到每日多空收益序列 $r_1, r_2,\cdots, r_n$，则多空组合在第 $n$ 天的净值等于 $(1+r_1) (1+r_2) ⋯(1+r_n)$ 。

#### 因子评价方法

!!! tip "因子评价方法"

    全部 N 层组合年化收益率 （观察是否单调变化） ，多空组合的年化收益率、夏普比率、最大回撤等。

### 因子测试结果

新闻舆情因子在沪深 300 成分股内表现最好，因子收益率均值达 0.29%，RankIC 均值达 6.13%，多空组合年化收益率达 18.73%。

## BERT 运用于文本情感分类

我不太清楚 BERT 的底层学习机制，目前只能大概理解如何将 BERT 运用于分类问题的关键思路：

1. 带有情感标注的金融新闻是具有 [CLS] 字符信息的，我们可以在 BERT 模型的最顶层添加一个 Softmax 分类层，并以 [CLS] 字符的输出信息作为分类层的输入，即可得到 BERT 对该语句的情感分类结果。

2. 对于那些不带有情感标注的金融新闻，我们就可以用已经训练好的模型来预测这条新闻的 [CLS] 字符，将 [CLS] 字符作为 Softmax 分类层的输入，即可得到情感分类结果。

    ![image-20230219151848442](index-image/image-20230219151848442.png)

3. 我们可以将训练好的模型运用于大量不带有情感标注的金融新闻，对个股新闻舆情进行更全面、无偏的判断，再用上述因子测试方法对新闻舆情因子进行检验。
