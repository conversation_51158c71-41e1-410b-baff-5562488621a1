---
title: Qlib 使用笔记
authors: 
  - <PERSON>
date: '2023-04-19'
slug: qlib
categories:
  - 量化研究
tags:
  - 量化研究
links:
  - "Qlib GitHub": https://github.com/microsoft/qlib
  - "Qlib 官方文档": https://qlib.readthedocs.io/en/latest/index.html
  - "下载全量数据": https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#get-qlib-databin-file
  - "下载增量数据": https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#collector-yahoofinance-data-to-qlib
  - "导入自定义数据": https://zhuanlan.zhihu.com/p/554653707
---

# Qlib 使用笔记

记录使用 Qlib 的代码和经验。


<!-- more -->

## 下载数据到本地

首先需要进入 `qlib` 包所在的目录：

```bash
cd /Users/<USER>/miniconda3/lib/python3.8/site-packages/qlib
```

### 日频数据

#### 首次下载

参考：

[https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#get-qlib-databin-file](https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#get-qlib-databin-file)

```bash
python scripts/get_data.py qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn
```

### 增量下载（或指定任意时间段）

参考：

[https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#collector-yahoofinance-data-to-qlib](https://github.com/microsoft/qlib/tree/main/scripts/data_collector/yahoo#collector-yahoofinance-data-to-qlib)

```bash
python collector.py download_data --source_dir ~/.qlib/stock_data/source/cn_data --start 2020-01-01 --end 2020-12-31 --delay 1 --interval 1d --region CN
```

### 分钟频数据

#### 首次下载

```bash
python scripts/get_data.py qlib_data --target_dir ~/.qlib/qlib_data/cn_data_1min --region cn --interval 1min
```

!!! tip "使用 [https://gitpod.io/](https://gitpod.io/) 加速下载"

	在国内直接下载全量分钟频数据，下载速度十分感人，至少需要 3 小时才能下载完。
	
	![image-20230419211433253](index-image/image-20230419211433253.png)
	
	使用 [https://gitpod.io/](https://gitpod.io/) 的服务器，简单安装 `qlib` 后，再下载分钟频数据，速度非常快！实测 1 分多钟就全部下载好了。
	
	![image-20230419215614160](index-image/image-20230419215614160.png)
	
	![image-20230419220318064](index-image/image-20230419220318064.png)
	
	压缩的分钟频数据一共 1.3 G，再下载到本地即可。
	
	![image-20230419220607886](index-image/image-20230419220607886.png)

#### 增量下载（最多下载最近一个月的分钟频数据）

```bash
python collector.py download_data --source_dir ~/.qlib/stock_data/source/cn_data_1min --delay 1 --interval 1min --region CN
```

## 导入自定义数据

参考：

[https://zhuanlan.zhihu.com/p/554653707](https://zhuanlan.zhihu.com/p/554653707)

```bash
python /Users/<USER>/miniconda3/lib/python3.8/site-packages/qlib/scripts/dump_bin.py dump_all --csv_path  /Users/<USER>/Desktop/dump_to_qlib --qlib_dir ~/.qlib/qlib_data/cn_data --include_fields open,high,low,close,volume,amount --date_field_name datetime --freq 1min
```

## 获取日历数据

```python
from qlib.data.data import Cal

Cal.calendar(freq="1min")
```

或

```python
from qlib.data import D

D.calendar(start_time="2021-01-01", end_time="2021-2-01", freq="1min")
```

其中，`freq` 参数用 `min`、`day`、`5min` 等等都可以。

![image-20230510195723427](index-image/image-20230510195723427.png)

