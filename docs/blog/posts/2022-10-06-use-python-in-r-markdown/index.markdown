---
title: 在 Rmarkdown 中使用 Python
authors: 
  - <PERSON> Feng
date: '2022-10-06'
slug: use-python-in-r-markdown
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 在 Rmarkdown 中运行 Python 代码，并自主配置 Python 环境。
---

# 在 Rmarkdown 中使用 Python

在 Rmarkdown 中运行 Python 代码，并自主配置 Python 环境。

<!-- more -->

## 必要的包和切换环境

在 Rmarkdown 中运行 Python 代码，需要用到`reticulate`包。

安装`reticulate`时，会安装名为`r-miniconda`的`conda`环境。在之后使用 Python 时，默认使用`r-reticulate`环境。这个环境只有`numpy`的库，如果需要使用其他库则需要安装。具体安装方法可以参考[官方文档](https://rstudio.github.io/reticulate/articles/python_packages.html)：

-   `py_install("pandas")`
-   `conda_install("r-reticulate", "scipy")`

> 熟悉常用的安装包的方法：

> 1. 打开 Anaconda Prompt(R-MINI\~1) 终端。
>
> 2. 输入`conda activate r-reticulate`，这是 reticulate 里默认用的 conda 环境。
>
> 3. 把代理网络关闭（用`pip`通过清华源安装包的时候，用代理环境可能会报错），输入`pip install package_name`即可安装。

另一种解决方案是将环境切换为我常用的，这个环境里有我需要的库。

切换环境的方法是：

1.  先在 R Session 中输入命令`reticulate::use_python("C:\\ProgramData\\Anaconda3")`。
2.  再导入`reticulate`包。

上面两个步骤最好不要调换。如果先导入`reticulate`包，再切换环境，这时由于`reticulate`包已经使用了默认环境，就不能再切换环境了。报错信息如下。解决方案可以参考[Stack overflow 上的回答](https://stackoverflow.com/a/64286000/16760102)。

![切换环境的报错](%E5%88%87%E6%8D%A2%E7%8E%AF%E5%A2%83%E7%9A%84%E6%8A%A5%E9%94%99.jpg)

## 使用示例


```python
print("Hello Python!")
```

```
## Hello Python!
```


```python
# Import pandas library
import pandas as pd

# initialize list elements
data = [10, 20, 30, 40, 50, 60]

# Create the pandas DataFrame with column name is provided explicitly
df = pd.DataFrame(data, columns=["Numbers"])

# print dataframe.
df
```

```
##    Numbers
## 0       10
## 1       20
## 2       30
## 3       40
## 4       50
## 5       60
```
