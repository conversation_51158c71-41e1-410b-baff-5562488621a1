---
title: Python 进阶教程系列 9：单例模式
authors: 
  - <PERSON> Feng
date: '2023-08-04'
slug: advanced-python-9-singleton-design-pattern
categories:
  - Python
tags:
  - Python
links:
  - "Singleton Design Pattern - Advanced Python Tutorial #9": https://youtu.be/Qb4rMvFRLJw
---

# Python 进阶教程系列 9：单例模式

本文是 Python 进阶教程系列 9，主要介绍了 Python 单例模式。

在软件开发中，单例模式是一种常见的设计模式，它确保一个类只有一个实例，并提供全局访问点。Python 作为一种灵活而强大的编程语言，也提供了多种实现单例模式的方法。

本文将介绍 Python 中常用的两种单例模式实现方式：基于模块和基于类装饰器。

<!-- more -->

## 基于模块的单例模式

基于模块的单例模式是 Python 中最简单的实现方式之一。Python 的模块在程序中只会被导入一次，因此可以将需要实现单例的类定义在一个模块中，然后在其他地方导入该模块来获取单例对象。

```python
# singleton_module.py


class SingletonClass:
    def __init__(self):
        # 初始化操作
        pass


# 在其他地方导入模块获取单例对象
from singleton_module import SingletonClass

singleton = SingletonClass()
```

在上述示例中，`SingletonClass`是需要实现单例的类，它只会被实例化一次。在其他地方导入`singleton_module`模块时，可以直接使用`SingletonClass`的实例`singleton`。

## 基于类装饰器的单例模式

基于类装饰器的单例模式是另一种常见的实现方式。通过定义一个装饰器函数，将其应用于需要实现单例的类上，从而实现类的唯一实例化。

```python
import functools


def singleton(cls):
    _instance = {}

    @functools.wraps(cls)
    def _singleton(*args, **kargs):
        if cls not in _instance:
            _instance[cls] = cls(*args, **kargs)
        return _instance[cls]

    return _singleton


@singleton
class A(object):
    a = 1

    def __init__(self, x=0):
        self.x = x


a1 = A(2)
a2 = A(3)
```

```python
print(a1.x)
print(a2.x)
print(id(a1))
print(id(a2))

# 2
# 2
# 4398036688
# 4398036688
```

在上述示例中，`singleton`是一个装饰器函数，它接受一个类作为参数，并返回一个包装函数`_singleton`。`_singleton`函数在每次实例化时会检查该类是否已经有实例存在，如果不存在，则创建一个新的实例并返回，否则直接返回已存在的实例。

使用装饰器`@singleton`修饰类`A`后，该类的实例化操作将被装饰器函数所控制，确保只有一个实例存在。

!!! note "Python 进阶教程系列文章"

	- [x] [Python 进阶教程系列 1：双下划线的魔法方法](../advanced-python-1-magic-method/)
	
	- [x] [Python 进阶教程系列 2：装饰器](../advanced-python-2-decorator/)
	
	- [x] [Python 进阶教程系列 3：生成器](../advanced-python-3-generator/)
	
	- [x] [Python 进阶教程系列 4：命令行参数](../advanced-python-4-argument-parsing/)
	
	- [x] [Python 进阶教程系列 5：获取和修改被保护的属性](../advanced-python-5-encapsulation/)
	
	- [x] [Python 进阶教程系列 6：使用 `mypy` 进行类型提示](../advanced-python-6-type-hinting/)
	
	- [x] [Python 进阶教程系列 7：工厂模式](../advanced-python-7-factory-design-pattern/)
	
	- [x] [Python 进阶教程系列 8：代理模式](../advanced-python-8-proxy-design-pattern/)
	
	- [x] [Python 进阶教程系列 9：单例模式](../advanced-python-9-singleton-design-pattern/)
	
	- [x] [Python 进阶教程系列 10：组合模式](../advanced-python-10-composite-design-pattern/)

