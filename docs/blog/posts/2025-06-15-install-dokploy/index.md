---
title: 国内服务器安装 Dokploy
authors:
  - <PERSON>
date: "2025-06-15"
slug: install-dokploy
categories:
  - Computer Science
  - IndieDev
tags:
  - Computer Science
  - IndieDev
links:
  - "Dokploy 的极简教程": https://mksaas.me/blog/dokploy
  - "阿里云 ECS 国内服务器部署 Dokploy 教程": https://coreychiu.com/blogs/how-to-install-dokploy-on-aliyun-ecs-server
  - "目前国内可用 Docker 镜像源汇总": https://www.coderjia.cn/archives/dba3f94c-a021-468a-8ac6-e840f85867ea
---

# 国内服务器安装 Dokploy

使用国内服务器安装 Dokploy 时，可能由于网络问题无法顺利下载某些 Docker 镜像而卡住。本文记录了修改 `install.sh` 并成功在一台国内服务器上安装 Dokploy 的过程。

![image-20250615150048780](index-image/image-20250615150048780.png)

<!-- more -->

## 下载官方安装脚本

```bash
wget -O install.sh https://dokploy.com/install.sh
```

## 修改安装脚本

根据 [这篇帖子](https://coreychiu.com/blogs/how-to-install-dokploy-on-aliyun-ecs-server) 中的教程，添加以下代码：

```sh hl_lines="1 2 17"
    docker pull swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/dokploy/dokploy:latest
    docker tag  swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/dokploy/dokploy:latest  docker.io/dokploy/dokploy:latest

    # Installation
    docker service create \
      --name dokploy \
      --replicas 1 \
      --network dokploy-network \
      --mount type=bind,source=/var/run/docker.sock,target=/var/run/docker.sock \
      --mount type=bind,source=/etc/dokploy,target=/etc/dokploy \
      --mount type=volume,source=dokploy-docker-config,target=/root/.docker \
      --publish published=3000,target=3000,mode=host \
      --update-parallelism 1 \
      --update-order stop-first \
      --constraint 'node.role == manager' \
      -e ADVERTISE_ADDR=$advertise_addr \
      swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/dokploy/dokploy
```

![image-20250615145540666](index-image/image-20250615145540666.png)

## 修改 Docker 配置

在 [这篇帖子](https://www.coderjia.cn/archives/dba3f94c-a021-468a-8ac6-e840f85867ea) 中可以查看国内可用的 Docker 镜像。

```json title="/etc/docker/daemon.json"
{
  "dns": ["*******", "*******"],
  "registry-mirrors": ["https://docker-0.unsee.tech"]
}
```

## 运行安装脚本

```bash
sh install.sh
```

等待几分钟即可安装完成。

![image-20250615135832861](index-image/image-20250615135832861.png)

在服务器的安全组开启 3000 端口的访问，即可在公网打开 Dokploy 的登录界面。

![image-20250615150242610](index-image/image-20250615150242610.png)

![image-20250615150048780](index-image/image-20250615150048780.png)
