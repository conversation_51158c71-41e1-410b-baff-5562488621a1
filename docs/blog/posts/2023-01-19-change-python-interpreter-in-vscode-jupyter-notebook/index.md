---
title: 修改 Jupyter Notebook 的默认 Python 解释器
authors: 
  - <PERSON>
date: '2023-01-19'
slug: change-python-interpreter-in-vscode-jupyter-notebook
categories:
  - Python
tags:
  - Python
---

# 修改 Jupyter Notebook 的默认 Python 解释器

Conda 可以十分方便地创建虚拟环境，便于在不同的项目中使用不同的 Python 版本、外部包等。今天在创建新的虚拟 Conda 环境后，在 VS Code 中没有找到刚刚创建的 Python 解释器。

解决方案是：

1. 先按++ctrl+shift+p++，调出`Select Interpreter`选项，这里应该可以看到最新创建的 Conda 环境；
2. 再按++ctrl+shift+p++，调出`Clear Cache and Reload Window`选项，重新加载窗口；
3. 最后点击右上角的“选择内核”，可以看到刚才创建的环境。

<!-- more -->

## 新建虚拟环境

```bash
conda create -n 环境名 python=3.10
```

![image-20230119150347074](index-image/image-20230119150347074.png)

## 按++ctrl+shift+p++，调出`select interpreter`选项

选择需要的环境：

![image-20230119150721410](index-image/image-20230119150721410.png)

## 按++ctrl+shift+p++，调出`Clear Cache and Reload Window`选项

重新加载窗口：

![image-20230119150822918](index-image/image-20230119150822918.png)

## 选择环境

右上角可以选择内核：

![image-20230119151000612](index-image/image-20230119151000612.png)

现在可以看到刚才创建的环境：

![image-20230119150540954](index-image/image-20230119150540954.png)