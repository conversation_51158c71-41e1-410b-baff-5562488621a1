---
title: SQL 刷题
authors: 
  - <PERSON>
date: '2022-10-09'
slug: sql-exercise
categories:
  - 数据库
tags:
  - 数据库
toc: yes
katex: yes
description: 记录遇到的`SQL`题，夯实使用数据库的技能。
---

# SQL 刷题

记录遇到的`SQL`题，夯实使用数据库的技能。

<!-- more -->

## [596. 超过 5 名学生的课](https://leetcode.cn/problems/classes-more-than-5-students/)

![image-20221009234735660](index-image/image-20221009234735660.png)

```SQL
SELECT
    class
FROM
    courses
GROUP BY class
HAVING COUNT(DISTINCT student) >= 5
```

- 关键：`GROUP BY`之后再`HAVING COUNT()`，`HAVING`相当于做了一次筛选。

## [1873. 计算特殊奖金](https://leetcode.cn/problems/calculate-special-bonus/)

![image-20221010170922698](index-image/image-20221010170922698.png)

```sql
select
	employee_id,
	if(employee_id % 2 != 0 and left(name,1) != 'M',salary,0) as bonus
from Employees
order by employee_id
```

- 取模运算用`MOD(a, b)`，或者`a % b`。

- 判断字符串是否以'M'开头用`LIKE 'M%'`。

  - 注意是`LIKE`，而不是`=`。

- `IF(expr,v1,v2)`

  其中：表达式 `expr` 得到不同的结果，当 `expr`为真是返回 `v1` 的值，否则返回 `v2`。

## [1667. 修复表中的名字](https://leetcode.cn/problems/fix-names-in-a-table/)

![image-20221011235906143](index-image/image-20221011235906143.png)

```SQL
select
    user_id,
    concat(upper(SUBSTRING(name,1,1)), lower(SUBSTRING(name,2,length(name)))) AS name
from Users
ORDER BY
    user_id
```

- `UPPER(str)` 与 `LOWER(str)` 
  - `UPPER(str)` 将字符串中所有字符转为大写

  - `LOWER(str)` 将字符串中所有字符转为小写

- `SUBSTRING(str, begin, end)`截取字符串。
  - `end` 不写默认为空，则截取到最后一个字符。
  - `SUBSTRING(name, 2)`从第二个截取到末尾，注意并不是下标，就是第二个。即`SUBSTRING(abcd, 2)`的结果是`bcd`。

- `CONCAT()`函数可以将多个字符串拼接在一起。

## [1484. 按日期分组销售产品](https://leetcode.cn/problems/group-sold-products-by-the-date/)

![image-20221013164603223](index-image/image-20221013164603223.png)

```SQL
SELECT
    sell_date,
    COUNT(DISTINCT product) AS num_sold,
    GROUP_CONCAT(DISTINCT product
                ORDER BY product
                SEPARATOR ',') AS products
FROM
    Activities
GROUP BY
    sell_date
ORDER BY
    sell_date
```

- `COUNT(DISTINCT product)`：唯一值计数
- `group_concat()`函数，顾名思义与 group by 有关，功能：将 group by 产生的同一个分组中的值连接起来，返回一个字符串结果。
  - `Distinct`：去除重复值。
  - `Order By`：组内的字符串按照什么排序。可以加`DESC`用于降序。
  - `Separator`：默认情况下，组的值由 `,`运算符分隔。如果想把分隔符改成`;`，可以使用`SEPARATOR ';'`。
