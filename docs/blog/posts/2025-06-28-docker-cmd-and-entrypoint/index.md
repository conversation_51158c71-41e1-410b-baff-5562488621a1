---
title: Docker 中的 CMD 和 ENTRYPOINT
authors:
  - <PERSON>
date: "2025-06-28"
slug: docker-cmd-and-entrypoint
categories:
  - Computer Science
  - IndieDev
tags:
  - Computer Science
  - IndieDev
links:
  - "Docker — 从入门到实践：CMD 容器启动命令": https://yeasy.gitbook.io/docker_practice/image/dockerfile/cmd
  - "Docker — 从入门到实践：ENTRYPOINT 入口点": https://yeasy.gitbook.io/docker_practice/image/dockerfile/entrypoint
---

# Docker 中的 CMD 和 ENTRYPOINT

Docker 中的 CMD 和 ENTRYPOINT 都是用来指定容器启动时执行的命令，但它们的作用和使用场景有明显区别，理解它们的区别能帮助我们更灵活地控制容器行为。

## CMD 和 ENTRYPOINT 的通俗理解

- **ENTRYPOINT**：就像容器的“主菜”，是容器启动时**必须执行的命令**，无论启动容器时传入什么参数，这个命令都会执行。可以把它想象成披萨店的“披萨”，披萨是必做的主食。
- **CMD**：就像披萨的“默认口味”，是容器启动时的**默认参数或命令**，如果启动容器时没有指定其他命令，CMD 会生效；如果指定了其他命令，CMD 会被覆盖。它是给 ENTRYPOINT 提供默认参数，或者单独作为默认命令使用。

- 如果只有 CMD，启动容器时指定的命令会替代 CMD。
- 如果只有 ENTRYPOINT，启动容器时的参数会附加到 ENTRYPOINT 命令后面。
- 如果两者结合，ENTRYPOINT 定义主命令，CMD 定义默认参数，启动时可以覆盖 CMD 参数但不会替换 ENTRYPOINT。

<!-- more -->

## 具体区别和用法

| 特性     | ENTRYPOINT                                       | CMD                                |
| :------- | :----------------------------------------------- | :--------------------------------- |
| 作用     | 固定执行的主命令，容器启动时一定执行             | 默认命令或参数，容器启动时可被覆盖 |
| 覆盖行为 | 不能被普通参数覆盖，除非用 `--entrypoint` 选项   | 可以被 `docker run` 后面的命令覆盖 |
| 灵活性   | 固定命令，参数可变                               | 命令或参数都可变                   |
| 推荐格式 | exec 格式（数组形式）                            | exec 格式（数组形式）或 shell 格式 |
| 典型用例 | 运行必须执行的程序，比如启动服务、脚本           | 提供默认参数，如默认启动参数或命令 |
| 组合使用 | 与 CMD 结合，ENTRYPOINT 是主命令，CMD 是默认参数 | 作为 ENTRYPOINT 的默认参数         |

## 使用示例

### 只用 CMD

```dockerfile
CMD ["echo", "Hello World"]
```

- 运行 `docker run image` 输出：Hello World
- 运行 `docker run image echo Bye` 输出：Bye（CMD 被覆盖）

### 只用 ENTRYPOINT

```dockerfile
ENTRYPOINT ["echo", "Hello"]
```

- 运行 `docker run image` 输出：Hello
- 运行 `docker run image Bye` 输出：Hello Bye（参数附加到 ENTRYPOINT）

### ENTRYPOINT 和 CMD 结合

```dockerfile
ENTRYPOINT ["echo"]
CMD ["Hello World"]
```

- 运行 `docker run image` 输出：Hello World（CMD 作为默认参数）
- 运行 `docker run image Bye` 输出：Bye（覆盖了 CMD 参数，但 ENTRYPOINT 不变）

## 总结

- **ENTRYPOINT** 用于定义容器启动时的核心命令，保证这个命令一定执行。
- **CMD** 用于定义默认参数或命令，方便用户在启动时覆盖。
- 两者结合使用时，ENTRYPOINT 是主命令，CMD 是默认参数，既保证了执行的确定性，又保留了灵活性。

这种设计让 Docker 容器既能保证核心逻辑不变，又能允许用户根据需要传入不同参数，满足各种使用场景。
