---
title: PyTorch Lecture 2：基础知识
authors: 
  - <PERSON>
date: '2022-07-12'
slug: pytorch-lecture-2-fundamentals
categories:
  - 深度学习
  - PyTorch
tags:
  - 深度学习
  - PyTorch
toc: yes
description: "PyTorch 的基础数据类型和简单运算。"
---

# PyTorch Lecture 2：基础知识

PyTorch 的基础数据类型和简单运算。

<!-- more -->

首先导入`torch`库。

```python
import torch
```

## 张量

张量（Tensor）可以理解为多维数组，它是 PyTorch 中的基本数据结构。

![张量](https://pica.zhimg.com/v2-a28dec3a92213d05ea50166508a24847_r.jpg?source=1940ef5c)

有关张量的创建、运算、数据处理等代码实现，可以参考[GitHub 代码](https://github.com/datawhalechina/thorough-pytorch/blob/main/notebook/%E7%AC%AC%E4%BA%8C%E7%AB%A0%20PyTorch%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86/%E4%BB%A3%E7%A0%81%E6%BC%94%E7%A4%BA%EF%BC%9APyTorch%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%86.ipynb)。

### 根据初始值创建张量

```python
# 创建 tensor，用 dtype 指定类型。注意类型要匹配
a = torch.tensor(1.0, dtype=torch.float)
b = torch.tensor(1, dtype=torch.long)
c = torch.tensor(1.0, dtype=torch.int8)
print(a, b, c)
```

```
tensor(1.) tensor(1) tensor(1, dtype=torch.int8)
```

### 根据随机值创建张量

```python
# 使用指定类型函数随机初始化指定大小的 tensor
d = torch.FloatTensor(2, 3)
e = torch.IntTensor(2)
f = torch.IntTensor([1, 2, 3, 4])  # 对于 Python 已经定义好的数据结构可以直接转换
print(d, "\n", e, "\n", f)
```

```python
tensor([[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]])
tensor([1065353216, 0], dtype=torch.int32)
tensor([1, 2, 3, 4], dtype=torch.int32)
```

### `tensor`和`numpy array`之间的相互转换

```python
import numpy as np

g = np.array([[1, 2, 3], [4, 5, 6]])
h = torch.tensor(g)
print(h)
i = torch.from_numpy(g)
print(i)
j = h.numpy()
print(j)
```

```
tensor([[1, 2, 3],
        [4, 5, 6]], dtype=torch.int32)
tensor([[1, 2, 3],
        [4, 5, 6]], dtype=torch.int32)
[[1 2 3]
 [4 5 6]]
```

注意：`torch.tensor`创建得到的张量和原数据是不共享内存的，张量对应的变量是独立变量。而`torch.from_numpy()`和`torch.as_tensor()`从`numpy array`创建得到的张量和原数据是共享内存的，张量对应的变量不是独立变量，修改`numpy array`会导致对应`tensor`的改变。      

### 常见的构造 Tensor 的函数

```python
k = torch.rand(2, 3)
l = torch.ones(2, 3)
m = torch.zeros(2, 3)
n = torch.arange(0, 10, 2)
print(k, "\n", l, "\n", m, "\n", n)
```

```python
tensor([[0.8086, 0.8206, 0.0462], [0.0070, 0.2045, 0.5737]])
tensor([[1.0, 1.0, 1.0], [1.0, 1.0, 1.0]])
tensor([[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]])
tensor([0, 2, 4, 6, 8])
```

### 查看 tensor 的维度信息（两种方式）

```python
print(k.shape)
print(k.size())
```

```python
torch.Size([2, 3])
torch.Size([2, 3])
```

### tensor 的运算（加法）

```python
o = torch.add(k, l)
print(o)
```

```python
tensor([[1.8086, 1.8206, 1.0462], [1.0070, 1.2045, 1.5737]])
```

### tensor 的索引方式（与 NumPy 类似）

```python
print(o[:, 0])  # 取第 1 列
print(o[0, :])  # 取第 1 行
```

```python
tensor([1.8086, 1.0070])
tensor([1.8086, 1.8206, 1.0462])
```

### 改变 tensor 形状的神器：view

```python
print(o.view((3, 2)))  # 3 行 2 列
print(o.view(-1, 2))  # 2 列，行数自适应
```

```python
tensor([[1.8086, 1.8206], [1.0462, 1.0070], [1.2045, 1.5737]])
tensor([[1.8086, 1.8206], [1.0462, 1.0070], [1.2045, 1.5737]])
```

### tensor 的广播机制（使用加法时要注意这个特性）

```python
p = torch.arange(1, 3).view(1, 2)
print(p)
q = torch.arange(1, 4).view(3, 1)
print(q)
print(p + q)
```

```python
tensor([[1, 2]])
tensor([[1], [2], [3]])
tensor([[2, 3], [3, 4], [4, 5]])
```

### 扩展和压缩 tensor 的维度：squeeze

例如，有一个张量是三维的，但其中的一维只有一个元素，可以通过`squeeze`进行降维，减小一个维度，但不会损失数据。

```python
print(o)
print(o.shape)
r = o.unsqueeze(1)
print(r)
print(r.shape)
```

```python
tensor([[1.8086, 1.8206, 1.0462], [1.0070, 1.2045, 1.5737]])
torch.Size([2, 3])
tensor([[[1.8086, 1.8206, 1.0462]], [[1.0070, 1.2045, 1.5737]]])

torch.Size([2, 1, 3])
```

降维：

```python
t = r.squeeze(1)  # 函数的参数是 1，意思是对第 2 个维度进行压缩。
print(t)
print(t.shape)
```

```python
tensor([[1.8086, 1.8206, 1.0462], [1.0070, 1.2045, 1.5737]])
torch.Size([2, 3])
```

## PyTorch 自动求导

这里将通过一个简单的函数  $y=x_1+2*x_2$  来说明 PyTorch 自动求导的过程。

```python
import torch

x1 = torch.tensor(1.0, requires_grad=True)
x2 = torch.tensor(2.0, requires_grad=True)
y = x1 + 2 * x2
print(y)
```

```
tensor(5., grad_fn=<AddBackward0>)
```

反向传播后看导数大小：

```python
y = x1 + 2 * x2
y.backward()
print(x1.grad.data)
print(x2.grad.data)
```

```python
tensor(1.0)
tensor(2.0)
```

上述结果表明，偏导数分别是 1 和 2。

## 并行计算

优势：

- 计算速度快；
- 训练效果一般更好。
