---
title: pandas 中的 axis
authors:
  - <PERSON>
date: "2023-01-10"
slug: axis-in pandas
categories:
  - Python
tags:
  - Python
toc: yes
links:
  - "Pandas 分不清 axis=0 axis=1？一文搞懂": https://mp.weixin.qq.com/s/ApSmOVyyJYX5sJ_B732nqA
---

# pandas 中的 axis

pandas 中的`#!py axis`参数代表对数据进行处理时遵循的方向。在单行、单列操作（如`#!py drop`）时，`#!py axis=0`和`#!py axis=1`分别代表删除行和删除列。在聚合操作（如求`mean`）时，`#!py axis=0`和`#!py axis=1`分别代表求列均值和行均值。

!!! note "总结"

    - `#!py axis=0`：
    
    如果是单行操作，就指的是某一行；
    
    如果是聚合操作，指的是跨行 cross rows。
    
    - `#!py axis=1`：
    
      如果是单列操作，就指的是某一列；
    
      如果是聚合操作，指的是跨列 cross columns。

<!-- more -->

本文转载自公众号`Python For Finance`的 [这篇帖子](https://mp.weixin.qq.com/s/ApSmOVyyJYX5sJ_B732nqA)，已获得作者授权。

## 准备数据

```python
import pandas as pd

df = pd.DataFrame(
    [[1, 1, 1, 1], [2, 2, 2, 2], [3, 3, 3, 3]],
    columns=["a", "b", "c", "d"],
    index=["one", "two", "three"],
)
print(df)
```

结果为：

```
       a  b  c  d
one    1  1  1  1
two    2  2  2  2
three  3  3  3  3
```

## `#!py axis=0`代表行？`#!py axis=1`代表列？

有人认为`axis=0`代表行，`axis=1`代表列，那么你就会迷惑：

## `drop`函数

`drop`函数中`axis=0`删除行，`axis=1`删除列

删除 a 列：

```python
print(df.drop("a", axis=1))
```

结果为：

```
       b  c  d
one    1  1  1
two    2  2  2
three  3  3  3
```

删除`one`行：

```python
print(df.drop("one", axis=0))
```

结果为：

```
       a  b  c  d
two    2  2  2  2
three  3  3  3  3
```

## `mean`函数

`mean`函数的`axis=0`表示对列进行求取均值，`axis=1`表示对行进行求取均值。

对行取均值：

```python
print(df.mean(axis=1))
```

结果为：

```
one      1.0
two      2.0
three    3.0
dtype: float64
```

对列取均值：

```python
print(df.mean(axis=0))
```

结果为：

```
a    2.0
b    2.0
c    2.0
d    2.0
dtype: float64
```

> - `drop`函数中`axis=0`删除行，`axis=1`删除列。
>
> - `mean`函数的`axis=0`表示对列进行求取均值，`axis=1`表示对行进行求取均值。

## `axis`代表方向！

`axis` 在英文中是轴的意义，二维数据拥有两个轴：第 0 轴（`axis=0`）沿着行的垂直往下，第 1 轴（`axis=1`）沿着列的方向水平延伸。

**坐标轴是有方向的，所以千万不要用行和列的思维去想 axis，因为行和列是没有方向的。**

axis 的重点在于方向，而不是行和列。axis=1 代表跨列（cross columns)，表示横向，方向从左到右；axis=0 代表跨行（cross rows)，表示纵向，方向从上到下。

![图片](index-image/640.jpg)

- `df.mean(axis=1)`代表沿着列水平方向计算均值，从左到右横向求均值。
- `df.drop(name, axis=1)` 代表将`name`对应的列标签（们）沿着水平的方向依次删掉，横向发生变化，体现为列的减少。

!!! note

    - `#!py axis=0`：
    
    如果是单行操作，就指的是某一行；
    
    如果是聚合操作，指的是跨行 cross rows。
    
    - `#!py axis=1`：
    
      如果是单列操作，就指的是某一列；
    
      如果是聚合操作，指的是跨列 cross columns。
