---
title: 利用 VS Code 代码片段提高编码效率
authors: 
  - <PERSON>
date: '2023-02-22'
slug: vscode-snippets
categories:
  - Python
tags:
  - Python
links:
  - "VS Code 代码片段完全入门指南": https://www.freecodecamp.org/chinese/news/definitive-guide-to-snippets-visual-studio-code/
  - "代码片段生成器": https://snippet-generator.app/
---

# VS Code 代码片段

许多常用的代码片段是重复、通用的，将它们记录并整理起来，在需要时可以方便地调用，可以大幅提高编码效率。

VS Code 代码片段功能是一个非常好用的功能，在保存代码片段后，只需输入几个前缀，即可自动生成代码片段。

利用 [snippet-generator](https://snippet-generator.app/) 这个工具自动生成代码片段，可以让整理代码片段的过程更高效！

![vscode-snippets](index-image/vscode-snippets.gif)

<!-- more -->

## 打开 VS Code 的设置

`文件` --> `首选项` --> `配置用户代码片段`

![image-20230221223812484](index-image/vscode-preference.png){width=500px}

## 编辑代码片段文件

选择语言后，即可进入代码片段的文件中进行编辑。

以 `Python` 为例，默认 `python.json` 如下：

```json
{
	// Place your snippets for Python here. Each snippet is defined under a snippet name and has a prefix, body and 
	// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the 
	// same ids are connected.
	// Example:
	// "Print to console": {
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
}
```

要将代码片段写成下面这种格式：

```json
"": {
  "prefix": "",
  "body": [
    ""
  ],
  "description": ""
}
```

我们需要指定 3 个地方：

1. `description`：代码片段的描述；
2. `prefix`：代码片段的诱发前缀（当输入这个前缀时，会被识别出是代码段）；
3. `body`：代码主体。

## 使用 [snippet-generator](https://snippet-generator.app/) 自动生成代码片段

[snippet-generator](https://snippet-generator.app/) 可以帮我们自动生成 VS Code 能够识别的代码片段样式。

例如我们经常需要引入的包：

```python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams["axes.unicode_minus"] = False
```

我们可以将它输入到 [snippet-generator](https://snippet-generator.app/) 中：

![image-20230221225143930](index-image/snippet-generator.png)

将它为我们生成的代码片段复制到 `python.json` 中，就可以使用代码片段了！

## 使用效果

![vscode-snippets](index-image/vscode-snippets.gif)
