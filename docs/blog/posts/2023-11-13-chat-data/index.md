---
title: chat-data 基于大语言模型的数据分析应用
authors: 
  - <PERSON>
date: '2023-11-13'
slug: chat-data
categories:
  - Python
tags:
  - Python
links:
  - "项目代码": https://github.com/jeremy-feng/chat-data/
  - "在线 Demo": https://chat-data.streamlit.app/
  - "PandasAI 文档": https://docs.pandas-ai.com/en/latest/
---

# chat-data: 基于大语言模型的数据分析应用

在数据分析领域，`Pandas` 是最受欢迎的 Python 库之一。然而，许多数据分析任务可以利用人工智能模型进行自动化。`PandasAI` 库为流行的数据分析和处理工具 Pandas 添加了生成式人工智能功能，你可以与 `DataFrame` 进行对话，并得到想要的数据分析结果。

本文使用 `Streamlit` 构建了一个在线应用，用户可以上传 Excel/CSV 数据，在输入想要执行的数据分析任务后，便可得到图表等结果，以及生成该结果的相应代码。

![chat-data](index-image/chat-data.png)

<!-- more -->

## 项目动机

`Pandas` 是 Python 中最流行的数据分析库，但对于非技术人员来说，使用 `Pandas` 进行数据分析可能有些困难。因此，我们希望构建一个工具，帮助用户进行日常的数据分析工作。

在最终的应用中，我们希望它可以：

- 让用户可以上传自己的数据，无论是 Excel 还是 CSV 格式（这两种格式在商业领域是最常见的）。
- 显示数据，以便用户可以思考他们想要对数据进行哪些操作。
- 允许用户输入想要执行的数据分析任务。
- 输出用户想要的结果，包括数字、表格和图表等。
- 显示与任务相对应的 Python 代码，以便用户可以自行执行或修改代码。

## 效果展示

你可以在 [https://chat-data.streamlit.app/](https://chat-data.streamlit.app/) 上体验在线演示。

### 分析数据框

输入问题：

```
What is the total profit for each country? Round the result to two decimals.
```

![chat-data](index-image/chat-data.png)

### 绘制图片

输入问题：

```
Plot a line chart  to show the trend of interest expense, interest income, and net interest income.
```

![image-20231113211112267](index-image/image-20231113211112267.png)

![image-20231113211211294](index-image/image-20231113211211294.png){width=500px}

## 代码

[GitHub 仓库](https://github.com/jeremy-feng/chat-data)上提供了完整的代码。

我们将要构建的是一个使用 Streamlit 构建的交互式数据分析应用程序，它允许用户上传 Excel 或 CSV 文件，然后使用 OpenAI 的语言模型与数据进行交互，并获得针对数据的自然语言响应。

### 导入模块
```python
import os
from dotenv import load_dotenv
import pandas as pd
import streamlit as st
from pandasai import SmartDatalake
from pandasai.llm.openai import OpenAI
from pandasai.middlewares import StreamlitMiddleware
from pandasai.responses.streamlit_response import StreamlitResponse
```
这部分代码导入了所需的 Python 模块。`os` 用于与操作系统交互，`dotenv` 用于加载环境变量，`pandas` 用于数据处理，`streamlit` 用于构建交互式应用，而 `pandasai` 则提供了与数据相关的智能功能。

### 加载环境变量
```python
load_dotenv()
```
这行代码使用 `dotenv` 模块来加载环境变量。

### 设置页面配置
```python
if __name__ == "__main__":
    st.set_page_config(
        layout="wide",
        page_icon="./image/logo.svg",
        page_title="Chat with Excel / CSV Data",
    )
    st.title("Chat with Excel / CSV Data")
```
在这里，使用 `st.set_page_config` 设置了页面的宽度、图标和标题。接着使用 `st.title` 添加了页面的标题。

### 侧边栏设置
```python
with st.sidebar:
    st.header(
        "Set your API Key",
        help="You can get it from [OpenAI](https://platform.openai.com/account/api-keys/), or buy it conveniently from [here](https://api.nextweb.fun/).",
    )
```
这一部分创建了一个侧边栏，允许用户设置他们的 OpenAI API 密钥。同时提供了链接到 OpenAI 官网获取 API 密钥的帮助。

### 主内容区域
```python
with st.container():
    pass
```
这一部分定义了主要的内容区域，其中用户可以上传文件并进行数据分析。

### 文件上传和数据加载
```python
input_files = st.file_uploader(
    "Upload files", type=["xlsx", "csv"], accept_multiple_files=True
)

# 如果用户上传了文件，加载它们
if len(input_files) > 0:
    data_list = []
    for input_file in input_files:
        if input_file.name.lower().endswith(".csv"):
            data = pd.read_csv(input_file)
        else:
            data = pd.read_excel(input_file)
        st.dataframe(data, use_container_width=True)
        data_list.append(data)
# 否则加载默认文件
else:
    st.header("Example Data")
    data = pd.read_excel("./Sample.xlsx")
    st.dataframe(data, use_container_width=True)
    data_list = [data]
```
这部分允许用户上传多个 Excel 或 CSV 文件，并根据文件类型加载数据。如果用户上传了文件，则加载这些文件，否则加载默认的示例文件。

### 创建 SmartDatalake 实例
```python
df = SmartDatalake(
    dfs=data_list,
    config={
        "llm": llm,
        "verbose": True,
        "response_parser": StreamlitResponse,
        "middlewares": [StreamlitMiddleware()],
    },
)
```
这里创建了一个 `SmartDatalake` 实例，用于处理用户上传的数据。`SmartDatalake` 是一个用于数据分析和交互的工具，它接受数据框列表和配置参数，其中包括与 OpenAI 交互所需的参数。

### 输入文本和执行交互
```python
st.header("Ask anything!")
input_text = st.text_area(
    "Enter your question", value="What is the total profit for each country?"
)

if input_text is not None:
    if st.button("Start Execution"):
        result = df.chat(input_text)

        # 显示结果和代码
        col1, col2 = st.columns(2)

        with col1:
            # 显示结果
            st.header("Answer")
            st.write(result)

        with col2:
            # 显示对应的代码
            st.header("The corresponding code")
            st.code(df.last_code_executed, language="python", line_numbers=True)
```
这部分允许用户输入文本并执行交互操作。当用户点击 "Start Execution" 按钮时，应用程序会使用用户提供的文本与数据进行交互，并显示交互结果以及执行的代码。

