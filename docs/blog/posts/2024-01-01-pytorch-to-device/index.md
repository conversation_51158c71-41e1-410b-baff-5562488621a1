---
title: PyTorch 中的数据与模型迁移：理解 .to(device) 的使用
authors: 
  - <PERSON>
date: '2024-01-01'
slug: pytorch-to-device
categories:
  - PyTorch
tags:
  - PyTorch
links:
  - "stackoverflow.com - what-is-the-difference-between-model-todevice-and-model-model-todevice": https://stackoverflow.com/questions/59560043/what-is-the-difference-between-model-todevice-and-model-model-todevice
---

# PyTorch 中的数据与模型迁移：理解 `.to(device)` 的使用

在使用 PyTorch 框架进行深度学习模型训练时，我们经常需要将模型从 CPU 迁移到 GPU 上以加速计算。PyTorch 提供了一个简洁的 API `model.to(device)` 来实现这一过程。但是，在使用这个 API 时，我们可能会遇到两种不同的写法：`model.to(device)` 和 `model = model.to(device)`。那么，这两种写法有什么区别呢？

<!-- more -->

## 张量（Tensors）的`.to(device)`与`XX = XX.to(device)`的区别
当处理 PyTorch 张量时，使用 `to(device)` 方法可以将张量移动到指定的设备（如 GPU）。这个方法会返回张量在新设备上的副本，而原始张量不会改变位置。这意味着，如果我们不将返回值赋值给一个新的变量或者覆盖原始变量，那么原始张量仍然保留在原来的设备上。例如：

```python
# 假设张量 a 在 CPU 上
device = torch.device("cuda:0")
b = a.to(device)
# a 仍然在 CPU 上，b 是 a 在 GPU 上的副本
```

这里，`a` 保持不变，仍然在 CPU 上，而`b`是`a`在 GPU 上的副本。如果我们想要更新`a`到 GPU 上，我们必须显式地赋值：

```python
a = a.to(device)
# 现在 a 在 GPU 上
```

## 模型（Models）的`.to(device)`与`XX = XX.to(device)`的区别
对于模型对象，`to(device)` 方法会将模型及其所有参数移动到指定的设备上。与张量不同，当我们对模型使用 `to(device)` 方法时，模型对象会就地（in-place）更新，即使我们没有将返回值赋给一个新变量或者覆盖原始变量。因此，写法 `model.to(device)` 和 `model = model.to(device)` 在语义上没有区别。例如：

```python
# 假设模型 model 在 CPU 上
device = torch.device("cuda:0")
model.to(device)
# 或者
model = model.to(device)
# model 现在在 GPU 上
```

在这两种情况下，`model` 都会被移动到 GPU 上。

## 总结

综上所述，对于张量，我们必须保存`.to(device)`的返回值，因为它返回的是张量在新设备上的副本。而对于模型对象，`to(device)`的使用会直接更新模型对象本身，因此保存返回值与否并不影响结果。
