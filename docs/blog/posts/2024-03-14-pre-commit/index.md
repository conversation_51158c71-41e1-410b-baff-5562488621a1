---
title: 使用 pre-commit 为 Git 仓库设置自动任务
authors: 
  - <PERSON>
date: '2024-03-18'
slug: pre-commit
categories:
  - Computer Science
tags:
  - Computer Science
links:
  - "How to setup git hooks(pre-commit, commit-msg) in my project?": https://medium.com/@0xmatriksh/how-to-setup-git-hooks-pre-commit-commit-msg-in-my-project-11aaec139536
---

# 使用 pre-commit 为 Git 仓库设置自动任务

 pre-commit 可以在提交代码前自动完成代码格式化、commit message 格式校验等任务。在联网环境下，直接填写 GitHub 中的 pre-commit hook 配置即可。此外，本文还介绍了如何在离线环境下配置 pre-commit。

<!-- more -->

## 联网环境

首先使用命令 `pip install pre-commit` 在项目中安装用于 pre-commit 的包管理器。

然后创建 `.pre-commit-config.yaml` 配置文件，类似于：

```yaml
repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.3.3
    hooks:
      # Run the linter.
      - id: ruff
        types_or: [ python, pyi, jupyter ]
        args: [ --fix ]
      # Run the formatter.
      - id: ruff-format
        types_or: [ python, pyi, jupyter ]
```

最后安装这些 hook：

```bash
pre-commit install
```

它的基本作用是，在项目的 `.git/hooks` 内创建 `pre-commit` 文件（如果已经存在，则进行更新）。因此，每当更改任何配置时都需要运行 `pre-commit install` 。

## 离线环境

上述配置文件中包含 GitHub 仓库链接，也就是会访问 [`.pre-commit-hooks.yaml`](https://github.com/astral-sh/ruff-pre-commit/blob/main/.pre-commit-hooks.yaml)。 

如果环境无法联网，也可以这样配置：

```yaml
repos:
  - repo: local
    hooks:
      - id: ruff
        name: ruff
        entry: ruff check --force-exclude
        language: system
        types: [python]
      - id: ruff-format
        name: ruff-format
        entry: ruff format --force-exclude
        language: system
        types: [python]
      - id: docformatter
        name: docformatter
        entry: docformatter
        language: python
        args: [--in-place]
      - id: commitizen
        name: commitizen-check
        entry: cz check
        language: python
        args: [--allow-abort, --commit-msg-file]
        stages: [commit-msg]
```

最后一个 `commitizen` 比较特别，它是针对 commit message 本身进行校验，你可以用 `pip install commitizen` 安装它，它将确保 commit message 符合[AngularJS commit message format](https://docs.google.com/document/d/1QrDFcIiPjSLDn3EL15IJygNPiHORgU1_OOAqWjiDU5Y/edit#) 规范。

同时还需要执行：

```bash
pre-commit install --hook-type commit-msg
```

才能实现针对 commit message 本身进行校验的功能。

具体操作过程还可以参考：[How to setup git hooks(pre-commit, commit-msg) in my project?](https://medium.com/@0xmatriksh/how-to-setup-git-hooks-pre-commit-commit-msg-in-my-project-11aaec139536)

!!! note "排除检查某些文件"

	例如，要排除检查 site 文件夹，可以在 pre-commit 配置文件中使用 `exclude` 选项来指定正则表达式，以排除特定的目录或文件。以下是一个示例配置：
	
	```yaml hl_lines="9"
	repos:
	  - repo: local
	    hooks:
        - id: docformatter
          name: docformatter
          entry: docformatter
          language: python
          args: [--in-place]
          exclude: ^site/
	```
	
	这个配置使用 `exclude` 选项，并设置为正则表达式 `^site/`，这将排除所有位于 site 文件夹中的文件。


!!! note "language 设置的注意事项"

	如果运行的命令是一个本地的二进制文件，例如 `autocorrect`，则需要把 `language` 设置为 `system`。详见 [https://github.com/huacnlee/autocorrect/issues/23](https://github.com/huacnlee/autocorrect/issues/23) 的讨论。
