---
title: Python 自动合并 PDF 文件
authors: 
  - <PERSON>
date: '2023-02-15'
slug: python-merge-pdf
categories:
  - Python
tags:
  - Python
---

# Python 自动合并 PDF 文件

## 问题与需求

合并 PDF 文件是常用的操作，如果手动合并的话可以使用 Adobe Acrobat 这类专业软件，只要点点鼠标即可完成合并。

如果经常需要合并同样的文件（例如经常更新的中英文简历），可以使用 PyPDF2 包，几行代码即可实现自动合并 PDF 的功能。

## 代码实现


<!-- more -->

```python
from PyPDF2 import PdfMerger

pdfs = ["./文件夹 1/文件 1.pdf", "./文件夹 2/文件 2.pdf"]

merger = PdfMerger()

for pdf in pdfs:
    merger.append(open(pdf, "rb"))

with open("合并后的文件.pdf", "wb") as fout:
    merger.write(fout)
```

## 编写自动化 Batch 脚本

再新建一个 Batch 脚本，只需一行代码，即可实现点一点鼠标就能合并 PDF 文件的功能。

```shell
python merge-pdf.py
```

