---
title: Linux 命令行—重定向
authors:
  - <PERSON>
date: "2022-06-04"
slug: linux-command-line-redirection
categories:
  - Linux
  - Computer Science
tags:
  - Linux
  - Computer Science
toc: yes
description: "在 Linux 中操作文件的基本方法。"
---

# Linux 命令行—重定向

在 Linux 中操作文件的基本方法。

<!-- more -->

## Linux 重定向（Input Output Redirection）

### `>`和`>>`：写入内容

- `cat a.txt > b.txt`：将 a.txt 的内容全部复制到 b.txt 中，且 b.txt 中原来的内容会被删除。

- `cat a.txt >> b.txt`：将 a.txt 的内容全部复制到 b.txt 中，且 b.txt 中原来的内容不会被删除。（相当于 append 操作）
- `echo "some text" >> b.txt`：将"some text"添加到 b.txt 中。与`cat`不同的是，`echo`是手动输入字符串，而`cat`是从 a.txt 中读取字符串。

### `|`：Pipe，将`|`左边的内容作为输入，输出到`|`右边

可以理解为：把一个命令传送到另一个命令的重定向。

### `wc`：统计行数、词数和字符数

- `cat a.txt | wc `：统计 a.txt 的行数、词数和字符数，并打印在命令行中。`wc`代表 Word Count。

- `cat a.txt | wc | cat > a_count.txt`：统计 a.txt 的行数、词数和字符数，并将内容写入到 a_count.txt 中。

### `sort`：按字母表排序

- `sort a.txt`：将 a.txt 的每行内容按字母表顺序打印出来，但不会改变 a.txt 文件的内容。

- `cat a.txt | sort > sorted-a.txt`：将 a.txt 的每行内容按字母表顺序写入到 sorted-a.txt 中。

### `uniq`：删除重复行

- `uniq a.txt`：如果两个**相邻的行**是重复的，则只保留一行。注意，如果 a.txt 是：

```
abc
xyz
abc
```

那么，第一行和最后一行的`abc`都会被保留，因为它们并不是相邻的。

为了解决这一问题，可以先`sort`，再`uniq`。

- 例如：`sort a.txt | uniq > uniq-a.txt`可以将 a.txt 按字母表排序后（保证相同的内容是相邻的），再删除重复行，最终得到唯一的行。

### `grep`：Global Regular Expression Print，按照正则表达式进行输出

- `grep America continents.txt`：只打印出 continents.txt 中包含 America 的行，注意这个命令对大小写敏感，即大小写不能混用。

- `grep -i America continents.txt`：功能同上，但对大小写不敏感。`i`代表 Insensitive。

- `grep -R Arctic /home/<USER>/workspace/geography`：在 geography 文件夹中遍历各个文件，搜索其中是否包含 Arctic。若包含，则输出文件名，以及包含 Arctic 的那一行。`R`代表 Recursive。

- `grep -Rl Arctic /home/<USER>/workspace/geography`：在上一行的`R`后面加了`l`（`L`的小写，而不是`i`的大写），它只输出文件名，不输出包含 Arctic 的那一行。

### `sed`：Stream Editor，查找和替换

- `sed 's/snow/rain/' forests.txt`：把 forests.txt 中**每一行的第一个**snow 替换成 rain。

  - 其中，`s`代表 Substitution，`snow`是待查找的字符串，`rain`是待替换的字符串。

- `sed 's/snow/rain/g' forests.txt`：把 forests.txt 中**所有的**snow 替换成 rain。`g`代表 Global。

上面的命令只是将查找替换的结果输出到命令行，并没有修改原文件。要想修改原文件，需要加上`-i`。

- `sed -i 's/snow/rain/g' forests.txt`
