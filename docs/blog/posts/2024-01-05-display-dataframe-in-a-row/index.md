---
title: Jupyter Notebook 并排显示多个 Pandas DataFrame
authors: 
  - <PERSON>
date: '2024-01-05'
slug: display-dataframe-in-a-row
categories:
  - Python
tags:
  - Python
---

# Jupyter Notebook 并排显示多个 Pandas DataFrame

在 Jupyter Notebook 中，可以使用 HTML 功能来并排显示两个 Pandas DataFrame，让我们更方便地查看和对比多个表格。本文提供了一个简单的例子，展示了如何做到这一点。

![image-20240105111806034](index-image/image-20240105111806034.png)

<!-- more -->

## 代码示例

首先，确保你已经导入了必要的库：

```python
import pandas as pd
from IPython.display import display_html
```

然后，可以定义一个函数来并排显示两个 DataFrames：

```python
def display_side_by_side(*args):
    html_str = ""
    for df in args:
        html_str += df.to_html()
    display_html(
        html_str.replace("table", 'table style="display:inline;margin-right:20px;"'),
        raw=True,
    )
```

现在，创建两个 DataFrame 作为示例：

```python
df1 = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6]})
df2 = pd.DataFrame({"C": [7, 8, 9], "D": [10, 11, 12]})
```

最后，使用之前定义的函数来并排显示它们：

```python
display_side_by_side(df1, df2)
```

这将在 Jupyter Notebook 中并排显示`df1`和`df2`。

![image-20240105111607312](index-image/image-20240105111607312.png)

你可以通过调整`style="display:inline;margin-right:20px;"`中的 CSS 样式来修改表格的外观，例如改变间距、对齐方式等。

## 居中并排显示

要使整体的 HTML 内容居中显示，您可以通过定义一个包含居中样式的外部容器来实现。以下是修改后的代码：

```python
import pandas as pd
from IPython.display import display_html


def display_side_by_side(*args):
    html_str = '<div style="text-align:center;">'
    for df in args:
        html_str += df.to_html().replace(
            "table", 'table style="display:inline;margin-right:20px;"'
        )
    html_str += "</div>"
    display_html(html_str, raw=True)


# 创建两个示例 DataFrame
df1 = pd.DataFrame({"A": [1, 2, 3], "B": [4, 5, 6]})
df2 = pd.DataFrame({"C": [7, 8, 9], "D": [10, 11, 12]})

# 并排显示 DataFrames
display_side_by_side(df1, df2)
```

这段代码将为两个并排显示的 DataFrame 创建一个外部的`<div>`元素，并设置该元素的`text-align`属性为`center`。这样，当显示在 Jupyter Notebook 中时，整个 HTML 内容（包括两个 DataFrame）会居中显示。注意，每个 DataFrame 仍然是内联显示的，并且它们之间有一定的右侧边距以分隔它们。

![image-20240105111806034](index-image/image-20240105111806034.png)

请注意，根据 Jupyter Notebook 的样式和所使用的浏览器，居中显示效果可能会有所不同。如果需要进一步调整样式，可以在`style`属性中添加额外的 CSS 规则。
