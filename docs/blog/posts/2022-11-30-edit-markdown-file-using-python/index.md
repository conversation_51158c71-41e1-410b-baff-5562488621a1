---
title: Python 批量修改 Markdown 文件
authors: 
  - <PERSON>
date: '2022-11-30'
slug: edit-markdown-file-using-python
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 逐个修改文件是一件很痛苦的事情，使用 Python 结合正则表达式可以轻松且快速实现批量修改文件的功能。
---

# Python 批量修改 Markdown 文件

逐个修改文件是一件很痛苦的事情，使用 Python 结合正则表达式可以轻松且快速实现批量修改文件的功能。

<!-- more -->

## 需求

对于所有博文，若其 Metadata 中的`tags`包含“技术”，则将“技术”删除，它和`categories`时常有重复，因而是不必要的。

![image-20221129215008912](index-image/image-20221129215008912.png)

## Python 实现

```python
import os
import re

path = "./post/"

for root, dirs, files in os.walk(path, topdown=False):
    for name in files:
        # 对于 Markdown 文件进行处理
        if name.endswith(".md") or name.endswith(".markdown"):
            with open(os.path.join(root, name), "r+", encoding="UTF-8") as f:
                # 读取文件内容
                content = f.read()
                # 替换内容
                content = re.sub(r"\n  - 技术", r"", content)
                # 将指针移动到文件开头
                f.seek(0)
                # 清空文件
                f.truncate()
                # 将修改后的内容写入文件
                f.write(content)
                # 关闭文件
                f.close()
```

代码中替换文本时用到了正则表达式，它寻找满足`\n  - 技术`模式的字符串，并替换为空值，这样就能删除`tags`中的“技术”这一行。

修改前的文本：

![image-20221129215426592](index-image/image-20221129215426592.png)

修改后的文本：

![image-20221129220838742](index-image/image-20221129220838742.png)
