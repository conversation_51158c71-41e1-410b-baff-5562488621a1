---
title: 在 Python 中调用在线大模型 API
authors: 
  - <PERSON>
date: '2023-11-01'
slug: call-llm-api-in-python
categories:
  - Python
tags:
  - Python
links:
  - "OpenAI API": https://api.nextweb.fun/
  - "讯飞星火 API": https://console.xfyun.cn/services/cbm
  - "智谱 AI API": https://open.bigmodel.cn/usercenter/apikeys
  - "百度 API": https://console.bce.baidu.com/qianfan/ais/console/applicationConsole/application
  - "阿里通义千问 API": https://dashscope.console.aliyun.com/apiKey
---

# 在 Python 中调用在线大模型 API

许多大模型都提供了在线 API 接口服务，我们可以在 Python 中调用它们。本文使用 `openai`、`chatllm`、`dashscope` 等包实现了对 OpenAI、讯飞星火、智谱 AI、百度、阿里通义千问的调用。用户只需在本地用 `.env` 文件存储 API Key 即可快速调用这些大模型。

![image-20231031211003404](index-image/image-20231031211003404.png){width=500px}

<!-- more -->

## 导入必要的包


```python
import os

import dashscope
import openai
from chatllm.llmchain.llms import ChatGLM, ErnieBot, SparkBot
from dashscope import Generation
from dotenv import load_dotenv
from langchain.chains import LLMChain
from langchain.prompts import ChatPromptTemplate
```

## 配置模型 API Key

我们一共使用 5 个在线大模型 API，分别是：

- OpenAI API，详见 [https://api.nextweb.fun/](https://api.nextweb.fun/) 和 [https://flowus.cn/yifei/share/7c1ff13b-277d-40da-8c04-ebf770ea46ea](https://flowus.cn/yifei/share/7c1ff13b-277d-40da-8c04-ebf770ea46ea)
- 讯飞星火 API，详见 [https://console.xfyun.cn/services/cbm](https://console.xfyun.cn/services/cbm)
- 智谱 AI API，详见 [https://open.bigmodel.cn/usercenter/apikeys](https://open.bigmodel.cn/usercenter/apikeys)
- 百度 API，详见 [https://console.bce.baidu.com/qianfan/ais/console/applicationConsole/application](https://console.bce.baidu.com/qianfan/ais/console/applicationConsole/application)
- 阿里通义千问 API，详见 [https://dashscope.console.aliyun.com/apiKey](https://dashscope.console.aliyun.com/apiKey)

你可以在上述网址中注册账号并申请 API Key，大部分模型对于新用户都有一定的免费额度，少部分可能需要预付费。

为了避免将 API Key 暴露在公开的代码中，我们将 API Key 保存在 `.env` 文件中，然后通过 `dotenv` 包的 `load_dotenv()` 方法读取到环境变量中。

在当前目录下新建一个 `.env` 文件，层级结构如下：

```
.
├── .env
└── call-api.ipynb
```

`.env` 文件的内容如下：

```
OPENAI_API_BASE=https://api.nextweb.fun/openai/v1
OPENAI_API_KEY=ak-XXXXXX
SPARK_API_KEY={APP Id}:{API Key}:{Secret Key}
CHATGLM_API_KEY={API Key}
ERNIE_API_KEY={API Key}:{Secret Key}
DASHSCOPE_API_KEY=sk-XXXXXX
```


```python
# 通过 `dotenv` 包的 `load_dotenv()` 方法读取到环境变量中
load_dotenv()

# 下面的三个变量较为特殊，我们需要手动设置，否则可能会出现错误
openai.api_base = os.environ["OPENAI_API_BASE"]
openai.api_key = os.environ["OPENAI_API_KEY"]
dashscope.api_key = os.environ["DASHSCOPE_API_KEY"]
```


    True

## 设置各个模型的对话机器人实例


```python
# 设置默认提示词
prompt = ChatPromptTemplate.from_template("{question}")

# 设置各个模型的对话机器人实例
llm_spark = SparkBot()
llm_zhipuai = ChatGLM()
llm_ernie = ErnieBot()

chat_spark = LLMChain(llm=llm_spark, prompt=prompt)
chat_zhipuai = LLMChain(llm=llm_zhipuai, prompt=prompt)
chat_ernie = LLMChain(llm=llm_ernie, prompt=prompt)
```

## 测试对话效果


```python
prompt = "你是谁？"
```

### OpenAI


```python
openai.ChatCompletion.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": prompt}],
).choices[0].message.content
```


    '我是一个AI助手，没有实体身份。我可以回答一些问题和提供一些建议。'

### 讯飞星火


```python
chat_spark.run(prompt)
```


    '您好，我是科大讯飞研发的认知智能大模型，我的名字叫讯飞星火认知大模型。我可以和人类进行自然交流，解答问题，高效完成各领域认知智能需求。'

### 智谱 AI


```python
chat_zhipuai.run(prompt)[1:-1].strip()
```


    '我是一个名为 ChatGLM2-12B 的人工智能助手，是基于清华大学 KEG 实验室和智谱 AI 公司于 2023 年共同训练的语言模型开发的。我的任务是针对用户的问题和要求提供适当的答复和支持。'

### 百度


```python
chat_ernie.run(prompt)
```


    '您好，我是百度研发的知识增强大语言模型，中文名是文心一言，英文名是ERNIE Bot。我能够与人对话互动，回答问题，协助创作，高效便捷地帮助人们获取信息、知识和灵感。\n\n如果您有任何问题，请随时告诉我。'

### 阿里通义千问


```python
Generation.call(model="qwen-turbo", prompt=prompt).output.text
```


    '我是来自阿里云的大规模语言模型，我叫通义千问。'
