---
title: 为静态网页生成文件目录
authors: 
  - <PERSON>
date: '2022-12-13'
slug: generate-file-index
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 静态网页就相当于一个文件夹，为文件夹中的文件生成目录，可以方便地查找文件。
---

# 为静态网页生成文件目录

静态网页就相当于一个文件夹，为文件夹中的文件生成目录，可以方便地查找文件。

<!-- more -->

## 代码

```python
# 导入模块
import os
```

```python
# 获取当前目录下所有文件的列表
files = os.listdir(r"./directory/")
```

```python
# 打开一个新文件，用于写入 HTML 目录
with open("./directory/index.html", "w", encoding="utf-8") as f:
    f.write("<html><body><ul>\n")
    # 遍历文件列表，写入 HTML 格式的目录
    for file in files:
        # 写入文件名，并添加超链接
        f.write('<li><a href="{}">{}</a></li>\n'.format(file, file))
    f.write("</ul></body></html>\n")
```

## 在 VS Code 中运行 Python，需要修改路径

### 修改 VS Code 运行 Python 的路径为当前脚本所在路径

```python
import sys

# 切换到当前目录，否则会报路径错误（VSCode 的问题）
os.chdir(sys.path[0])
```

## 编写脚本执行文件

新建文本文件，写入

```
python python脚本文件名.py
```

再修改后缀名为`.bat`，双击即可运行 Python 程序。

## 实现效果

![image-20221213193819497](index-image/image-20221213193819497.png)

![image-20221213193800253](index-image/image-20221213193800253.png)
