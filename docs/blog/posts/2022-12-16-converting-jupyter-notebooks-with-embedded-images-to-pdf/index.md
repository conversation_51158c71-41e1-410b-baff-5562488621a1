---
title: 在 Jupyter Notebook 中插入本地图片并导出带有图片的 PDF 文件
authors: 
  - <PERSON> Feng
date: '2022-12-16'
slug: converting-jupyter-notebooks-with-embedded-images-to-pdf
categories:
  - Python
tags:
  - Python
toc: yes
katex: yes
description: 将 Notebook 转为 PDF 时通常都嵌有本地图片，本文转载了一个可以成功将图片嵌入 PDF 的方法。
---

# 在 Jupyter Notebook 中插入本地图片并导出带有图片的 PDF 文件

将 Notebook 转为 PDF 时通常都嵌有本地图片，本文转载了一个可以成功将图片嵌入 PDF 的方法。

<!-- more -->

在 VS Code 中的 Notebook 直接插入图片路径，在转为 PDF 时通常会导致不显示图片。

下面这种方法需要在浏览器中拖动图片，虽然繁琐了一些，但可以成功将图片嵌入 PDF。

原文链接：[https://mpievolbio-scicomp.pages.gwdg.de/blog/post/2020-12-09_pandoc_vs_nbconvert](https://mpievolbio-scicomp.pages.gwdg.de/blog/post/2020-12-09_pandoc_vs_nbconvert/)

