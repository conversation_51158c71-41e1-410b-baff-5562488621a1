---
title: 量化投资策略设计与分析 - 多策略
authors:
  - <PERSON> Feng
date: "2023-03-25"
slug: quantitative-investment-lecture-multiple-strategies
categories:
  - 量化研究
tags:
  - 量化研究
---

# 量化投资策略设计与分析 - 多策略

本文是 2023 年 3 月 25 日的量化投资策略设计与分析的课程笔记，本节课介绍了多策略组合。

![image-20230325110239760](index-image/image-20230325110239760.png)

<!-- more -->

假设有以下多个策略，时间跨度是从 1998 年到 2013 年。其中没有明星策略，有些策略甚至有巨大回撤。

![image-20230325104218396](index-image/image-20230325104218396.png)

## 等权重组合

做为组合研究的基准，以下是等权重组合的累积收益率曲线。

![image-20230325104307148](index-image/image-20230325104307148.png)

## 根据近期回报率调整策略权重 

下图是指每个月调整一次权重，最近 6 个月的平均回报高的占有更大权重。

![image-20230325104401296](index-image/image-20230325104401296.png)

通过定期调整策略权重的方法，我们获得了比等权重更高的回报率。(其实作弊了)

有两个参数需要研究：

1. 调整权重需要用到近期平均回报率，这个近期是指多长时间，即回溯样本长度是多少？
2. 每隔多长时间调整一次权重？

### 不同回溯样本长度

下图画出不同回溯样本长度的情况下，每月进行权重调整的平均月度回报比较，基准仍然是等权重组合。

![image-20230325104524480](index-image/image-20230325104524480.png)

### 不同调整频率

只要我们调整权重的频率不要太低（超过 6 个月），我们一般能获得比等权重更高的回报率。（回溯样本为 3 个月）

![image-20230325104624367](index-image/image-20230325104624367.png)

经过对回溯样本长度和调整频率的研究，我们意识到：

1. 这种依据近期回报率调整权重的方法，一般可以获得比等权重更高的回报率；
2. 但是信息比率反而不如等权重。

可能有以下原因：

1. 这种只以回报率为调整标准的方法，完全忽略了策略的波动性和相关性。
2. 近期表现较好的策略往往相关性较高，容易导致过高配置相关性很高的策略。

## 根据近期信息比率调整策略权重

下图是指每个月调整一次权重，最近 6 个月的信息比率高的占有更大权重。

![image-20230325104824038](index-image/image-20230325104824038.png)

### 不同回溯样本长度

下图画出不同回溯样本长度的情况下，每月进行权重调整的平均月度回报比较，基准仍然是等权重组合。

![image-20230325104908623](index-image/image-20230325104908623.png)

信息比率还是不如等权重组合。

!!! note "反直觉的现象"

	我们以最近一段时间的信息比率为权重依据，但得到的投资组合的信息比率反而害不如等权组合的信息比率。

![image-20230325104932884](index-image/image-20230325104932884.png)

### 不同调整频率

不同调整频率下，我们一般能获得比等权重更高的回报率。

![image-20230325105127940](index-image/image-20230325105127940.png)

调整频率过低下，会造成信息比率下降。

![image-20230325105305158](index-image/image-20230325105305158.png)

## 均值 - 方差模型

![image-20230325105342817](index-image/image-20230325105342817.png)

样本内检验最大信息比率权重，所得到的业绩全面优于等权重组合。

![image-20230325105422492](index-image/image-20230325105422492.png)

样本内最大信息比率组合收益率曲线较为平滑。

![image-20230325110401723](index-image/image-20230325110401723.png)

### 不同回溯样本长度

在各个回测样本长度的情况下，回报率和信息比率大体上都优于等权重组合。

![image-20230325105505612](index-image/image-20230325105505612.png)

![image-20230325105520247](index-image/image-20230325105520247.png)

### 不同调整频率

不同调整频率下，我们一般能获得比等权重更高的回报率。

![image-20230325105617574](index-image/image-20230325105617574.png)

![image-20230325105646536](index-image/image-20230325105646536.png)

## 总结

利用近期回报数据计算最大化信息比率权重的方法，

1. 一般可以获得比等权重更高的回报率；
2. 一般可以获得比等权重更高的信息比率；
3. 调整频率不能太低。

其优越性主要体现在，这种方法考虑了多策略的各种性质：

1. 回报率
2. 波动率
3. 相关性

### 各类方法比较

![image-20230325105859181](index-image/image-20230325105859181.png)

1. 依据近期回报率/信息比率调整权重的方法能取得较大的回报率；
2. 但是经常在相同类型策略上放入太高的比重，造成波动率上升，信息比率下降；
3. 利用近期回报数据计算最大化信息比率权重的方法兼顾回报率，波动率和相关性，是可以重点考虑的方法。

需要考虑的问题：

1. 过往回报是否预测未来回报？
2. 过往波动率是否预测未来波动率？ 
3. 过往相关性是否预测未来相关性？

