---
title: Python 滚动回归
authors: 
  - <PERSON>
date: '2023-04-24'
slug: rolling-ols
categories:
  - Python
  - 量化研究
tags:
  - Python
  - 量化研究
links:
  - "statsmodels: Rolling Regression": https://www.statsmodels.org/dev/examples/notebooks/generated/rolling_ls.html
  - "statsmodels: RollingRegressionResults": https://www.statsmodels.org/dev/generated/statsmodels.regression.rolling.RollingRegressionResults.html#statsmodels.regression.rolling.RollingRegressionResults
---

# Python 滚动回归

本文实现了多个资产分别在时间序列上进行滚动回归，并返回由最新系数计算得到的残差，最后将多个资产的残差结果重新聚合为多重索引的数据框。

![image-20230424173801905](index-image/image-20230424173801905.png)

<!-- more -->

## 代码示例

### 导入包

```python
import pandas as pd
from statsmodels.regression.rolling import RollingOLS
from statsmodels.tools import add_constant
from tqdm import tqdm

# 在 apply 时展示进度条
tqdm.pandas()
```

### 查看原始数据

```python
data = pd.read_pickle("./demo.pkl")
# 取 3 只股票，减少计算时间
data = data.loc[(slice(None), ["000001.SZ", "000002.SZ", "000008.SZ"]), :]
```

![image-20230424172810652](index-image/image-20230424172810652.png)

### 滚动回归的核心函数

```python
def rolling_ols(x, y, window=120, _add_constant=True):
    try:
        # 为自变量添加截距项
        if _add_constant:
            x = add_constant(x)
        # 构建滚动回归模型
        model = RollingOLS(endog=y, exog=x, window=window)
        # 估计滚动回归的参数，包括截距项和各特征的回归系数
        params = model.fit().params
        # 根据估计出的参数，计算估计出的因变量值
        y_hat = (params * x).sum(axis=1)
        # 计算残差
        residual = y - y_hat
        return pd.DataFrame({"residual": residual})
    # 如果报错，则可能是样本量比 window 小，无法进行回归
    except:
        return None
```

### `groupby`后`apply`

使用 `progress_apply` 可以展示进度条。

```python
residual = data.groupby("instrument", group_keys=False).progress_apply(
    lambda group: rolling_ols(
        x=group.iloc[:, :-1],
        y=group.iloc[:, -1],
        window=120,
        _add_constant=True,
    )
)
```

![image-20230424173754050](index-image/image-20230424173754050.png)

### 查看结果

![image-20230424173801905](index-image/image-20230424173801905.png)
