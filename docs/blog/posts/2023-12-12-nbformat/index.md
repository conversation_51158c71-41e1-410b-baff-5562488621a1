---
title: 批量修改 Jupyter Notebook 的内容
authors: 
  - <PERSON>
date: '2023-12-12'
slug: nbformat
categories:
  - Python
tags:
  - Python
---

# 批量修改 Jupyter Notebook 的内容

有时需要批量替换许多 Jupyter Notebook 的内容，由于 Jupyter Notebook 并不是简单的文本文件，在读取和替换时并不像批量修改 Python 脚本那样方便。

本文介绍了使用 `nbformat` 批量修改 Jupyter Notebook 的内容的方法。

![20231207155702](index-image/20231207155702.gif)


<!-- more -->

## 待修改的文件

假设我们有一个 Jupyter Notebook，它的一个单元格的内容是：

```python
a = 1
```

我们需要把所有的 `a = 1` 的代码替换为：

```python
a = 1
b = 2
```

## 批量修改 Jupyter Notebook 的内容的代码

我们可以使用下面的代码，批量修改 Jupyter Notebook 的内容：

```python
# 导入 glob 模块用于文件路径匹配
import glob

# 导入 nbformat 模块用于读取和写入 Jupyter Notebook 文件
import nbformat

# 定义要被替换的旧代码
old_code = """a = 1"""

# 定义新的代码，将替换旧代码
new_code = """a = 1
b = 2"""

# 遍历当前目录及其所有子目录下的所有.ipynb 文件
for filename in glob.iglob("./**/*.ipynb", recursive=True):
    # 排除当前正在运行的脚本文件
    if filename != ".\\edit-notebook-cell.ipynb":  # (1)
        # 打开文件并读取其内容
        with open(filename, "r", encoding="utf-8") as f:
            nb = nbformat.read(f, as_version=4)

        # 遍历 notebook 中的所有代码单元格
        for cell in nb.cells:
            # 如果代码单元格中包含"source"属性，且其源代码中包含旧代码
            if "source" in cell and old_code in cell.source:
                # 将旧代码替换为新代码
                cell.source = cell.source.replace(old_code, new_code)

        # 打开文件并写入新的内容
        with open(filename, "w", encoding="utf-8") as f:
            nbformat.write(nb, f)
```

1. 当前脚本文件的文件名是 `edit-notebook-cell.ipynb`，因此需要排除。

![20231207155702](index-image/20231207155702.gif)
