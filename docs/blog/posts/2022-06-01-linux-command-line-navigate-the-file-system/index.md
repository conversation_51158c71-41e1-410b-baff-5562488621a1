---
title: Linux 命令行—文件操作
authors: 
  - <PERSON>
date: '2022-06-01'
slug: Linux-Command-Line-Navigate-the-File-System
categories:
- Computer Science
- Linux
tags:
- Computer Science
- Linux
toc: true
description: "在 Linux 中常用的操作文件的命令。"
---

# Linux 命令行—文件操作

在 Linux 中常用的操作文件的命令。

Linux 和 Mac OS 自带的 Bash 中可以使用 Linux 命令。Windows 系统自带的 Command Prompt（命令提示符）并不是最常用的，很多 Linux 命令在命令提示符中无法使用，它们有一些等价的对应关系。

![Linux 和 Windows Command Prompt](images/image-20220601211010335.png)

Windows 的 Powershell 可以用一些 Linux 命令。

<!-- more -->

## 命令行基础

- 在命令行中，文件夹叫做 Directory。
- 在命令行中，每一行的开头是一个美元符号`$`，这是 Shell Prompt，即命令提示符。

## `ls`：列示文件和文件夹

- `ls`：列出目前所在的文件夹中包含的子文件夹和文件的名称。
- `ls -a`：a 代表 all。列出所有内容，包括那些隐藏的文件和文件夹。隐藏的文件和文件夹通常是以`.`开头，比如`.git`。
- `ls -l`：l 代表 long，意味着详细信息。列出文件和文件夹（不包含隐藏的），并且可以显示修改时间和文件权限。
- `ls -t`：t 代表 time。按照修改时间倒序来展示文件和文件夹。
- `ls -alt`：同时实现上述三个命令的功能。

## `pwd`：打印当前工作路径

- `pwd`：Print Working Directory，打印当前的工作路径。注意`pwd`并不是 Password 的意思。

## `cd`：改变工作路径

- `cd`：Change Directory，改变当前的工作路径。后面可以接收一个参数，则可以移动到这个参数代表的路径。例如：
  - `cd folder_a`会进入到子文件夹`folder_a`中。
  - `cd ..`会返回上一级。

## `mkdir`：新建文件夹

- `mkdir`：Make Directory，新建文件夹。
  - `mkdir folder_b`会在当前文件夹中新建一个叫做`folder_b`的文件夹。
  - 如果希望在`folder_b`中继续新建子文件夹`folder_c`：
    - 一个笨拙的办法是先`cd`进入`folder_b`，再`mkdir folder_c`。
    - 也可以直接用`cd mkdir folder_b/folder_c`。**注意**：<u>是用斜杠`/`，而不是反斜杠`\`</u>。如果用反斜杠，即`cd mkdir folder_b\folder_c`，则会生成一个叫做`folder_bfolder_c`的文件夹，并且这个文件夹和`folder_b`是并列的。

## `chmod`：修改权限

- `r`：read，二进制为`100`，即十进制的 4。

- `w`：write，二进制为`010`，即十进制的 2。

- `x`：execute，二进制为`001`，即十进制的 1。

```bash
chmod 775 file
```

  修改文件权限为：

  - 拥有者可读、写、执行；
  - 组用户可读、写、执行；
  - 其他用户可读、执行。

  即文件权限变成`-rwxrwxr-x`。

```bash
chmod -R 775 file_folder
```

  注意`-R`是大写！

  修改文件夹中的所有文件权限为：

  - 拥有者可读、写、执行；
  - 组用户可读、写、执行；
  - 其他用户可读、执行。

  即文件夹中的所有权限都变成`-rwxrwxr-x`。

## `touch`：新建文件

- `touch`：新建文件。后面可以接收一个参数作为文件名。如`touch example.txt`会生成一个空的文本文件。

## `clear`：清空历史命令

- `clear`：清空历史命令，它不会执行任何操作，只会让屏幕上的历史命令清空。

## `echo`：输出字符串

- `echo`：输出指定的字符串或者变量。`echo "Hello Command Line" >> hello_cli.txt`会新建一个文件，并写入“Hello Command Line”。

## `cat`：打印文件内容

- `cat`：查看文件的内容。`cat hello_cli.txt`会打印出文本文件中的内容。

## `cp`：复制文件

- `cp`：复制文件。一般有两个参数，`cp source destination`可以将 source 复制到 destination。
- `cp`也可以同时复制多个文件，只需要在后面跟多个参数即可。在这种情况下，最后一个参数是目标路径，前面的所有参数都是待复制的文件。例如，`cp a b c d/`会将 a、b、c 都复制到文件夹 d 中。
- `cp`后面可用**通配符**（Wildcard）。例如，`cp * test/`会将本工作目录下的所有文件都复制到子文件夹 test 中；`cp a*.txt test/`会将本工作目录下的所有以 a 开头的 txt 格式的文件都复制到子文件夹 test 中。

## `mv`：移动文件

- `mv`：移动文件，可以理解为剪切。使用方法和`cp`类似。
- `mv`也可以用于重命名文件，例如`mv a.txt b.txt`只是将 a.txt 重命名为 b.txt。

## `rm`：删除文件

- `rm`：删除文件。<u>**谨慎使用这个命令！**</u>如果删除了文件，没有回收站可以找回。

- `rm`不能直接删除一个文件夹。例如：`rm folder`会报错：

  ![rm 删除文件夹报错](images/image-20220602103541090.png)

- `rm -r`可以删除文件夹和文件夹中的所有内容，也就是删除整个文件夹。`-r`代表 recursive。

## `du -h` 查看文件占用磁盘大小

```bash
du -h 文件路径
```

## `du -sh` 查看文件夹占用磁盘大小

```bash
du -sh 文件夹路径
```

## 压缩文件夹

```bash
tar czvf 压缩后的文件名.tar.gz 被压缩的文件夹路径
```

## 解压文件夹

```bash
tar zvxf 压缩文件名.tar.gz -C 目标文件夹
```

- c  压缩
- x  解压
- z  支持 gzip 解压文件
- v  显示操作过程
- f  使用档名，请留意，在 f 之后要立即接档名！不要再加参数！

## 将可执行文件添加到 PATH 环境变量

转载自 [joshua317 博客](https://www.joshua317.com/article/35)

Linux 命令行下面执行某个命令的时候，首先保证该命令是否存在，若存在，但输入命令的时候若仍提示：`command not found`。

这个时候就的查看 PATH 环境变量的设置了，当前命令是否存在于 PATH 环境变量中

```bash
#查看PATH：
echo $PATH
```

举例说，命令 composr 在/usr/loca/bin

但执行的时候提示：

```bash
composr: command not found
```

这个时候，通过`echo $PATH`，发现 composer 并未在 PATH 环境变量中有设置，这个时候就需要把 composer 所在路径添加到 PATH 中

所以需要修改 PATH 环境变量，具体如下：

### 方法一

```bash
export PATH=/usr/local/bin:$PATH
#配置完后可以通过echo $PATH查看配置结果。
#生效方法：立即生效
#有效期限：临时改变，只能在当前的终端窗口中有效，当前窗口关闭后就会恢#复原有的path配置
#用户局限：仅对当前用户
```

### 方法二

```bash
#通过修改.bashrc文件:
vim ~/.bashrc 
#在最后一行添上：
export PATH=/usr/local/bin:$PATH
#生效方法：（有以下两种）
#1、关闭当前终端窗口，重新打开一个新终端窗口就能生效
#2、输入“source ~/.bashrc”命令，立即生效
#有效期限：永久有效
#用户局限：仅对当前用户
```

### 方法三

```bash
#通过修改profile文件:
vim /etc/profile
export PATH=/usr/local/bin:$PATH
#生效方法：系统重启
#有效期限：永久有效
#用户局限：对所有用户
```

### 方法四

```bash
#通过修改environment文件:
vim /etc/environment
在PATH="/usr/local/sbin:/usr/sbin:/usr/bin:/sbin:/bin"中加入 
":/usr/local/bin"
#生效方法：系统重启
#有效期限：永久有效
#用户局限：对所有用户
```
