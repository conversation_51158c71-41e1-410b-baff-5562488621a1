---
title: Python 中的 set 和 list 转换时的随机性
authors: 
  - <PERSON>
date: '2023-12-14'
slug: randomness-when-convert-set-to-list
categories:
  - Python
tags:
  - Python
links:
  - "stackoverflow - Random seed for order of elements in Python's Set to List conversion": https://stackoverflow.com/questions/60386708/random-seed-for-order-of-elements-in-pythons-set-to-list-conversion
---

# Python 中的 set 和 list 转换时的随机性

在 Python 中，set 和 list 是两种不同的数据结构，它们在使用和功能上有很大的不同。set 是一个无序的、不重复的元素集合，而 list 是一个有序的、可重复的元素集合。

然而，从 set 到 list 的过程并不是一个简单的转换，因为 set 本身就是无序的，所以从 set 到 list 的过程并没有固定的顺序。这意味着每次转换得到的 list 的元素顺序可能都会不同。

在一些项目中，我们希望结果可重现，因此需要确保每一步的结果都没有随机性。许多随机性可以通过随机种子来控制，但从 set 到 list 的过程并不会被随机种子控制住，因此仍然存在随机性。

本文探讨了从 set 到 list 的过程中的随机性，亦作为排查随机性来源的一次记录。

<!-- more -->

## set 到 list 的代码

```python
list({"foo", "bar", "baz", "qux"})
```

## 第一次运行的结果

![image-20231214123237343](index-image/image-20231214123237343.png){width=500px}

在 Jupyter Notebook 没有被重启前，得到的结果是一样的，因此看起来从 set 到 list 的过程没有随机性。

## 重启 Kernel 再次运行的结果

![image-20231214123842344](index-image/image-20231214123842344.png){width=500px}

可以看到，同样的代码，在重启 Kernel 再次运行时会得到不一样的结果。

## 解决方案

如果想固定得到的 list 的顺序，可以考虑把得到的 list 进行排序：

```python
sorted(list({"foo", "bar", "baz", "qux"}))
```

