---
title: 在 pandas 中计算方差
authors: 
  - <PERSON>
date: '2022-12-16'
slug: calculate-variance-in-pandas
categories:
  - 统计
  - Python
tags:
  - 统计
  - Python
toc: yes
katex: yes
description: pandas 默认的`.var()`方法计算的是样本方差，即自由度为$N-1$。若想计算总体方差，需指定参数`ddof=0`。
---

# 在 pandas 中计算方差

pandas 默认的`.var()`方法计算的是样本方差，即自由度为$N-1$。若想计算总体方差，需指定参数`ddof=0`(1)。
{ .annotate }

1. Delta Degrees of Freedom。当指定`ddof`时，计算方差的分母为`N-ddof`。

!!! note "总结"

    - pandas 中的`#!py var()`默认的自由度是 n-1，即`#!py var(ddof=1)`；
    - NumPy 中的`#!py var()`默认的自由度是 n，即`#!py var(ddof=0)`；
    - pandas 中的`#!py var(ddof=0)`相当于 NumPy 中的 `#!py var()`。

<!-- more -->

在验算统计调查与数据采集课程讲义的计算结果时，我对比了`pandas`计算方差的结果和自己手动实现计算方差的结果，发现`pandas`默认的`.var()`方法计算的是样本方差，即自由度为$N-1$。若想计算总体方差，需指定参数`ddof=0`。

```python
import pandas as pd
from IPython.display import Markdown as md
```

## 设定参数


```python
N = 36
n = 6
```

## 读取数据


```python
df1 = pd.read_excel("例 1-数据.xlsx", index_col=0)
```


```python
df1
```


| 单位号 |   Xi |   Yi |
| ------ | ---- | ---- |
|      1 |  598 |  633 |
|      2 |   21 |   18 |
|      3 |  630 |  656 |
|      4 | 3012 | 3273 |
|      5 |  372 |  386 |
|      6 |  142 |  164 |
|      7 | 1072 | 1145 |
|      8 |  432 |  501 |
|      9 |  216 |  235 |
|     10 | 1698 | 1778 |
|     11 | 1570 | 1541 |
|     12 |  502 |  486 |
|     13 |  497 |  516 |
|     14 |  723 |  786 |
|     15 |  712 |  740 |
|     16 |  335 |  352 |
|     17 |  267 |  299 |
|     18 | 1658 | 1714 |
|     19 |  231 |  255 |
|     20 |   15 |   24 |
|     21 |  172 |  181 |
|     22 |  234 |  243 |
|     23 |  312 |  338 |
|     24 |  351 |  371 |
|     25 |  252 |  281 |
|     26 |  194 |  210 |
|     27 |  149 |  166 |
|     28 |  173 |  189 |
|     29 |  318 |  344 |
|     30 |  204 |  227 |
|     31 |   52 |   63 |
|     32 |  188 |  174 |
|     33 |   97 |  122 |
|     34 |  218 |  242 |
|     35 |   47 |   51 |
|     36 |  838 |  879 |




### 简单随机抽样的理论方差


```python
V_Y_SRS = N**2 * ((1 - n / N) / n * df1["Yi"].var())
print(V_Y_SRS)
```

    75537056.42857143


$$V(\hat Y_{{SRS}}) = 75537056$$

上面的结果与课件中的一致。

![课件答案](index-image/课件答案.png)

但是，当我不调用`pandas`中的`.var()`方法，而是自己实现总体方差的计算时，发现结果与课件中的不一致。

![总体方差计算公式](index-image/总体方差计算公式.png)


```python
# 手动计算总体方差
sum_of_squre = 0
mean = df1["Yi"].mean()
for i in range(1, N + 1):
    sum_of_squre += (df1.loc[i, "Yi"] - mean) ** 2
S_square_manual = sum_of_squre / (N)
# 打印结果
print(S_square_manual)
```

    407993.3603395062


$$V_{{manual}}(Y_i) = {{ 407993 }}$$


```python
V_Y_SRS_manual = N**2 * ((1 - n / N) / n * S_square_manual)
# 打印结果
print(V_Y_SRS_manual)
```

    73438804.86111112


$$V_{{manual}}(\hat Y_{{SRS}}) = 73438805$$

这个结果与课件中的不一致。

检查发现，是因为`pandas`中的`var()`方法计算的是样本方差，分母除的是`N-1`。


```python
# 用 pandas 计算方差，默认是样本方差
S_square_pandas = df1["Yi"].var()
# 打印结果
print(S_square_pandas)
```

    419650.3134920635


$$V_{{pandas}}(\hat Y_{{SRS}}) = 419650$$


```python
V_Y_SRS_pandas = N**2 * ((1 - n / N) / n * S_square_pandas)
# 打印结果
print(V_Y_SRS_pandas)
```

    75537056.42857143


$$V_{{pandas}}(\hat Y_{{SRS}}) = 75537056$$'

如果我们将`pandas`中的`var()`方法改为计算总体方差，即传入`ddof=0`，使得分母除的是`N`，则结果与手动计算的一致，但与课件中的不一致。


```python
# 用 pandas 计算方差，且指定为总体方差
S_square_pandas = df1["Yi"].var(ddof=0)
# 打印结果
print(S_square_pandas)
```

    407993.3603395062


$$V_{{pandas}}(\hat Y_{{SRS}}) = 407993$$


```python
V_Y_SRS_pandas = N**2 * ((1 - n / N) / n * S_square_pandas)
# 打印结果
print(V_Y_SRS_pandas)
```

    73438804.86111112


$$V_{{pandas}}(\hat Y_{{SRS}}) = 73438805$$

在计算理论方差时，若我们的数据是总体数据，则应该使用总体方差，而不是样本方差。