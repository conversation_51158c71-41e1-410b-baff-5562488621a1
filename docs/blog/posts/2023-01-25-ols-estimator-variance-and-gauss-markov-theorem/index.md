---
title: 普通最小二乘估计的方差与高斯 - 马尔可夫定理
authors:
  - <PERSON>
date: "2023-01-25"
slug: ols-estimator-variance-and-gauss-markov-theorem
categories:
  - 统计
  - 机器学习
  - 量化研究
tags:
  - 统计
  - 机器学习
  - 量化研究
links:
  - blog/posts/2023-01-23-ols-estimator/index.md
  - blog/posts/2023-01-24-ols-estimator-unbiasedness-and-consistency/index.md
  - blog/posts/2023-01-30-ols-estimator-assumptions/index.md
---

# 普通最小二乘估计的方差与高斯 - 马尔可夫定理

本文计算了普通最小二乘估计的方差，并证明了高斯 - 马尔可夫定理。

普通最小二乘估计的方差：

$$
\begin{aligned}
 \operatorname{Var}(\underbrace{\beta^{O L S}}_ {(K+1) \times 1} \mid X)&=\sigma^2 \underbrace{\left(X^{\prime} X\right)^{-1}} _ {(K+1) \times (K+1)}
\end{aligned}
$$

!!! quote "高斯 - 马尔可夫定理（Gauss-Markov Theorem）"

	在线性回归模型中，如果线性模型满足高斯马尔可夫假定，则回归系数的最佳线性无偏估计（BLUE, Best Linear Unbiased Estimator）就是普通最小二乘法估计。

<!-- more -->

## 普通最小二乘估计的方差

按照方差的定义，计算最小二乘估计的方差。注意，最小二乘估计是一个列向量，它的方差是一个矩阵（即协方差矩阵）。

$$
\begin{aligned}
\operatorname{Var}(\underbrace{\beta^{O L S}}_{(K+1) \times 1} \mid X)&=E\left[\left(\beta^{O L S}-E\left[\beta^{O L S}\right]\right)\left(\beta^{O L S}-E\left[\beta^{O L S}\right]\right)^{\prime} \mid X\right] \\
& =E\left[\left(\left(X^{\prime} X\right)^{-1} X^{\prime} u\right)\left(\left(X^{\prime} X\right) ^ {-1} X ^ {\prime} u\right) ^ {\prime} \mid X\right] \\
& =E\left[\left(\left(X ^ { \prime} X\right) ^ {-1} X ^ {\prime} u\right) u^{\prime} X\left(\left(X^ {\prime} X\right) ^ {-1}\right) ^ {\prime} \mid X\right] \\
& =E\left[\left(X ^ {\prime} X\right) ^ {-1} X ^ {\prime} u u^{\prime} X\left(\left(X ^ {\prime} X\right) ^ {-1}\right) ^ {\prime} \mid X\right] \\
& =E\left[\left(X ^ {\prime} X\right) ^ {-1} X^{\prime} u u ^ {\prime} X\left(X ^ {\prime} X\right) ^ {-1} \mid X\right] \\
& =\left(X ^ {\prime} X\right)^{-1} X ^ {\prime} {\color{red}{E\left[u u ^ {\prime} \mid X\right]}} X\left(X ^ {\prime} X\right) ^ {-1} \\
& =\left(X ^ {\prime} X\right)^{-1} X ^ {\prime}{\color{red}{\left(\sigma^2 I\right)}} X\left(X ^ {\prime} X\right) ^ {-1} \\
& =\sigma^2 \left(X ^ {\prime} X\right)^ {-1} X ^ {\prime} X\left(X ^ {\prime} X\right) ^ {-1} \\
& =\sigma^2 \underbrace{\left(X^{\prime} X\right)^{-1}} _ {(K+1) \times (K+1)}
\end{aligned}\tag{1}\label{1}
$$

公式$\eqref{1}$中的红色部分用到了 ==误差项之间同方差且互不相关== 的假设。

## 高斯 - 马尔可夫定理

!!! quote "高斯 - 马尔可夫定理（Gauss-Markov Theorem）"

	在线性回归模型中，如果线性模型满足高斯马尔可夫假定，则回归系数的最佳线性无偏估计（BLUE, Best Linear Unbiased Estimator）就是普通最小二乘法估计。

### 高斯 - 马尔可夫的条件

高斯 - 马尔可夫定理的两个条件为：

1. 误差项的条件均值为 0：

   $$
   E(\mu \mid X)=0, \forall X
   $$

2. 误差项之间同方差且互不相关，即误差项的协方差矩阵是一个对角矩阵，且对角线元素相同：

   $$
   E\left[u u ^ {\prime} \mid X\right] = \sigma^2 I
   $$

{==

值得注意的是，这里<u>不需要</u>假定误差满足同分布或正态分布。

==}

换句话说，如果误差项之间来自不同的分布，但各个分布的方差相同，也是满足高斯 - 马尔可夫定理的条件的。因为我们在下面的推导中只需要用到$E\left[u u ^ {\prime} \mid X\right] = \sigma^2 I$这个性质。

### 高斯 - 马尔可夫定理的证明

#### 利用估计量的线性，将估计量表达成 $\widehat{\beta} \equiv \left( A ^ {O L S} + D \right) Y$

由于最小二乘估计是线性估计，即估计量是因变量$Y$的线性函数，因此我们可以将$\beta^{O L S}$记为$A^{O L S} Y$。

$$
\beta^{O L S}=\underbrace{\left(X^{\prime} X\right)^{-1} X^{\prime} }_{(K+1) \times T} Y \equiv A^{O L S} Y
$$

若另有一个线性估计量，则它也可以表达为：

$$
\widehat{\beta} \equiv A Y \equiv\left(A^{O L S}+D\right) Y \text { where } D=A-A^{O L S}
$$

#### 利用估计量的无偏性、误差项的条件均值为 0，推导出$D X=0$

$$
\begin{aligned}
E[\hat{\beta} \mid X] & =\beta \\
E\left[\left(A^{O L S}+D\right) Y \mid X\right] & =E\left[A^{O L S} Y+D Y \mid X\right] \\
& =E\left[\beta^{O L S}+D Y \mid X\right] \\
& =\beta+\underbrace{E[D Y \mid X]} _ {=0} \\
E[D Y \mid X] & =E[D(X \beta+u) \mid X] \\
& =D X \beta+{\color{red}{D E[u \mid X]}} \\
& =D X \beta \\
& =0 \\
\Rightarrow D X& =0
\end{aligned}\tag{2}\label{2}
$$

公式$\eqref{2}$中的红色部分用到了 ==误差项的条件均值为 0== 的假设。

#### 利用误差项之间同方差且互不相关，推导出 $\operatorname{Var}[\widehat{\beta} \mid X]\geq \operatorname{Var}\left[\beta^{O L S} \mid X\right]$

由公式$\eqref{2}$，我们可以将$\hat\beta$中包含$X$的部分删去，即化简为：

$$
\begin{aligned}
\widehat{\beta} & =A^{O L S} Y+D Y \\
& =\beta^{O L S}+D(X \beta+u) \\
& =\beta^{O L S}+D u
\end{aligned}
$$

再由方差计算公式，将协方差项化为 0，可证明$\operatorname{Var}[\widehat{\beta} \mid X]\geq \operatorname{Var}\left[\beta^{O L S} \mid X\right]$，

$$
\begin{aligned}
\operatorname{Var}[\widehat{\beta} \mid X] & =\operatorname{Var}\left[\beta^{O L S}+D \mu \mid X\right] \\
& =\operatorname{Var}\left[\beta^{O L S} \mid X\right]+\operatorname{Var}[D \mu \mid X]+\underbrace{\operatorname{Cov}\left[\beta^{O L S}, D \mu \mid X\right]}_{=0} \\
& =\operatorname{Var}\left[\beta^{O L S} \mid X\right]+\operatorname{Var}[D \mu \mid X] \\
& \geq \operatorname{Var}\left[\beta^{O L S} \mid X\right]
\end{aligned}
$$

其中

$$
\begin{aligned}
\operatorname{Cov}\left[\beta^{O L S}, D u \mid X\right] & =\operatorname{Cov}\left[\beta+\left(X^{\prime} X\right)^{-1} X^{\prime} u, D u \mid X\right] \\\
& =\operatorname{Cov}\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u, D u \mid X\right] \\\
& =E\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u(D u)^{\prime} \mid X\right]+E\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u\mid X\right]  E\left[(D u)^{\prime} \mid X\right]\\\
& =E\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u(D u)^{\prime} \mid X\right]+\left(X^{\prime} X\right)^{-1} X^{\prime} {\color{red}{E\left[u\mid X\right]}}  D^ \prime {\color{red}{E\left[u^{\prime} \mid X\right]}}\\\
& =E\left[\left(X^{\prime} X\right)^{-1} X^{\prime} u(D u)^{\prime} \mid X\right]+{\color{red}{0}} \\\
& =\left(X^{\prime} X\right)^{-1} X^{\prime} {\color{blue}{E\left[u u^{\prime} \mid X\right]}} D^{\prime} \\\
& ={\color{blue}{\sigma^2}} \left(X^{\prime} X\right)^{-1} X^{\prime} D^{\prime} \\\
& =\sigma^2 \left(X^{\prime} X\right) ^{-1}(D X) ^{\prime} \\\
& =0
\end{aligned}\tag{3}\label{3}
$$

公式$\eqref{3}$中的红色部分用到了 ==误差项的条件均值为 0== 的假设，蓝色部分用到了 ==利用误差项之间同方差且互不相关== 的假设。
